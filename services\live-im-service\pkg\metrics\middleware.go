/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package metrics

import (
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/sirupsen/logrus"
)

// HTTPMiddleware provides Gin middleware for HTTP metrics collection
type HTTPMiddleware struct {
	collector *Collector
	logger    *logrus.Logger
}

// NewHTTPMiddleware creates a new HTTP metrics middleware
func NewHTTPMiddleware(collector *Collector, logger *logrus.Logger) *HTTPMiddleware {
	return &HTTPMiddleware{
		collector: collector,
		logger:    logger,
	}
}

// Middleware returns a Gin middleware function for metrics collection
func (m *HTTPMiddleware) Middleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		method := c.Request.Method

		// Record in-flight request
		m.collector.RecordHTTPRequestInFlight(method, path, 1)
		defer m.collector.RecordHTTPRequestInFlight(method, path, -1)

		// Get request size
		requestSize := float64(c.Request.ContentLength)
		if requestSize < 0 {
			requestSize = 0
		}

		// Process request
		c.Next()

		// Calculate duration
		duration := time.Since(start)

		// Get response size
		responseSize := float64(c.Writer.Size())
		if responseSize < 0 {
			responseSize = 0
		}

		// Get status code
		statusCode := strconv.Itoa(c.Writer.Status())

		// Record metrics
		m.collector.RecordHTTPRequest(method, path, statusCode, duration, requestSize, responseSize)

		// Log slow requests
		if duration > 1*time.Second {
			m.logger.WithFields(logrus.Fields{
				"method":      method,
				"path":        path,
				"status_code": statusCode,
				"duration_ms": duration.Milliseconds(),
				"request_size": requestSize,
				"response_size": responseSize,
			}).Warn("Slow HTTP request detected")
		}
	}
}

// PrometheusHandler returns a Gin handler for Prometheus metrics endpoint
func (m *HTTPMiddleware) PrometheusHandler() gin.HandlerFunc {
	handler := promhttp.Handler()
	return gin.WrapH(handler)
}

// WebSocketMetrics provides metrics collection for WebSocket connections
type WebSocketMetrics struct {
	collector *Collector
	logger    *logrus.Logger
}

// NewWebSocketMetrics creates a new WebSocket metrics collector
func NewWebSocketMetrics(collector *Collector, logger *logrus.Logger) *WebSocketMetrics {
	return &WebSocketMetrics{
		collector: collector,
		logger:    logger,
	}
}

// RecordConnection records a WebSocket connection event
func (w *WebSocketMetrics) RecordConnection(status string) {
	w.collector.RecordWebSocketConnection(status)
}

// UpdateActiveConnections updates the count of active WebSocket connections
func (w *WebSocketMetrics) UpdateActiveConnections(roomID string, count int) {
	w.collector.UpdateWebSocketConnectionsActive(roomID, float64(count))
}

// RecordMessage records a WebSocket message
func (w *WebSocketMetrics) RecordMessage(messageType, direction string, size int) {
	w.collector.RecordWebSocketMessage(messageType, direction, float64(size))
}

// RecordConnectionDuration records the duration of a WebSocket connection
func (w *WebSocketMetrics) RecordConnectionDuration(roomID string, duration time.Duration) {
	w.collector.RecordWebSocketConnectionDuration(roomID, duration)
}

// HubMetrics provides metrics collection for Hub operations
type HubMetrics struct {
	collector *Collector
	logger    *logrus.Logger
}

// NewHubMetrics creates a new Hub metrics collector
func NewHubMetrics(collector *Collector, logger *logrus.Logger) *HubMetrics {
	return &HubMetrics{
		collector: collector,
		logger:    logger,
	}
}

// UpdateStats updates Hub statistics
func (h *HubMetrics) UpdateStats(clientsConnected, activeRooms int, messagesPerSecond, broadcastsPerSecond float64) {
	h.collector.UpdateHubStats(clientsConnected, activeRooms, messagesPerSecond, broadcastsPerSecond)
}

// RecordMessage records a Hub message
func (h *HubMetrics) RecordMessage(messageType string) {
	h.collector.RecordHubMessage(messageType)
}

// RecordBroadcast records a Hub broadcast
func (h *HubMetrics) RecordBroadcast(roomID string) {
	h.collector.RecordHubBroadcast(roomID)
}

// RoomMetrics provides metrics collection for room operations
type RoomMetrics struct {
	collector *Collector
	logger    *logrus.Logger
}

// NewRoomMetrics creates a new room metrics collector
func NewRoomMetrics(collector *Collector, logger *logrus.Logger) *RoomMetrics {
	return &RoomMetrics{
		collector: collector,
		logger:    logger,
	}
}

// UpdateMembers updates room member count
func (r *RoomMetrics) UpdateMembers(roomID string, count int) {
	r.collector.UpdateRoomMembers(roomID, float64(count))
}

// RecordMessage records a room message
func (r *RoomMetrics) RecordMessage(roomID, messageType string) {
	r.collector.RecordRoomMessage(roomID, messageType)
}

// RecordJoin records a room join
func (r *RoomMetrics) RecordJoin(roomID string) {
	r.collector.RecordRoomJoin(roomID)
}

// RecordLeave records a room leave
func (r *RoomMetrics) RecordLeave(roomID string) {
	r.collector.RecordRoomLeave(roomID)
}

// MessageMetrics provides metrics collection for message processing
type MessageMetrics struct {
	collector *Collector
	logger    *logrus.Logger
}

// NewMessageMetrics creates a new message metrics collector
func NewMessageMetrics(collector *Collector, logger *logrus.Logger) *MessageMetrics {
	return &MessageMetrics{
		collector: collector,
		logger:    logger,
	}
}

// RecordProcessing records message processing duration
func (m *MessageMetrics) RecordProcessing(messageType, status string, duration time.Duration) {
	m.collector.RecordMessageProcessing(messageType, status, duration)
}

// RecordValidation records message validation result
func (m *MessageMetrics) RecordValidation(messageType, result string) {
	m.collector.RecordMessageValidation(messageType, result)
}

// RecordDropped records a dropped message
func (m *MessageMetrics) RecordDropped(reason string) {
	m.collector.RecordMessageDropped(reason)
}

// RateLimitMetrics provides metrics collection for rate limiting
type RateLimitMetrics struct {
	collector *Collector
	logger    *logrus.Logger
}

// NewRateLimitMetrics creates a new rate limit metrics collector
func NewRateLimitMetrics(collector *Collector, logger *logrus.Logger) *RateLimitMetrics {
	return &RateLimitMetrics{
		collector: collector,
		logger:    logger,
	}
}

// RecordHit records a rate limit hit
func (r *RateLimitMetrics) RecordHit(userID, action string) {
	r.collector.RecordRateLimitHit(userID, action)
}

// RecordAllowed records an allowed rate limit request
func (r *RateLimitMetrics) RecordAllowed(action string) {
	r.collector.RecordRateLimitAllowed(action)
}

// ExternalServiceMetrics provides metrics collection for external service calls
type ExternalServiceMetrics struct {
	collector *Collector
	logger    *logrus.Logger
}

// NewExternalServiceMetrics creates a new external service metrics collector
func NewExternalServiceMetrics(collector *Collector, logger *logrus.Logger) *ExternalServiceMetrics {
	return &ExternalServiceMetrics{
		collector: collector,
		logger:    logger,
	}
}

// RecordRedisOperation records a Redis operation
func (e *ExternalServiceMetrics) RecordRedisOperation(operation, status string, duration time.Duration) {
	e.collector.RecordRedisOperation(operation, status, duration)
}

// RecordServiceCall records an external service call
func (e *ExternalServiceMetrics) RecordServiceCall(service, method, status string, duration time.Duration) {
	e.collector.RecordExternalServiceCall(service, method, status, duration)
}

// SystemMetrics provides metrics collection for system-level metrics
type SystemMetrics struct {
	collector *Collector
	logger    *logrus.Logger
	startTime time.Time
}

// NewSystemMetrics creates a new system metrics collector
func NewSystemMetrics(collector *Collector, logger *logrus.Logger, startTime time.Time) *SystemMetrics {
	return &SystemMetrics{
		collector: collector,
		logger:    logger,
		startTime: startTime,
	}
}

// UpdateUptime updates system uptime
func (s *SystemMetrics) UpdateUptime() {
	uptime := time.Since(s.startTime)
	s.collector.UpdateSystemUptime(uptime)
}

// UpdateMemoryUsage updates memory usage
func (s *SystemMetrics) UpdateMemoryUsage(bytes int64) {
	s.collector.UpdateMemoryUsage(float64(bytes))
}

// UpdateGoroutines updates active goroutines count
func (s *SystemMetrics) UpdateGoroutines(count int) {
	s.collector.UpdateGoroutinesActive(float64(count))
}

// MetricsManager provides centralized metrics management
type MetricsManager struct {
	collector           *Collector
	httpMiddleware      *HTTPMiddleware
	webSocketMetrics    *WebSocketMetrics
	hubMetrics          *HubMetrics
	roomMetrics         *RoomMetrics
	messageMetrics      *MessageMetrics
	rateLimitMetrics    *RateLimitMetrics
	externalServiceMetrics *ExternalServiceMetrics
	systemMetrics       *SystemMetrics
	logger              *logrus.Logger
}

// NewMetricsManager creates a new metrics manager
func NewMetricsManager(logger *logrus.Logger, startTime time.Time) *MetricsManager {
	collector := NewCollector(logger)
	
	return &MetricsManager{
		collector:           collector,
		httpMiddleware:      NewHTTPMiddleware(collector, logger),
		webSocketMetrics:    NewWebSocketMetrics(collector, logger),
		hubMetrics:          NewHubMetrics(collector, logger),
		roomMetrics:         NewRoomMetrics(collector, logger),
		messageMetrics:      NewMessageMetrics(collector, logger),
		rateLimitMetrics:    NewRateLimitMetrics(collector, logger),
		externalServiceMetrics: NewExternalServiceMetrics(collector, logger),
		systemMetrics:       NewSystemMetrics(collector, logger, startTime),
		logger:              logger,
	}
}

// GetHTTPMiddleware returns the HTTP middleware
func (m *MetricsManager) GetHTTPMiddleware() *HTTPMiddleware {
	return m.httpMiddleware
}

// GetWebSocketMetrics returns the WebSocket metrics collector
func (m *MetricsManager) GetWebSocketMetrics() *WebSocketMetrics {
	return m.webSocketMetrics
}

// GetHubMetrics returns the Hub metrics collector
func (m *MetricsManager) GetHubMetrics() *HubMetrics {
	return m.hubMetrics
}

// GetRoomMetrics returns the room metrics collector
func (m *MetricsManager) GetRoomMetrics() *RoomMetrics {
	return m.roomMetrics
}

// GetMessageMetrics returns the message metrics collector
func (m *MetricsManager) GetMessageMetrics() *MessageMetrics {
	return m.messageMetrics
}

// GetRateLimitMetrics returns the rate limit metrics collector
func (m *MetricsManager) GetRateLimitMetrics() *RateLimitMetrics {
	return m.rateLimitMetrics
}

// GetExternalServiceMetrics returns the external service metrics collector
func (m *MetricsManager) GetExternalServiceMetrics() *ExternalServiceMetrics {
	return m.externalServiceMetrics
}

// GetSystemMetrics returns the system metrics collector
func (m *MetricsManager) GetSystemMetrics() *SystemMetrics {
	return m.systemMetrics
}

// GetCollector returns the underlying Prometheus collector
func (m *MetricsManager) GetCollector() *Collector {
	return m.collector
}
