/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package serializer

import (
	"fmt"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"cina.club/services/live-im-service/internal/domain/model"
)

func TestNewProtobufSerializer(t *testing.T) {
	serializer := NewProtobufSerializer()
	assert.NotNil(t, serializer)
}

func TestProtobufSerializer_SerializeDeserialize_BasicMessage(t *testing.T) {
	serializer := NewProtobufSerializer()

	// Create a basic message
	originalMsg := model.Message{
		MessageID: uuid.New().String(),
		Type:      model.MessageTypeBarrage,
		Timestamp: time.Now().UTC().Truncate(time.Second), // Truncate for protobuf precision
		Token:     "test-token",
		RoomID:    "test-room",
		Content:   "Hello World",
		Count:     1,
		Success:   true,
		Data:      map[string]interface{}{"key": "value"},
	}

	// Serialize
	data, err := serializer.SerializeMessage(originalMsg)
	require.NoError(t, err)
	assert.NotEmpty(t, data)

	// Deserialize
	deserializedMsg, err := serializer.DeserializeMessage(data)
	require.NoError(t, err)

	// Verify basic fields
	assert.Equal(t, originalMsg.MessageID, deserializedMsg.MessageID)
	assert.Equal(t, originalMsg.Type, deserializedMsg.Type)
	assert.Equal(t, originalMsg.Timestamp, deserializedMsg.Timestamp)
	assert.Equal(t, originalMsg.Token, deserializedMsg.Token)
	assert.Equal(t, originalMsg.RoomID, deserializedMsg.RoomID)
	assert.Equal(t, originalMsg.Content, deserializedMsg.Content)
	assert.Equal(t, originalMsg.Count, deserializedMsg.Count)
	assert.Equal(t, originalMsg.Success, deserializedMsg.Success)
	assert.Equal(t, originalMsg.Data, deserializedMsg.Data)
}

func TestProtobufSerializer_SerializeDeserialize_AllMessageTypes(t *testing.T) {
	serializer := NewProtobufSerializer()

	messageTypes := []model.MessageType{
		model.MessageTypeAuth,
		model.MessageTypeAuthResult,
		model.MessageTypeBarrage,
		model.MessageTypeLike,
		model.MessageTypeGift,
		model.MessageTypePing,
		model.MessageTypePong,
		model.MessageTypeError,
		model.MessageTypeRateLimited,
		model.MessageTypeNewBarrage,
		model.MessageTypeLikeBurst,
		model.MessageTypeNewGift,
		model.MessageTypeUserJoin,
		model.MessageTypeUserLeave,
		model.MessageTypeRoomUpdate,
	}

	for _, msgType := range messageTypes {
		t.Run(string(msgType), func(t *testing.T) {
			originalMsg := model.Message{
				MessageID: uuid.New().String(),
				Type:      msgType,
				Timestamp: time.Now().UTC().Truncate(time.Second),
				Content:   "Test message for " + string(msgType),
			}

			// Serialize
			data, err := serializer.SerializeMessage(originalMsg)
			require.NoError(t, err)

			// Deserialize
			deserializedMsg, err := serializer.DeserializeMessage(data)
			require.NoError(t, err)

			assert.Equal(t, originalMsg.Type, deserializedMsg.Type)
			assert.Equal(t, originalMsg.Content, deserializedMsg.Content)
		})
	}
}

func TestProtobufSerializer_SerializeDeserialize_WithUser(t *testing.T) {
	serializer := NewProtobufSerializer()

	userID := uuid.New()
	originalMsg := model.Message{
		MessageID: uuid.New().String(),
		Type:      model.MessageTypeBarrage,
		Timestamp: time.Now().UTC().Truncate(time.Second),
		Content:   "Message with user",
		FromUser: &model.User{
			UserID:   userID,
			Username: "testuser",
			Avatar:   "avatar.jpg",
			Role:     model.RoleViewer,
			Level:    5,
			VIPLevel: 2,
		},
	}

	// Serialize
	data, err := serializer.SerializeMessage(originalMsg)
	require.NoError(t, err)

	// Deserialize
	deserializedMsg, err := serializer.DeserializeMessage(data)
	require.NoError(t, err)

	// Verify user information
	require.NotNil(t, deserializedMsg.FromUser)
	assert.Equal(t, originalMsg.FromUser.UserID, deserializedMsg.FromUser.UserID)
	assert.Equal(t, originalMsg.FromUser.Username, deserializedMsg.FromUser.Username)
	assert.Equal(t, originalMsg.FromUser.Avatar, deserializedMsg.FromUser.Avatar)
	assert.Equal(t, originalMsg.FromUser.Role, deserializedMsg.FromUser.Role)
	assert.Equal(t, originalMsg.FromUser.Level, deserializedMsg.FromUser.Level)
	assert.Equal(t, originalMsg.FromUser.VIPLevel, deserializedMsg.FromUser.VIPLevel)
}

func TestProtobufSerializer_SerializeDeserialize_WithRoomInfo(t *testing.T) {
	serializer := NewProtobufSerializer()

	originalMsg := model.Message{
		MessageID: uuid.New().String(),
		Type:      model.MessageTypeRoomUpdate,
		Timestamp: time.Now().UTC().Truncate(time.Second),
		RoomInfo: &model.RoomInfo{
			RoomID:      "test-room",
			Title:       "Test Room",
			Description: "A test room",
			Category:    "Gaming",
			Tags:        []string{"live", "gaming"},
			Status:      "live",
			OnlineCount: 100,
			StartTime:   time.Now().UTC().Truncate(time.Second),
		},
	}

	// Serialize
	data, err := serializer.SerializeMessage(originalMsg)
	require.NoError(t, err)

	// Deserialize
	deserializedMsg, err := serializer.DeserializeMessage(data)
	require.NoError(t, err)

	// Verify room information
	require.NotNil(t, deserializedMsg.RoomInfo)
	assert.Equal(t, originalMsg.RoomInfo.RoomID, deserializedMsg.RoomInfo.RoomID)
	assert.Equal(t, originalMsg.RoomInfo.Title, deserializedMsg.RoomInfo.Title)
	assert.Equal(t, originalMsg.RoomInfo.Description, deserializedMsg.RoomInfo.Description)
	assert.Equal(t, originalMsg.RoomInfo.Category, deserializedMsg.RoomInfo.Category)
	assert.Equal(t, originalMsg.RoomInfo.Status, deserializedMsg.RoomInfo.Status)
	assert.Equal(t, originalMsg.RoomInfo.OnlineCount, deserializedMsg.RoomInfo.OnlineCount)
	assert.Equal(t, originalMsg.RoomInfo.StartTime, deserializedMsg.RoomInfo.StartTime)
}

func TestProtobufSerializer_SerializeDeserialize_WithOnlineUsers(t *testing.T) {
	serializer := NewProtobufSerializer()

	user1 := &model.User{
		UserID:   uuid.New(),
		Username: "user1",
		Avatar:   "avatar1.jpg",
		Role:     model.RoleViewer,
		Level:    1,
		VIPLevel: 0,
	}

	user2 := &model.User{
		UserID:   uuid.New(),
		Username: "user2",
		Avatar:   "avatar2.jpg",
		Role:     model.RoleModerator,
		Level:    10,
		VIPLevel: 5,
	}

	originalMsg := model.Message{
		MessageID:  uuid.New().String(),
		Type:       model.MessageTypeRoomUpdate,
		Timestamp:  time.Now().UTC().Truncate(time.Second),
		OnlineList: []*model.User{user1, user2},
	}

	// Serialize
	data, err := serializer.SerializeMessage(originalMsg)
	require.NoError(t, err)

	// Deserialize
	deserializedMsg, err := serializer.DeserializeMessage(data)
	require.NoError(t, err)

	// Verify online users
	require.Len(t, deserializedMsg.OnlineList, 2)

	// Check first user
	assert.Equal(t, user1.UserID, deserializedMsg.OnlineList[0].UserID)
	assert.Equal(t, user1.Username, deserializedMsg.OnlineList[0].Username)
	assert.Equal(t, user1.Role, deserializedMsg.OnlineList[0].Role)

	// Check second user
	assert.Equal(t, user2.UserID, deserializedMsg.OnlineList[1].UserID)
	assert.Equal(t, user2.Username, deserializedMsg.OnlineList[1].Username)
	assert.Equal(t, user2.Role, deserializedMsg.OnlineList[1].Role)
}

func TestProtobufSerializer_SerializeDeserialize_GiftMessage(t *testing.T) {
	serializer := NewProtobufSerializer()

	originalMsg := model.Message{
		MessageID: uuid.New().String(),
		Type:      model.MessageTypeGift,
		Timestamp: time.Now().UTC().Truncate(time.Second),
		GiftID:    "gift-123",
		ToUserID:  "recipient-456",
		Count:     5,
		Content:   "Sending 5 roses",
		FromUser: &model.User{
			UserID:   uuid.New(),
			Username: "sender",
			Role:     model.RoleViewer,
		},
	}

	// Serialize
	data, err := serializer.SerializeMessage(originalMsg)
	require.NoError(t, err)

	// Deserialize
	deserializedMsg, err := serializer.DeserializeMessage(data)
	require.NoError(t, err)

	// Verify gift-specific fields
	assert.Equal(t, originalMsg.GiftID, deserializedMsg.GiftID)
	assert.Equal(t, originalMsg.ToUserID, deserializedMsg.ToUserID)
	assert.Equal(t, originalMsg.Count, deserializedMsg.Count)
	assert.Equal(t, originalMsg.Content, deserializedMsg.Content)
}

func TestProtobufSerializer_SerializeDeserialize_ErrorMessage(t *testing.T) {
	serializer := NewProtobufSerializer()

	originalMsg := model.Message{
		MessageID: uuid.New().String(),
		Type:      model.MessageTypeError,
		Timestamp: time.Now().UTC().Truncate(time.Second),
		Success:   false,
		ErrorMsg:  "Authentication failed",
		Content:   "Invalid token provided",
	}

	// Serialize
	data, err := serializer.SerializeMessage(originalMsg)
	require.NoError(t, err)

	// Deserialize
	deserializedMsg, err := serializer.DeserializeMessage(data)
	require.NoError(t, err)

	// Verify error-specific fields
	assert.Equal(t, originalMsg.Success, deserializedMsg.Success)
	assert.Equal(t, originalMsg.ErrorMsg, deserializedMsg.ErrorMsg)
	assert.Equal(t, originalMsg.Content, deserializedMsg.Content)
}

func TestProtobufSerializer_DeserializeMessage_InvalidData(t *testing.T) {
	serializer := NewProtobufSerializer()

	// Test with invalid protobuf data
	invalidData := []byte("invalid protobuf data")

	_, err := serializer.DeserializeMessage(invalidData)
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to unmarshal protobuf")
}

func TestProtobufSerializer_SerializeDeserialize_EmptyMessage(t *testing.T) {
	serializer := NewProtobufSerializer()

	// Create an empty message
	originalMsg := model.Message{}

	// Serialize
	data, err := serializer.SerializeMessage(originalMsg)
	require.NoError(t, err)

	// Deserialize
	deserializedMsg, err := serializer.DeserializeMessage(data)
	require.NoError(t, err)

	// Verify empty message handling
	assert.Equal(t, originalMsg.MessageID, deserializedMsg.MessageID)
	assert.Equal(t, originalMsg.Type, deserializedMsg.Type)
	assert.Nil(t, deserializedMsg.FromUser)
	assert.Nil(t, deserializedMsg.RoomInfo)
	assert.Nil(t, deserializedMsg.OnlineList)
}

func TestProtobufSerializer_SerializeDeserialize_ComplexMetadata(t *testing.T) {
	serializer := NewProtobufSerializer()

	// Create message with complex metadata
	originalMsg := model.Message{
		MessageID: uuid.New().String(),
		Type:      model.MessageTypeBarrage,
		Timestamp: time.Now().UTC().Truncate(time.Second),
		Content:   "Message with complex metadata",
		Data: map[string]interface{}{
			"string_value":  "test",
			"number_value":  42,
			"boolean_value": true,
			"nested_object": map[string]interface{}{
				"inner_key": "inner_value",
			},
		},
	}

	// Serialize
	data, err := serializer.SerializeMessage(originalMsg)
	require.NoError(t, err)

	// Deserialize
	deserializedMsg, err := serializer.DeserializeMessage(data)
	require.NoError(t, err)

	// Verify metadata (note: complex nested objects may be converted to strings)
	assert.NotNil(t, deserializedMsg.Data)
	assert.Contains(t, deserializedMsg.Data, "string_value")
}

func TestProtobufSerializer_SerializeDeserialize_UnicodeContent(t *testing.T) {
	serializer := NewProtobufSerializer()

	// Create message with Unicode content
	originalMsg := model.Message{
		MessageID: uuid.New().String(),
		Type:      model.MessageTypeBarrage,
		Timestamp: time.Now().UTC().Truncate(time.Second),
		Content:   "Hello 世界! 🎉 Emoji and Chinese characters",
		FromUser: &model.User{
			UserID:   uuid.New(),
			Username: "用户名",
			Avatar:   "头像.jpg",
			Role:     model.RoleViewer,
		},
	}

	// Serialize
	data, err := serializer.SerializeMessage(originalMsg)
	require.NoError(t, err)

	// Deserialize
	deserializedMsg, err := serializer.DeserializeMessage(data)
	require.NoError(t, err)

	// Verify Unicode handling
	assert.Equal(t, originalMsg.Content, deserializedMsg.Content)
	assert.Equal(t, originalMsg.FromUser.Username, deserializedMsg.FromUser.Username)
	assert.Equal(t, originalMsg.FromUser.Avatar, deserializedMsg.FromUser.Avatar)
}

func TestProtobufSerializer_SerializeDeserialize_LargeMessage(t *testing.T) {
	serializer := NewProtobufSerializer()

	// Create a large message with many online users
	onlineUsers := make([]*model.User, 100)
	for i := 0; i < 100; i++ {
		onlineUsers[i] = &model.User{
			UserID:   uuid.New(),
			Username: fmt.Sprintf("user_%d", i),
			Avatar:   fmt.Sprintf("avatar_%d.jpg", i),
			Role:     model.RoleViewer,
			Level:    i % 20,
			VIPLevel: i % 5,
		}
	}

	originalMsg := model.Message{
		MessageID:  uuid.New().String(),
		Type:       model.MessageTypeRoomUpdate,
		Timestamp:  time.Now().UTC().Truncate(time.Second),
		Content:    "Large message with many users",
		OnlineList: onlineUsers,
	}

	// Serialize
	data, err := serializer.SerializeMessage(originalMsg)
	require.NoError(t, err)
	assert.True(t, len(data) > 1000) // Should be a reasonably large message

	// Deserialize
	deserializedMsg, err := serializer.DeserializeMessage(data)
	require.NoError(t, err)

	// Verify large data handling
	assert.Len(t, deserializedMsg.OnlineList, 100)
	for i, user := range deserializedMsg.OnlineList {
		assert.Equal(t, onlineUsers[i].Username, user.Username)
		assert.Equal(t, onlineUsers[i].Level, user.Level)
	}
}

func TestProtobufSerializer_MessageTypeConversion_UnknownType(t *testing.T) {
	serializer := NewProtobufSerializer()

	// Create message with unknown/invalid type
	originalMsg := model.Message{
		MessageID: uuid.New().String(),
		Type:      "unknown_message_type",
		Timestamp: time.Now().UTC().Truncate(time.Second),
		Content:   "Message with unknown type",
	}

	// Serialize (should handle unknown type gracefully)
	data, err := serializer.SerializeMessage(originalMsg)
	require.NoError(t, err)

	// Deserialize
	deserializedMsg, err := serializer.DeserializeMessage(data)
	require.NoError(t, err)

	// Unknown type should be converted to empty string or default
	assert.Equal(t, model.MessageType(""), deserializedMsg.Type)
}

func TestProtobufSerializer_SerializeDeserialize_AllUserRoles(t *testing.T) {
	serializer := NewProtobufSerializer()

	userRoles := []model.UserRole{
		model.RoleViewer,
		model.RoleStreamer,
		model.RoleModerator,
		model.RoleAdmin,
		model.RoleVIP,
	}

	for _, role := range userRoles {
		t.Run(string(role), func(t *testing.T) {
			originalMsg := model.Message{
				MessageID: uuid.New().String(),
				Type:      model.MessageTypeBarrage,
				Timestamp: time.Now().UTC().Truncate(time.Second),
				Content:   "Message from " + string(role),
				FromUser: &model.User{
					UserID:   uuid.New(),
					Username: "testuser",
					Role:     role,
					Level:    10,
					VIPLevel: 3,
				},
			}

			// Serialize
			data, err := serializer.SerializeMessage(originalMsg)
			require.NoError(t, err)

			// Deserialize
			deserializedMsg, err := serializer.DeserializeMessage(data)
			require.NoError(t, err)

			// Verify role conversion
			assert.Equal(t, role, deserializedMsg.FromUser.Role)
		})
	}
}

func TestProtobufSerializer_SerializeDeserialize_NilFields(t *testing.T) {
	serializer := NewProtobufSerializer()

	// Create message with nil fields
	originalMsg := model.Message{
		MessageID:  uuid.New().String(),
		Type:       model.MessageTypeBarrage,
		Timestamp:  time.Now().UTC().Truncate(time.Second),
		Content:    "Message with nil fields",
		FromUser:   nil,
		RoomInfo:   nil,
		OnlineList: nil,
		Data:       nil,
	}

	// Serialize
	data, err := serializer.SerializeMessage(originalMsg)
	require.NoError(t, err)

	// Deserialize
	deserializedMsg, err := serializer.DeserializeMessage(data)
	require.NoError(t, err)

	// Verify nil field handling
	assert.Nil(t, deserializedMsg.FromUser)
	assert.Nil(t, deserializedMsg.RoomInfo)
	assert.Nil(t, deserializedMsg.OnlineList)
	assert.Nil(t, deserializedMsg.Data)
}
