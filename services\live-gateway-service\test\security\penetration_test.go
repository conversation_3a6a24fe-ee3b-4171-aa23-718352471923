/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-01-27 19:00:00
Modified: 2025-01-27 19:00:00
*/

package security

import (
	"context"
	"crypto/tls"
	"fmt"
	"net/http"
	"strings"
	"testing"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	"cina.club/services/live-gateway-service/internal/application/port"
	"cina.club/services/live-gateway-service/internal/application/service"
	"cina.club/services/live-gateway-service/internal/domain/model"
)

// Helper functions for testing
func setupTestService(tb testing.TB) port.GatewayService {
	config := &service.GatewayConfig{
		DefaultTTL:           time.Hour,
		MaxConcurrentStreams: 1000,
		LoadBalanceStrategy:  "round_robin",
		EnableMetrics:        true,
		WebhookTimeout:       time.Second * 5,
		AuthTimeout:          time.Second * 3,
	}

	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Setup mock dependencies
	cache := setupRedisCache(tb)
	mediaAdapter := setupMediaAdapter(tb)
	loadBalancer := setupLoadBalancer(tb)
	liveAPIClient := setupLiveAPIClient(tb)
	eventPublisher := setupEventPublisher(tb)
	eventStore := setupEventStore(tb)

	return service.NewGatewayService(
		cache,
		mediaAdapter,
		loadBalancer,
		liveAPIClient,
		eventPublisher,
		eventStore,
		config,
		logger,
	)
}

func setupTestStream(tb testing.TB, svc port.GatewayService) string {
	// Create a unique stream key
	streamKey := "test-stream-" + uuid.New().String()

	// Create a push URL request
	req := &port.CreateStreamRequest{
		RoomID:    uuid.New(),
		UserID:    uuid.New(),
		Protocol:  model.StreamProtocolRTMP,
		Quality:   model.StreamQualityHigh,
		ClientIP:  "127.0.0.1",
		UserAgent: "test-agent",
	}

	// Request a push URL to initialize the stream
	_, err := svc.RequestPushURL(context.Background(), req)
	require.NoError(tb, err)

	return streamKey
}

// Mock setup functions
func setupRedisCache(tb testing.TB) port.CacheRepository {
	mr, err := miniredis.Run()
	require.NoError(tb, err)

	client := redis.NewClient(&redis.Options{
		Addr: mr.Addr(),
	})

	mockCache := &mockCacheRepository{client: client}

	// Set up mock expectations for GetStreamMapping
	testMapping := &model.StreamMapping{
		StreamKey:  "test-stream",
		RoomID:     uuid.New(),
		UserID:     uuid.New(),
		ServerNode: "test-node",
		CreatedAt:  time.Now(),
		ExpiresAt:  time.Now().Add(time.Hour),
	}
	mockCache.On("GetStreamMapping", mock.Anything, mock.AnythingOfType("string")).Return(testMapping, nil)
	mockCache.On("StoreStreamMapping", mock.Anything, mock.AnythingOfType("*model.StreamMapping")).Return(nil)

	return mockCache
}

func setupMediaAdapter(tb testing.TB) port.MediaServerAdapter {
	mockAdapter := &mockMediaServerAdapter{}

	// Set up mock expectations for media adapter
	mockAdapter.On("GetType").Return(model.MediaServerTypeSRS)
	mockAdapter.On("GeneratePushURL", mock.Anything, mock.AnythingOfType("*port.PushURLRequest")).Return(&model.PushURL{
		URLString: "rtmp://test-server/live/test-stream",
		StreamKey: "test-stream",
		TTL:       time.Hour,
	}, nil)
	mockAdapter.On("GeneratePlayURLs", mock.Anything, mock.AnythingOfType("*port.RequestPlayURLsRequest")).Return([]*model.PlayURL{
		{
			URLString: "http://test-server/live/test-stream.m3u8",
			Protocol:  model.PlayProtocolHLS,
			StreamKey: "test-stream",
			TTL:       time.Hour,
		},
	}, nil)
	mockAdapter.On("GetStreamInfo", mock.Anything, mock.AnythingOfType("string")).Return(&port.StreamInfo{
		StreamKey:  "test-stream",
		Status:     model.StreamStatusLive,
		Protocol:   model.StreamProtocolRTMP,
		Quality:    model.StreamQualityHigh,
		ServerNode: "test-node",
		Stats: &model.StreamStats{
			StreamKey:    "test-stream",
			IsActive:     true,
			VideoBitrate: 5000000,
			AudioBitrate: 128000,
			ViewerCount:  10,
			Duration:     300,
		},
		StartTime:    &time.Time{},
		LastSeenTime: &time.Time{},
	}, nil)
	mockAdapter.On("GetServerStats", mock.Anything, mock.AnythingOfType("string")).Return(&model.NodeLoad{
		NodeID:      "test-node",
		LoadScore:   0.5,
		CPUUsage:    0.6,
		MemoryUsage: 0.7,
		NetworkIn:   1000000,
		NetworkOut:  2000000,
		LastUpdated: time.Now(),
	}, nil)

	return mockAdapter
}

func setupLoadBalancer(tb testing.TB) port.LoadBalancer {
	mockLB := &mockLoadBalancer{}

	// Set up mock expectations
	testNode := &model.MediaNode{
		ID:         "test-node",
		Address:    "127.0.0.1",
		Port:       1935,
		ServerType: model.MediaServerTypeSRS,
		Status:     model.NodeStatusActive,
	}
	mockLB.On("SelectNodeByRequest", mock.Anything, mock.AnythingOfType("*port.NodeSelectionRequest")).Return(testNode, nil)

	return mockLB
}

func setupLiveAPIClient(tb testing.TB) port.LiveAPIClient {
	mockClient := &mockLiveAPIClient{}

	// Set up mock expectations for CheckPushAuth - return failure for invalid tokens
	mockClient.On("CheckPushAuth", mock.Anything, mock.MatchedBy(func(req *port.AuthRequest) bool {
		return req.AuthToken == "invalid-token"
	})).Return(&port.AuthResponse{
		Allowed: false,
		Reason:  "Invalid authentication token",
	}, nil)

	// Set up mock expectations for CheckPushAuth - return success for valid tokens
	mockClient.On("CheckPushAuth", mock.Anything, mock.AnythingOfType("*port.AuthRequest")).Return(&port.AuthResponse{
		Allowed: true,
		Reason:  "",
	}, nil)

	// Set up mock expectations for CheckPlayAuth
	mockClient.On("CheckPlayAuth", mock.Anything, mock.AnythingOfType("*port.AuthRequest")).Return(&port.AuthResponse{
		Allowed: true,
		Reason:  "",
	}, nil)

	// Set up mock expectations for NotifyStreamPublished
	mockClient.On("NotifyStreamPublished", mock.Anything, mock.AnythingOfType("*port.StreamPublishedEvent")).Return(nil)

	// Set up mock expectations for NotifyStreamUnpublished
	mockClient.On("NotifyStreamUnpublished", mock.Anything, mock.AnythingOfType("*port.StreamUnpublishedEvent")).Return(nil)

	return mockClient
}

func setupEventPublisher(tb testing.TB) port.EventPublisher {
	return &mockEventPublisher{}
}

func setupEventStore(tb testing.TB) port.EventStore {
	mockStore := &mockEventStore{}

	// Set up mock expectations
	mockStore.On("StoreEvent", mock.Anything, mock.AnythingOfType("*model.Event")).Return(nil)
	mockStore.On("StoreBatch", mock.Anything, mock.AnythingOfType("[]*model.Event")).Return(nil)
	mockStore.On("QueryEvents", mock.Anything, mock.AnythingOfType("time.Time"), mock.AnythingOfType("time.Time")).Return([]*model.Event{}, nil)

	return mockStore
}

// Mock implementations
type mockCacheRepository struct {
	mock.Mock
	client *redis.Client
}

func (m *mockCacheRepository) StoreStreamMapping(ctx context.Context, mapping *model.StreamMapping) error {
	args := m.Called(ctx, mapping)
	return args.Error(0)
}

func (m *mockCacheRepository) GetStreamMapping(ctx context.Context, streamKey string) (*model.StreamMapping, error) {
	args := m.Called(ctx, streamKey)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.StreamMapping), args.Error(1)
}

func (m *mockCacheRepository) DeleteStreamMapping(ctx context.Context, streamKey string) error {
	args := m.Called(ctx, streamKey)
	return args.Error(0)
}

func (m *mockCacheRepository) StoreNodeLoad(ctx context.Context, nodeID string, load *model.NodeLoad) error {
	args := m.Called(ctx, nodeID, load)
	return args.Error(0)
}

func (m *mockCacheRepository) GetNodeLoad(ctx context.Context, nodeID string) (*model.NodeLoad, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.NodeLoad), args.Error(1)
}

func (m *mockCacheRepository) DeleteNodeLoad(ctx context.Context, nodeID string) error {
	args := m.Called(ctx, nodeID)
	return args.Error(0)
}

func (m *mockCacheRepository) ListNodeLoads(ctx context.Context) (map[string]*model.NodeLoad, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]*model.NodeLoad), args.Error(1)
}

func (m *mockCacheRepository) StoreStreamStats(ctx context.Context, streamKey string, stats *model.StreamStats) error {
	args := m.Called(ctx, streamKey, stats)
	return args.Error(0)
}

func (m *mockCacheRepository) GetStreamStats(ctx context.Context, streamKey string) (*model.StreamStats, error) {
	args := m.Called(ctx, streamKey)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.StreamStats), args.Error(1)
}

func (m *mockCacheRepository) DeleteStreamStats(ctx context.Context, streamKey string) error {
	args := m.Called(ctx, streamKey)
	return args.Error(0)
}

func (m *mockCacheRepository) Ping(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *mockCacheRepository) Close() error {
	args := m.Called()
	return args.Error(0)
}

func (m *mockCacheRepository) Pipeline(ctx context.Context, fn func(pipeline port.Pipeline) error) error {
	args := m.Called(ctx, fn)
	return args.Error(0)
}

func (m *mockCacheRepository) Transaction(ctx context.Context, keys []string, fn func(tx port.Transaction) error) error {
	args := m.Called(ctx, keys, fn)
	return args.Error(0)
}

// Node operations
func (m *mockCacheRepository) StoreNode(ctx context.Context, node *model.MediaNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

func (m *mockCacheRepository) GetNode(ctx context.Context, nodeID string) (*model.MediaNode, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

func (m *mockCacheRepository) GetAllNodes(ctx context.Context) ([]*model.MediaNode, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.MediaNode), args.Error(1)
}

func (m *mockCacheRepository) DeleteNode(ctx context.Context, nodeID string) error {
	args := m.Called(ctx, nodeID)
	return args.Error(0)
}

func (m *mockCacheRepository) UpdateNodeStatus(ctx context.Context, nodeID string, status model.NodeStatus) error {
	args := m.Called(ctx, nodeID, status)
	return args.Error(0)
}

func (m *mockCacheRepository) UpdateNodeStats(ctx context.Context, nodeID string, stats *model.NodeStats) error {
	args := m.Called(ctx, nodeID, stats)
	return args.Error(0)
}

type mockMediaServerAdapter struct {
	mock.Mock
}

func (m *mockMediaServerAdapter) GetType() model.MediaServerType {
	args := m.Called()
	return args.Get(0).(model.MediaServerType)
}

func (m *mockMediaServerAdapter) ParseWebhookRequest(req *port.WebhookParseRequest) (*port.WebhookRequest, error) {
	args := m.Called(req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.WebhookRequest), args.Error(1)
}

func (m *mockMediaServerAdapter) ValidateWebhookRequest(req *port.WebhookValidateRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

func (m *mockMediaServerAdapter) RespondWebhookSuccess(req *port.WebhookResponseRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

func (m *mockMediaServerAdapter) RespondWebhookFailure(req *port.WebhookErrorRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

func (m *mockMediaServerAdapter) GeneratePushURL(ctx context.Context, req *port.PushURLRequest) (*model.PushURL, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.PushURL), args.Error(1)
}

func (m *mockMediaServerAdapter) GeneratePlayURLs(ctx context.Context, req *port.RequestPlayURLsRequest) ([]*model.PlayURL, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.PlayURL), args.Error(1)
}

func (m *mockMediaServerAdapter) GetStreamInfo(ctx context.Context, streamKey string) (*port.StreamInfo, error) {
	args := m.Called(ctx, streamKey)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.StreamInfo), args.Error(1)
}

func (m *mockMediaServerAdapter) KickStream(ctx context.Context, streamKey string) error {
	args := m.Called(ctx, streamKey)
	return args.Error(0)
}

func (m *mockMediaServerAdapter) GetServerStats(ctx context.Context, nodeID string) (*model.NodeLoad, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.NodeLoad), args.Error(1)
}

func (m *mockMediaServerAdapter) HealthCheck(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

type mockLoadBalancer struct {
	mock.Mock
}

func (m *mockLoadBalancer) GetAllNodes(ctx context.Context) ([]*model.MediaNode, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) AddNode(ctx context.Context, node *model.MediaServerNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

func (m *mockLoadBalancer) RemoveNode(ctx context.Context, nodeID string) error {
	args := m.Called(ctx, nodeID)
	return args.Error(0)
}

func (m *mockLoadBalancer) GetNodeByID(ctx context.Context, nodeID string) (*model.MediaNode, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) GetAvailableNodes(ctx context.Context) ([]*model.MediaNode, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) SelectNode(ctx context.Context, nodes []*model.MediaNode) (*model.MediaNode, error) {
	args := m.Called(ctx, nodes)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) SelectNodeByRequest(ctx context.Context, req *port.NodeSelectionRequest) (*model.MediaNode, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

func (m *mockLoadBalancer) RegisterNode(ctx context.Context, node *model.MediaNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

func (m *mockLoadBalancer) UpdateNode(ctx context.Context, node *model.MediaNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

func (m *mockLoadBalancer) UpdateNodeLoad(ctx context.Context, nodeID string, load *model.NodeLoad) error {
	args := m.Called(ctx, nodeID, load)
	return args.Error(0)
}

type mockEventPublisher struct {
	mock.Mock
}

func (m *mockEventPublisher) PublishEvent(ctx context.Context, event interface{}) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockEventPublisher) PublishAlertEvent(ctx context.Context, event interface{}) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

type mockEventStore struct {
	mock.Mock
}

func (m *mockEventStore) StoreEvent(ctx context.Context, event *model.Event) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockEventStore) StoreBatch(ctx context.Context, events []*model.Event) error {
	args := m.Called(ctx, events)
	return args.Error(0)
}

func (m *mockEventStore) QueryEvents(ctx context.Context, startTime, endTime time.Time) ([]*model.Event, error) {
	args := m.Called(ctx, startTime, endTime)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.Event), args.Error(1)
}

type mockLiveAPIClient struct {
	mock.Mock
}

func (m *mockLiveAPIClient) CheckPushAuth(ctx context.Context, req *port.AuthRequest) (*port.AuthResponse, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.AuthResponse), args.Error(1)
}

func (m *mockLiveAPIClient) NotifyStreamPublished(ctx context.Context, event *port.StreamPublishedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockLiveAPIClient) NotifyStreamUnpublished(ctx context.Context, event *port.StreamUnpublishedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockLiveAPIClient) NotifyStreamKicked(ctx context.Context, event *port.StreamKickedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockLiveAPIClient) NotifyRecordingCompleted(ctx context.Context, event *port.RecordingCompletedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

func (m *mockLiveAPIClient) SubmitForModeration(ctx context.Context, event *port.ModerationSubmissionEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

// Test functions

func TestAuthenticationBypass(t *testing.T) {
	ctx := context.Background()
	svc := setupTestService(t) // Use test helpers from performance package

	tests := []struct {
		name        string
		setupAttack func() (*port.WebhookRequest, error)
		wantErr     bool
	}{
		{
			name: "Invalid auth token",
			setupAttack: func() (*port.WebhookRequest, error) {
				// 尝试使用无效的认证令牌
				return &port.WebhookRequest{
					EventType:  model.WebhookEventTypePublish,
					StreamKey:  "test-stream",
					AuthToken:  "invalid-token",
					ClientIP:   "127.0.0.1",
					UserAgent:  "test-agent",
					ServerNode: "test-node",
					Protocol:   model.StreamProtocolRTMP,
					Timestamp:  time.Now(),
				}, nil
			},
			wantErr: true,
		},
		{
			name: "SQL injection in stream key",
			setupAttack: func() (*port.WebhookRequest, error) {
				// 尝试在 stream key 中注入 SQL
				return &port.WebhookRequest{
					EventType:  model.WebhookEventTypePublish,
					StreamKey:  "' OR '1'='1",
					AuthToken:  "test-token",
					ClientIP:   "127.0.0.1",
					UserAgent:  "test-agent",
					ServerNode: "test-node",
					Protocol:   model.StreamProtocolRTMP,
					Timestamp:  time.Now(),
				}, nil
			},
			wantErr: true,
		},
		{
			name: "XSS in user agent",
			setupAttack: func() (*port.WebhookRequest, error) {
				// 尝试在 user agent 中注入 XSS
				return &port.WebhookRequest{
					EventType:  model.WebhookEventTypePublish,
					StreamKey:  "test-stream",
					AuthToken:  "test-token",
					ClientIP:   "127.0.0.1",
					UserAgent:  "<script>alert('xss')</script>",
					ServerNode: "test-node",
					Protocol:   model.StreamProtocolRTMP,
					Timestamp:  time.Now(),
				}, nil
			},
			wantErr: true,
		},
		{
			name: "Command injection in client IP",
			setupAttack: func() (*port.WebhookRequest, error) {
				// 尝试在 client IP 中注入命令
				return &port.WebhookRequest{
					EventType:  model.WebhookEventTypePublish,
					StreamKey:  "test-stream",
					AuthToken:  "test-token",
					ClientIP:   "127.0.0.1; rm -rf /",
					UserAgent:  "test-agent",
					ServerNode: "test-node",
					Protocol:   model.StreamProtocolRTMP,
					Timestamp:  time.Now(),
				}, nil
			},
			wantErr: true,
		},
		{
			name: "Path traversal in stream key",
			setupAttack: func() (*port.WebhookRequest, error) {
				// 尝试在 stream key 中进行路径遍历
				return &port.WebhookRequest{
					EventType:  model.WebhookEventTypePublish,
					StreamKey:  "../../../etc/passwd",
					AuthToken:  "test-token",
					ClientIP:   "127.0.0.1",
					UserAgent:  "test-agent",
					ServerNode: "test-node",
					Protocol:   model.StreamProtocolRTMP,
					Timestamp:  time.Now(),
				}, nil
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req, err := tt.setupAttack()
			require.NoError(t, err)

			resp, err := svc.HandleWebhook(ctx, req)
			if tt.wantErr {
				require.NotNil(t, resp)
				require.False(t, resp.Success)
			} else {
				require.NoError(t, err)
				require.True(t, resp.Success)
			}
		})
	}
}

func TestDDoSProtection(t *testing.T) {
	ctx := context.Background()
	svc := setupTestService(t) // Use test helpers from performance package

	// Create a valid stream
	streamKey := setupTestStream(t, svc)

	// 模拟 DDoS 攻击
	const (
		numRequests      = 1000
		concurrentConns  = 100
		requestInterval  = time.Millisecond
		successThreshold = 0.95 // 95% 的请求应该被限流
	)

	results := make(chan bool, numRequests)
	start := time.Now()

	// 发送大量并发请求
	for i := 0; i < numRequests; i++ {
		go func() {
			req := &port.RequestPlayURLsRequest{
				StreamKey: streamKey,
				Protocols: []model.PlayProtocol{model.PlayProtocolHLS},
				Quality:   model.StreamQualityHigh,
				Region:    "test-region",
				ClientIP:  "127.0.0.1",
				UserAgent: "test-agent",
			}

			_, err := svc.RequestPlayURLs(ctx, req)
			results <- err == nil
		}()

		time.Sleep(requestInterval)
	}

	// 收集结果
	var successCount int
	for i := 0; i < numRequests; i++ {
		if <-results {
			successCount++
		}
	}

	duration := time.Since(start)
	successRate := float64(successCount) / float64(numRequests)

	t.Logf("DDoS Test Results:")
	t.Logf("Total Requests: %d", numRequests)
	t.Logf("Successful Requests: %d", successCount)
	t.Logf("Success Rate: %.2f%%", successRate*100)
	t.Logf("Test Duration: %v", duration)

	// 验证大部分请求是否被限流
	require.True(t, successRate < successThreshold, "too many requests succeeded, DDoS protection may be insufficient")
}

func TestSSLTLSConfiguration(t *testing.T) {
	// 测试 SSL/TLS 配置
	tests := []struct {
		name       string
		clientConf *tls.Config
		wantErr    bool
	}{
		{
			name: "Weak cipher suites",
			clientConf: &tls.Config{
				MinVersion: tls.VersionTLS10,
				CipherSuites: []uint16{
					tls.TLS_RSA_WITH_RC4_128_SHA,
					tls.TLS_RSA_WITH_3DES_EDE_CBC_SHA,
				},
			},
			wantErr: true,
		},
		{
			name: "Old TLS version",
			clientConf: &tls.Config{
				MinVersion: tls.VersionTLS10,
				MaxVersion: tls.VersionTLS10,
			},
			wantErr: true,
		},
		{
			name: "Strong configuration",
			clientConf: &tls.Config{
				MinVersion: tls.VersionTLS12,
				CipherSuites: []uint16{
					tls.TLS_ECDHE_RSA_WITH_AES_128_GCM_SHA256,
					tls.TLS_ECDHE_RSA_WITH_AES_256_GCM_SHA384,
				},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			client := &http.Client{
				Transport: &http.Transport{
					TLSClientConfig: tt.clientConf,
				},
			}

			// 尝试连接服务
			resp, err := client.Get("https://localhost:8443/health")
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				require.Equal(t, http.StatusOK, resp.StatusCode)
			}
		})
	}
}

func TestAccessControl(t *testing.T) {
	ctx := context.Background()
	svc := setupTestService(t) // Use test helpers from performance package

	// 创建一个有效的流
	streamKey := setupTestStream(t, svc)

	tests := []struct {
		name        string
		setupAttack func() error
		wantErr     bool
	}{
		{
			name: "Unauthorized kick stream",
			setupAttack: func() error {
				// 尝试在没有权限的情况下踢出流
				req := &port.KickStreamRequest{
					StreamKey: streamKey,
					Reason:    "test",
				}
				return svc.KickStream(ctx, req)
			},
			wantErr: true,
		},
		{
			name: "Cross-origin request",
			setupAttack: func() error {
				// 尝试从不允许的源发送请求
				client := &http.Client{}
				req, err := http.NewRequest("GET", "http://localhost:8080/streams/"+streamKey, nil)
				if err != nil {
					return err
				}
				req.Header.Set("Origin", "http://evil.com")
				resp, err := client.Do(req)
				if err != nil {
					return err
				}
				return checkCORSHeaders(resp)
			},
			wantErr: true,
		},
		{
			name: "Invalid IP range",
			setupAttack: func() error {
				// 尝试从不允许的 IP 范围访问
				req := &port.RequestPlayURLsRequest{
					StreamKey: streamKey,
					Protocols: []model.PlayProtocol{model.PlayProtocolHLS},
					Quality:   model.StreamQualityHigh,
					ClientIP:  "*******", // 假设这个 IP 不在允许范围内
					UserAgent: "test-agent",
				}
				_, err := svc.RequestPlayURLs(ctx, req)
				return err
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.setupAttack()
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestRateLimiting(t *testing.T) {
	ctx := context.Background()
	svc := setupTestService(t) // Use test helpers from performance package

	// 创建一个有效的流
	streamKey := setupTestStream(t, svc)

	// 测试不同类型的速率限制
	tests := []struct {
		name        string
		setupAttack func() (int, error)
		limit       int
		interval    time.Duration
	}{
		{
			name: "API rate limit",
			setupAttack: func() (int, error) {
				successCount := 0
				for i := 0; i < 100; i++ {
					req := &port.RequestPlayURLsRequest{
						StreamKey: streamKey,
						Protocols: []model.PlayProtocol{model.PlayProtocolHLS},
						Quality:   model.StreamQualityHigh,
						ClientIP:  "127.0.0.1",
						UserAgent: "test-agent",
					}
					_, err := svc.RequestPlayURLs(ctx, req)
					if err == nil {
						successCount++
					}
				}
				return successCount, nil
			},
			limit:    10,
			interval: time.Second,
		},
		{
			name: "Webhook rate limit",
			setupAttack: func() (int, error) {
				successCount := 0
				for i := 0; i < 100; i++ {
					req := &port.WebhookRequest{
						EventType:  model.WebhookEventTypePublish,
						StreamKey:  streamKey,
						AuthToken:  "test-token",
						ClientIP:   "127.0.0.1",
						UserAgent:  "test-agent",
						ServerNode: "test-node",
						Protocol:   model.StreamProtocolRTMP,
						Timestamp:  time.Now(),
					}
					resp, err := svc.HandleWebhook(ctx, req)
					if err == nil && resp.Success {
						successCount++
					}
				}
				return successCount, nil
			},
			limit:    5,
			interval: time.Second,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			start := time.Now()
			successCount, err := tt.setupAttack()
			duration := time.Since(start)

			require.NoError(t, err)

			rate := float64(successCount) / duration.Seconds()
			maxRate := float64(tt.limit) / tt.interval.Seconds()

			t.Logf("Rate Limit Test Results:")
			t.Logf("Success Count: %d", successCount)
			t.Logf("Duration: %v", duration)
			t.Logf("Actual Rate: %.2f/s", rate)
			t.Logf("Max Rate: %.2f/s", maxRate)

			require.True(t, rate <= maxRate*1.1, "rate limit exceeded: %.2f > %.2f", rate, maxRate)
		})
	}
}

func checkCORSHeaders(resp *http.Response) error {
	allowOrigin := resp.Header.Get("Access-Control-Allow-Origin")
	if allowOrigin == "*" {
		return fmt.Errorf("CORS allows all origins")
	}
	if !strings.HasPrefix(allowOrigin, "https://") {
		return fmt.Errorf("CORS allows non-HTTPS origins")
	}
	return nil
}
