/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package serializer

import (
	"encoding/json"
	"fmt"

	"cina.club/services/live-im-service/internal/domain/model"
)

// JSONSerializer implements message serialization using JSON format.
// This provides a reliable fallback when protobuf is not available.
type JSONSerializer struct{}

// NewJSONSerializer creates a new JSON serializer.
func NewJSONSerializer() *JSONSerializer {
	return &JSONSerializer{}
}

// SerializeMessage serializes a domain message to JSON bytes.
func (s *JSONSerializer) SerializeMessage(msg model.Message) ([]byte, error) {
	// Convert domain message to JSON-serializable format
	jsonMsg := &JSONMessage{
		MessageID: msg.MessageID,
		Type:      string(msg.Type),
		Timestamp: msg.Timestamp.Unix(),
		Token:     msg.Token,
		RoomID:    msg.RoomID,
		Content:   msg.Content,
		Count:     int32(msg.Count),
		GiftID:    msg.GiftID,
		ToUserID:  msg.ToUserID,
		Success:   msg.Success,
		ErrorMsg:  msg.ErrorMsg,
		Data:      msg.Data,
	}

	// Convert user information
	if msg.FromUser != nil {
		jsonMsg.FromUser = &JSONUser{
			UserID:   msg.FromUser.UserID.String(),
			Username: msg.FromUser.Username,
			Avatar:   msg.FromUser.Avatar,
			Role:     string(msg.FromUser.Role),
			Level:    int32(msg.FromUser.Level),
			VIPLevel: int32(msg.FromUser.VIPLevel),
			Badges:   msg.FromUser.Badges,
		}
	}

	// Convert room information
	if msg.RoomInfo != nil {
		jsonMsg.RoomInfo = &JSONRoomInfo{
			RoomID:      msg.RoomInfo.RoomID,
			Title:       msg.RoomInfo.Title,
			Description: msg.RoomInfo.Description,
			Category:    msg.RoomInfo.Category,
			Tags:        msg.RoomInfo.Tags,
			Status:      msg.RoomInfo.Status,
			OnlineCount: int32(msg.RoomInfo.OnlineCount),
			StartTime:   msg.RoomInfo.StartTime.Unix(),
		}
	}

	// Convert online users list
	if len(msg.OnlineList) > 0 {
		jsonMsg.OnlineUsers = make([]*JSONUser, len(msg.OnlineList))
		for i, user := range msg.OnlineList {
			jsonMsg.OnlineUsers[i] = &JSONUser{
				UserID:   user.UserID.String(),
				Username: user.Username,
				Avatar:   user.Avatar,
				Role:     string(user.Role),
				Level:    int32(user.Level),
				VIPLevel: int32(user.VIPLevel),
				Badges:   user.Badges,
			}
		}
	}

	// Serialize to JSON
	data, err := json.Marshal(jsonMsg)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal JSON: %w", err)
	}

	return data, nil
}

// DeserializeMessage deserializes JSON bytes to a domain message.
func (s *JSONSerializer) DeserializeMessage(data []byte) (model.Message, error) {
	var jsonMsg JSONMessage
	if err := json.Unmarshal(data, &jsonMsg); err != nil {
		return model.Message{}, fmt.Errorf("failed to unmarshal JSON: %w", err)
	}

	// Convert back to domain message
	msg := model.Message{
		MessageID: jsonMsg.MessageID,
		Type:      model.MessageType(jsonMsg.Type),
		RoomID:    jsonMsg.RoomID,
		Content:   jsonMsg.Content,
		Count:     int(jsonMsg.Count),
		GiftID:    jsonMsg.GiftID,
		ToUserID:  jsonMsg.ToUserID,
		Token:     jsonMsg.Token,
		Success:   jsonMsg.Success,
		ErrorMsg:  jsonMsg.ErrorMsg,
		Data:      jsonMsg.Data,
	}

	// Set timestamp
	if jsonMsg.Timestamp > 0 {
		msg.Timestamp = model.UnixToTime(jsonMsg.Timestamp)
	}

	// Convert user information
	if jsonMsg.FromUser != nil {
		userID, err := model.ParseUUID(jsonMsg.FromUser.UserID)
		if err != nil {
			return model.Message{}, fmt.Errorf("invalid user ID: %w", err)
		}
		msg.FromUser = &model.User{
			UserID:   userID,
			Username: jsonMsg.FromUser.Username,
			Avatar:   jsonMsg.FromUser.Avatar,
			Role:     model.UserRole(jsonMsg.FromUser.Role),
			Level:    int(jsonMsg.FromUser.Level),
			VIPLevel: int(jsonMsg.FromUser.VIPLevel),
			Badges:   jsonMsg.FromUser.Badges,
		}
	}

	// Convert room information
	if jsonMsg.RoomInfo != nil {
		msg.RoomInfo = &model.RoomInfo{
			RoomID:      jsonMsg.RoomInfo.RoomID,
			Title:       jsonMsg.RoomInfo.Title,
			Description: jsonMsg.RoomInfo.Description,
			Category:    jsonMsg.RoomInfo.Category,
			Tags:        jsonMsg.RoomInfo.Tags,
			Status:      jsonMsg.RoomInfo.Status,
			OnlineCount: int(jsonMsg.RoomInfo.OnlineCount),
		}
		if jsonMsg.RoomInfo.StartTime > 0 {
			msg.RoomInfo.StartTime = model.UnixToTime(jsonMsg.RoomInfo.StartTime)
		}
	}

	// Convert online users list
	if len(jsonMsg.OnlineUsers) > 0 {
		msg.OnlineList = make([]*model.User, len(jsonMsg.OnlineUsers))
		for i, jsonUser := range jsonMsg.OnlineUsers {
			userID, err := model.ParseUUID(jsonUser.UserID)
			if err != nil {
				return model.Message{}, fmt.Errorf("invalid online user ID: %w", err)
			}
			msg.OnlineList[i] = &model.User{
				UserID:   userID,
				Username: jsonUser.Username,
				Avatar:   jsonUser.Avatar,
				Role:     model.UserRole(jsonUser.Role),
				Level:    int(jsonUser.Level),
				VIPLevel: int(jsonUser.VIPLevel),
				Badges:   jsonUser.Badges,
			}
		}
	}

	return msg, nil
}

// JSONMessage represents the JSON serializable message format
type JSONMessage struct {
	MessageID   string                 `json:"message_id"`
	Type        string                 `json:"type"`
	Timestamp   int64                  `json:"timestamp"`
	Token       string                 `json:"token,omitempty"`
	RoomID      string                 `json:"room_id,omitempty"`
	Content     string                 `json:"content,omitempty"`
	Count       int32                  `json:"count,omitempty"`
	GiftID      string                 `json:"gift_id,omitempty"`
	ToUserID    string                 `json:"to_user_id,omitempty"`
	FromUser    *JSONUser              `json:"from_user,omitempty"`
	Success     bool                   `json:"success,omitempty"`
	ErrorMsg    string                 `json:"error_msg,omitempty"`
	RoomInfo    *JSONRoomInfo          `json:"room_info,omitempty"`
	OnlineUsers []*JSONUser            `json:"online_users,omitempty"`
	Data        map[string]interface{} `json:"data,omitempty"`
}

// JSONUser represents the JSON serializable user format
type JSONUser struct {
	UserID   string   `json:"user_id"`
	Username string   `json:"username"`
	Avatar   string   `json:"avatar"`
	Role     string   `json:"role"`
	Level    int32    `json:"level"`
	VIPLevel int32    `json:"vip_level"`
	Badges   []string `json:"badges,omitempty"`
}

// JSONRoomInfo represents the JSON serializable room info format
type JSONRoomInfo struct {
	RoomID      string   `json:"room_id"`
	Title       string   `json:"title"`
	Description string   `json:"description"`
	Category    string   `json:"category"`
	Tags        []string `json:"tags,omitempty"`
	Status      string   `json:"status"`
	OnlineCount int32    `json:"online_count"`
	StartTime   int64    `json:"start_time"`
}
