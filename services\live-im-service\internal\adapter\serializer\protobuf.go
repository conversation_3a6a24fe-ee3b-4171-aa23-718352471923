/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:12:13
Modified: 2025-07-11 10:12:13
*/

package serializer

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	"cina.club/services/live-im-service/internal/domain/model"
	pb "cina.club/services/live-im-service/proto/live_im"
)

// ProtobufSerializer handles protobuf serialization/deserialization.
type ProtobufSerializer struct{}

// NewProtobufSerializer creates a new protobuf serializer.
func NewProtobufSerializer() *ProtobufSerializer {
	return &ProtobufSerializer{}
}

// SerializeMessage serializes a domain message to protobuf bytes.
func (s *ProtobufSerializer) SerializeMessage(msg model.Message) ([]byte, error) {
	pbMsg := s.domainMessageToProto(msg)
	return proto.Marshal(pbMsg)
}

// DeserializeMessage deserializes protobuf bytes to a domain message.
func (s *ProtobufSerializer) DeserializeMessage(data []byte) (model.Message, error) {
	var pbMsg pb.Message
	if err := proto.Unmarshal(data, &pbMsg); err != nil {
		return model.Message{}, fmt.Errorf("failed to unmarshal protobuf: %w", err)
	}

	return s.protoMessageToDomain(&pbMsg), nil
}

// domainMessageToProto converts a domain message to protobuf message.
func (s *ProtobufSerializer) domainMessageToProto(msg model.Message) *pb.Message {
	pbMsg := &pb.Message{
		MessageId:    msg.MessageID,
		Type:         s.domainMessageTypeToProto(msg.Type),
		Timestamp:    timestamppb.New(msg.Timestamp),
		Token:        msg.Token,
		RoomId:       msg.RoomID,
		Content:      msg.Content,
		Count:        int32(msg.Count),
		GiftId:       msg.GiftID,
		ToUserId:     msg.ToUserID,
		Success:      msg.Success,
		ErrorMessage: msg.ErrorMsg,
		Metadata:     convertMapToStringMap(msg.Data),
	}

	// Convert user information
	if msg.FromClient != nil {
		pbMsg.FromUser = s.domainUserToProto(msg.FromClient)
	}

	// Convert room info
	if msg.RoomInfo != nil {
		pbMsg.RoomInfo = s.domainRoomInfoToProto(msg.RoomInfo)
	}

	// Convert online users
	if len(msg.OnlineList) > 0 {
		pbMsg.OnlineUsers = make([]*pb.User, len(msg.OnlineList))
		for i, user := range msg.OnlineList {
			pbMsg.OnlineUsers[i] = s.domainUserModelToProto(user)
		}
	}

	return pbMsg
}

// protoMessageToDomain converts a protobuf message to domain message.
func (s *ProtobufSerializer) protoMessageToDomain(pbMsg *pb.Message) model.Message {
	msg := model.Message{
		MessageID: pbMsg.MessageId,
		Type:      s.protoMessageTypeToDomain(pbMsg.Type),
		Timestamp: pbMsg.Timestamp.AsTime(),
		Token:     pbMsg.Token,
		RoomID:    pbMsg.RoomId,
		Content:   pbMsg.Content,
		Count:     int(pbMsg.Count),
		GiftID:    pbMsg.GiftId,
		ToUserID:  pbMsg.ToUserId,
		Success:   pbMsg.Success,
		ErrorMsg:  pbMsg.ErrorMessage,
		Data:      convertStringMapToMap(pbMsg.Metadata),
	}

	// Convert user information
	if pbMsg.FromUser != nil {
		msg.FromUser = s.protoUserToDomain(pbMsg.FromUser)
	}

	// Convert room info
	if pbMsg.RoomInfo != nil {
		msg.RoomInfo = s.protoRoomInfoToDomain(pbMsg.RoomInfo)
	}

	// Convert online users
	if len(pbMsg.OnlineUsers) > 0 {
		msg.OnlineList = make([]*model.User, len(pbMsg.OnlineUsers))
		for i, pbUser := range pbMsg.OnlineUsers {
			msg.OnlineList[i] = s.protoUserToDomain(pbUser)
		}
	}

	return msg
}

// domainMessageTypeToProto converts domain message type to protobuf.
func (s *ProtobufSerializer) domainMessageTypeToProto(msgType model.MessageType) pb.MessageType {
	switch msgType {
	case model.MessageTypeAuth:
		return pb.MessageType_MESSAGE_TYPE_AUTH
	case model.MessageTypeAuthResult:
		return pb.MessageType_MESSAGE_TYPE_AUTH_RESULT
	case model.MessageTypeBarrage:
		return pb.MessageType_MESSAGE_TYPE_BARRAGE
	case model.MessageTypeLike:
		return pb.MessageType_MESSAGE_TYPE_LIKE
	case model.MessageTypeGift:
		return pb.MessageType_MESSAGE_TYPE_GIFT
	case model.MessageTypePing:
		return pb.MessageType_MESSAGE_TYPE_PING
	case model.MessageTypePong:
		return pb.MessageType_MESSAGE_TYPE_PONG
	case model.MessageTypeError:
		return pb.MessageType_MESSAGE_TYPE_ERROR
	case model.MessageTypeRateLimited:
		return pb.MessageType_MESSAGE_TYPE_RATE_LIMITED
	case model.MessageTypeNewBarrage:
		return pb.MessageType_MESSAGE_TYPE_NEW_BARRAGE
	case model.MessageTypeLikeBurst:
		return pb.MessageType_MESSAGE_TYPE_LIKE_BURST
	case model.MessageTypeNewGift:
		return pb.MessageType_MESSAGE_TYPE_NEW_GIFT
	case model.MessageTypeUserJoin:
		return pb.MessageType_MESSAGE_TYPE_USER_JOIN
	case model.MessageTypeUserLeave:
		return pb.MessageType_MESSAGE_TYPE_USER_LEAVE
	case model.MessageTypeRoomUpdate:
		return pb.MessageType_MESSAGE_TYPE_ROOM_UPDATE
	default:
		return pb.MessageType_MESSAGE_TYPE_UNSPECIFIED
	}
}

// protoMessageTypeToDomain converts protobuf message type to domain.
func (s *ProtobufSerializer) protoMessageTypeToDomain(pbType pb.MessageType) model.MessageType {
	switch pbType {
	case pb.MessageType_MESSAGE_TYPE_AUTH:
		return model.MessageTypeAuth
	case pb.MessageType_MESSAGE_TYPE_AUTH_RESULT:
		return model.MessageTypeAuthResult
	case pb.MessageType_MESSAGE_TYPE_BARRAGE:
		return model.MessageTypeBarrage
	case pb.MessageType_MESSAGE_TYPE_LIKE:
		return model.MessageTypeLike
	case pb.MessageType_MESSAGE_TYPE_GIFT:
		return model.MessageTypeGift
	case pb.MessageType_MESSAGE_TYPE_PING:
		return model.MessageTypePing
	case pb.MessageType_MESSAGE_TYPE_PONG:
		return model.MessageTypePong
	case pb.MessageType_MESSAGE_TYPE_ERROR:
		return model.MessageTypeError
	case pb.MessageType_MESSAGE_TYPE_RATE_LIMITED:
		return model.MessageTypeRateLimited
	case pb.MessageType_MESSAGE_TYPE_NEW_BARRAGE:
		return model.MessageTypeNewBarrage
	case pb.MessageType_MESSAGE_TYPE_LIKE_BURST:
		return model.MessageTypeLikeBurst
	case pb.MessageType_MESSAGE_TYPE_NEW_GIFT:
		return model.MessageTypeNewGift
	case pb.MessageType_MESSAGE_TYPE_USER_JOIN:
		return model.MessageTypeUserJoin
	case pb.MessageType_MESSAGE_TYPE_USER_LEAVE:
		return model.MessageTypeUserLeave
	case pb.MessageType_MESSAGE_TYPE_ROOM_UPDATE:
		return model.MessageTypeRoomUpdate
	default:
		return ""
	}
}

// domainUserToProto converts domain client to protobuf user.
func (s *ProtobufSerializer) domainUserToProto(user *model.Client) *pb.User {
	if user == nil {
		return nil
	}

	return &pb.User{
		UserId:   user.UserID.String(),
		Username: user.Username,
		Avatar:   user.Avatar,
		Role:     s.domainUserRoleToProto(user.Role),
	}
}

// domainUserModelToProto converts domain user model to protobuf user.
func (s *ProtobufSerializer) domainUserModelToProto(user *model.User) *pb.User {
	if user == nil {
		return nil
	}

	return &pb.User{
		UserId:   user.UserID.String(),
		Username: user.Username,
		Avatar:   user.Avatar,
		Role:     s.domainUserRoleToProto(user.Role),
		Level:    int32(user.Level),
		VipLevel: int32(user.VIPLevel),
		Badges:   user.Badges,
	}
}

// protoUserToDomain converts protobuf user to domain user.
func (s *ProtobufSerializer) protoUserToDomain(pbUser *pb.User) *model.User {
	if pbUser == nil {
		return nil
	}

	return &model.User{
		Username: pbUser.Username,
		Avatar:   pbUser.Avatar,
		Role:     s.protoUserRoleToDomain(pbUser.Role),
		Level:    int(pbUser.Level),
		VIPLevel: int(pbUser.VipLevel),
		Badges:   pbUser.Badges,
	}
}

// domainUserRoleToProto converts domain user role to protobuf.
func (s *ProtobufSerializer) domainUserRoleToProto(role model.UserRole) pb.UserRole {
	switch role {
	case model.RoleViewer:
		return pb.UserRole_USER_ROLE_VIEWER
	case model.RoleStreamer:
		return pb.UserRole_USER_ROLE_STREAMER
	case model.RoleModerator:
		return pb.UserRole_USER_ROLE_MODERATOR
	case model.RoleAdmin:
		return pb.UserRole_USER_ROLE_ADMIN
	case model.RoleVIP:
		return pb.UserRole_USER_ROLE_VIP
	default:
		return pb.UserRole_USER_ROLE_UNSPECIFIED
	}
}

// protoUserRoleToDomain converts protobuf user role to domain.
func (s *ProtobufSerializer) protoUserRoleToDomain(pbRole pb.UserRole) model.UserRole {
	switch pbRole {
	case pb.UserRole_USER_ROLE_VIEWER:
		return model.RoleViewer
	case pb.UserRole_USER_ROLE_STREAMER:
		return model.RoleStreamer
	case pb.UserRole_USER_ROLE_MODERATOR:
		return model.RoleModerator
	case pb.UserRole_USER_ROLE_ADMIN:
		return model.RoleAdmin
	case pb.UserRole_USER_ROLE_VIP:
		return model.RoleVIP
	default:
		return model.RoleViewer
	}
}

// domainRoomInfoToProto converts domain room info to protobuf.
func (s *ProtobufSerializer) domainRoomInfoToProto(roomInfo *model.RoomInfo) *pb.RoomInfo {
	if roomInfo == nil {
		return nil
	}

	pbRoomInfo := &pb.RoomInfo{
		RoomId:      roomInfo.RoomID,
		Title:       roomInfo.Title,
		Status:      roomInfo.Status,
		OnlineCount: int32(roomInfo.OnlineCount),
		StartTime:   timestamppb.New(roomInfo.StartTime),
	}

	// Convert streamer information
	if roomInfo.Streamer != nil {
		pbRoomInfo.StreamerId = roomInfo.Streamer.UserID.String()
		pbRoomInfo.StreamerName = roomInfo.Streamer.Username
		pbRoomInfo.StreamerAvatar = roomInfo.Streamer.Avatar
	}

	return pbRoomInfo
}

// protoRoomInfoToDomain converts protobuf room info to domain.
func (s *ProtobufSerializer) protoRoomInfoToDomain(pbRoomInfo *pb.RoomInfo) *model.RoomInfo {
	if pbRoomInfo == nil {
		return nil
	}

	var startTime time.Time
	if pbRoomInfo.StartTime != nil {
		startTime = pbRoomInfo.StartTime.AsTime()
	}

	roomInfo := &model.RoomInfo{
		RoomID:      pbRoomInfo.RoomId,
		Title:       pbRoomInfo.Title,
		Status:      pbRoomInfo.Status,
		OnlineCount: int(pbRoomInfo.OnlineCount),
		StartTime:   startTime,
	}

	// Convert streamer information if available
	if pbRoomInfo.StreamerId != "" {
		userID, err := uuid.Parse(pbRoomInfo.StreamerId)
		if err == nil {
			roomInfo.Streamer = &model.User{
				UserID:   userID,
				Username: pbRoomInfo.StreamerName,
				Avatar:   pbRoomInfo.StreamerAvatar,
			}
		}
	}

	return roomInfo
}

// convertMapToStringMap converts map[string]interface{} to map[string]string
func convertMapToStringMap(data map[string]interface{}) map[string]string {
	if data == nil {
		return nil
	}
	result := make(map[string]string)
	for k, v := range data {
		if str, ok := v.(string); ok {
			result[k] = str
		} else {
			result[k] = fmt.Sprintf("%v", v)
		}
	}
	return result
}

// convertStringMapToMap converts map[string]string to map[string]interface{}
func convertStringMapToMap(data map[string]string) map[string]interface{} {
	if data == nil {
		return nil
	}
	result := make(map[string]interface{})
	for k, v := range data {
		result[k] = v
	}
	return result
}
