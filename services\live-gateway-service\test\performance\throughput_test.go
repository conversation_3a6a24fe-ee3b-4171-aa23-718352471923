package performance

import (
	"context"
	"math/rand"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"

	"cina.club/services/live-gateway-service/internal/application/port"
	"cina.club/services/live-gateway-service/internal/domain/model"
)

func runThroughputTest(tb testing.TB, svc port.GatewayService, testFunc func(context.Context, port.GatewayService) error) {
	ctx := context.Background()

	// 预热
	time.Sleep(warmupDuration)

	// 记录开始时间
	start := time.Now()
	var requestCount int64
	var errorCount int64
	var wg sync.WaitGroup

	// 启动多个 goroutine 发送请求
	for i := 0; i < 10; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			for time.Since(start) < throughputTestDuration {
				err := testFunc(ctx, svc)
				if err != nil {
					atomic.AddInt64(&errorCount, 1)
				}
				atomic.AddInt64(&requestCount, 1)

				// 控制发送速率
				time.Sleep(time.Second / time.Duration(targetRPS/10))
			}
		}()
	}

	// 等待所有请求完成
	wg.Wait()

	// 冷却
	time.Sleep(cooldownDuration)

	// 计算实际 RPS
	duration := time.Since(start)
	actualRPS := float64(requestCount) / duration.Seconds()
	errorRate := float64(errorCount) / float64(requestCount) * 100

	tb.Logf("Throughput Test Results:")
	tb.Logf("Total Requests: %d", requestCount)
	tb.Logf("Total Errors: %d", errorCount)
	tb.Logf("Error Rate: %.2f%%", errorRate)
	tb.Logf("Actual RPS: %.2f", actualRPS)
	tb.Logf("Test Duration: %v", duration)

	require.True(tb, actualRPS >= float64(targetRPS)*0.9, "actual RPS %.2f is less than 90%% of target RPS %d", actualRPS, targetRPS)
	require.True(tb, errorRate < 1.0, "error rate %.2f%% exceeds 1%%", errorRate)
}

func TestPushURLThroughput(t *testing.T) {
	svc := setupTestService(t)

	testFunc := func(ctx context.Context, svc port.GatewayService) error {
		req := &port.CreateStreamRequest{
			RoomID:    uuid.New(),
			UserID:    uuid.New(),
			Protocol:  model.StreamProtocolRTMP,
			Quality:   model.StreamQualityHigh,
			ClientIP:  "127.0.0.1",
			UserAgent: "test-agent",
		}

		_, err := svc.RequestPushURL(ctx, req)
		return err
	}

	runThroughputTest(t, svc, testFunc)
}

func TestPlayURLsThroughput(t *testing.T) {
	svc := setupTestService(t)

	// 预创建一些流
	streamKeys := make([]string, 100)
	for i := range streamKeys {
		streamKeys[i] = setupTestStream(t, svc)
	}

	testFunc := func(ctx context.Context, svc port.GatewayService) error {
		req := &port.RequestPlayURLsRequest{
			StreamKey: streamKeys[rand.Intn(len(streamKeys))],
			Protocols: []model.PlayProtocol{
				model.PlayProtocolHLS,
				model.PlayProtocolFLV,
			},
			Quality:   model.StreamQualityHigh,
			EnableCDN: true,
			Region:    "test-region",
			ClientIP:  "127.0.0.1",
			UserAgent: "test-agent",
		}

		_, err := svc.RequestPlayURLs(ctx, req)
		return err
	}

	runThroughputTest(t, svc, testFunc)
}

func TestWebhookThroughput(t *testing.T) {
	svc := setupTestService(t)

	// 预创建一些流
	streamKeys := make([]string, 100)
	for i := range streamKeys {
		streamKeys[i] = setupTestStream(t, svc)
	}

	testFunc := func(ctx context.Context, svc port.GatewayService) error {
		req := &port.WebhookRequest{
			EventType:  model.WebhookEventTypePublish,
			StreamKey:  streamKeys[rand.Intn(len(streamKeys))],
			AuthToken:  "test-token",
			ClientIP:   "127.0.0.1",
			UserAgent:  "test-agent",
			ServerNode: "test-node",
			Protocol:   model.StreamProtocolRTMP,
			Timestamp:  time.Now(),
		}

		_, err := svc.HandleWebhook(ctx, req)
		return err
	}

	runThroughputTest(t, svc, testFunc)
}

func TestStreamInfoThroughput(t *testing.T) {
	svc := setupTestService(t)

	// 预创建一些流
	streamKeys := make([]string, 100)
	for i := range streamKeys {
		streamKeys[i] = setupTestStream(t, svc)
	}

	testFunc := func(ctx context.Context, svc port.GatewayService) error {
		_, err := svc.GetStreamInfo(ctx, streamKeys[rand.Intn(len(streamKeys))])
		return err
	}

	runThroughputTest(t, svc, testFunc)
}

func TestServerStatsThroughput(t *testing.T) {
	svc := setupTestService(t)

	testFunc := func(ctx context.Context, svc port.GatewayService) error {
		_, err := svc.GetServerStats(ctx)
		return err
	}

	runThroughputTest(t, svc, testFunc)
}
