/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package errors

import (
	"context"
	"fmt"
	"runtime"
	"time"

	"github.com/sirupsen/logrus"
)

// ErrorHandler provides centralized error handling, logging, and recovery
type ErrorHandler interface {
	// HandleError processes an error and returns a sanitized version for clients
	HandleError(ctx context.Context, err error, operation string) *LiveIMError
	
	// LogError logs an error with appropriate context
	LogError(ctx context.Context, err error, operation string, metadata map[string]interface{})
	
	// ShouldRetry determines if an operation should be retried
	ShouldRetry(err error, attempt int) bool
	
	// GetRetryDelay calculates the delay before retrying
	GetRetryDelay(attempt int) time.Duration
	
	// RecoverFromPanic recovers from panics and converts them to errors
	RecoverFromPanic(ctx context.Context, operation string) *LiveIMError
}

// DefaultErrorHandler implements ErrorHandler
type DefaultErrorHandler struct {
	logger      *logrus.Logger
	maxRetries  int
	baseDelay   time.Duration
	maxDelay    time.Duration
	enableStack bool
}

// ErrorHandlerConfig configures the error handler
type ErrorHandlerConfig struct {
	MaxRetries  int
	BaseDelay   time.Duration
	MaxDelay    time.Duration
	EnableStack bool
}

// NewErrorHandler creates a new error handler
func NewErrorHandler(logger *logrus.Logger, config ErrorHandlerConfig) ErrorHandler {
	if config.MaxRetries == 0 {
		config.MaxRetries = 3
	}
	if config.BaseDelay == 0 {
		config.BaseDelay = 100 * time.Millisecond
	}
	if config.MaxDelay == 0 {
		config.MaxDelay = 30 * time.Second
	}

	return &DefaultErrorHandler{
		logger:      logger,
		maxRetries:  config.MaxRetries,
		baseDelay:   config.BaseDelay,
		maxDelay:    config.MaxDelay,
		enableStack: config.EnableStack,
	}
}

// HandleError processes an error and returns a sanitized version for clients
func (h *DefaultErrorHandler) HandleError(ctx context.Context, err error, operation string) *LiveIMError {
	if err == nil {
		return nil
	}

	// If it's already a LiveIMError, log it and return
	if liveErr := GetLiveIMError(err); liveErr != nil {
		h.LogError(ctx, liveErr, operation, liveErr.Metadata)
		return liveErr
	}

	// Convert standard errors to LiveIMError
	liveErr := h.convertToLiveIMError(err, operation)
	h.LogError(ctx, liveErr, operation, liveErr.Metadata)
	
	return liveErr
}

// LogError logs an error with appropriate context
func (h *DefaultErrorHandler) LogError(ctx context.Context, err error, operation string, metadata map[string]interface{}) {
	fields := logrus.Fields{
		"operation": operation,
		"error":     err.Error(),
	}

	// Add context values
	if requestID := ctx.Value("request_id"); requestID != nil {
		fields["request_id"] = requestID
	}
	if userID := ctx.Value("user_id"); userID != nil {
		fields["user_id"] = userID
	}
	if clientID := ctx.Value("client_id"); clientID != nil {
		fields["client_id"] = clientID
	}

	// Add metadata
	for k, v := range metadata {
		fields[k] = v
	}

	// Add stack trace for internal errors if enabled
	if h.enableStack {
		if liveErr := GetLiveIMError(err); liveErr != nil {
			if liveErr.Code == ErrCodeInternalError {
				fields["stack_trace"] = getStackTrace()
			}
		}
	}

	// Determine log level based on error type
	logLevel := h.getLogLevel(err)
	
	switch logLevel {
	case logrus.ErrorLevel:
		h.logger.WithFields(fields).Error("Operation failed")
	case logrus.WarnLevel:
		h.logger.WithFields(fields).Warn("Operation warning")
	case logrus.InfoLevel:
		h.logger.WithFields(fields).Info("Operation info")
	default:
		h.logger.WithFields(fields).Error("Operation failed")
	}
}

// ShouldRetry determines if an operation should be retried
func (h *DefaultErrorHandler) ShouldRetry(err error, attempt int) bool {
	if attempt >= h.maxRetries {
		return false
	}

	if liveErr := GetLiveIMError(err); liveErr != nil {
		return liveErr.Retryable
	}

	// Default to not retrying unknown errors
	return false
}

// GetRetryDelay calculates the delay before retrying using exponential backoff
func (h *DefaultErrorHandler) GetRetryDelay(attempt int) time.Duration {
	delay := time.Duration(attempt) * h.baseDelay
	
	// Exponential backoff with jitter
	delay = delay * time.Duration(1<<uint(attempt))
	
	if delay > h.maxDelay {
		delay = h.maxDelay
	}
	
	return delay
}

// RecoverFromPanic recovers from panics and converts them to errors
func (h *DefaultErrorHandler) RecoverFromPanic(ctx context.Context, operation string) *LiveIMError {
	if r := recover(); r != nil {
		var err error
		switch x := r.(type) {
		case string:
			err = fmt.Errorf("panic: %s", x)
		case error:
			err = fmt.Errorf("panic: %w", x)
		default:
			err = fmt.Errorf("panic: %v", x)
		}

		liveErr := NewLiveIMErrorWithCause(
			ErrCodeInternalError,
			"Internal server error due to panic",
			err,
		).WithMetadata("panic_value", r).
			WithMetadata("stack_trace", getStackTrace())

		h.LogError(ctx, liveErr, operation, liveErr.Metadata)
		return liveErr
	}
	return nil
}

// convertToLiveIMError converts standard errors to LiveIMError
func (h *DefaultErrorHandler) convertToLiveIMError(err error, operation string) *LiveIMError {
	// Try to infer error type from error message or type
	errorMsg := err.Error()
	
	// Check for common error patterns
	switch {
	case contains(errorMsg, "context deadline exceeded", "timeout"):
		return WrapError(err, ErrCodeTimeoutError, "Operation timed out")
	case contains(errorMsg, "connection refused", "network"):
		return WrapError(err, ErrCodeNetworkError, "Network error occurred")
	case contains(errorMsg, "redis", "cache"):
		return WrapError(err, ErrCodeRedisError, "Cache operation failed")
	case contains(errorMsg, "database", "sql", "db"):
		return WrapError(err, ErrCodeDatabaseError, "Database operation failed")
	case contains(errorMsg, "unauthorized", "authentication"):
		return WrapError(err, ErrCodeUnauthorized, "Authentication failed")
	case contains(errorMsg, "permission", "forbidden"):
		return WrapError(err, ErrCodePermissionDenied, "Permission denied")
	case contains(errorMsg, "not found"):
		return WrapError(err, ErrCodeClientNotFound, "Resource not found")
	case contains(errorMsg, "invalid", "malformed"):
		return WrapError(err, ErrCodeInvalidInput, "Invalid input")
	default:
		return WrapError(err, ErrCodeInternalError, "Internal error occurred")
	}
}

// getLogLevel determines the appropriate log level for an error
func (h *DefaultErrorHandler) getLogLevel(err error) logrus.Level {
	if liveErr := GetLiveIMError(err); liveErr != nil {
		switch liveErr.Code {
		case ErrCodeInternalError, ErrCodeDatabaseError, ErrCodeRedisError:
			return logrus.ErrorLevel
		case ErrCodeServiceUnavailable, ErrCodeTimeoutError, ErrCodeNetworkError:
			return logrus.WarnLevel
		case ErrCodeUnauthorized, ErrCodePermissionDenied, ErrCodeInvalidInput:
			return logrus.InfoLevel
		case ErrCodeClientRateLimited, ErrCodeValidationFailed:
			return logrus.InfoLevel
		default:
			return logrus.ErrorLevel
		}
	}
	return logrus.ErrorLevel
}

// getStackTrace returns the current stack trace
func getStackTrace() string {
	buf := make([]byte, 4096)
	n := runtime.Stack(buf, false)
	return string(buf[:n])
}

// contains checks if any of the substrings are contained in the main string
func contains(str string, substrings ...string) bool {
	for _, substr := range substrings {
		if len(str) >= len(substr) {
			for i := 0; i <= len(str)-len(substr); i++ {
				if str[i:i+len(substr)] == substr {
					return true
				}
			}
		}
	}
	return false
}

// ErrorMiddleware provides error handling middleware for HTTP handlers
type ErrorMiddleware struct {
	handler ErrorHandler
}

// NewErrorMiddleware creates a new error middleware
func NewErrorMiddleware(handler ErrorHandler) *ErrorMiddleware {
	return &ErrorMiddleware{
		handler: handler,
	}
}

// HandleWithRecovery wraps a function with panic recovery
func (m *ErrorMiddleware) HandleWithRecovery(ctx context.Context, operation string, fn func() error) *LiveIMError {
	defer func() {
		if err := m.handler.RecoverFromPanic(ctx, operation); err != nil {
			// Panic was recovered and converted to error
		}
	}()

	if err := fn(); err != nil {
		return m.handler.HandleError(ctx, err, operation)
	}

	return nil
}

// RetryWithBackoff retries an operation with exponential backoff
func (m *ErrorMiddleware) RetryWithBackoff(ctx context.Context, operation string, fn func() error) error {
	var lastErr error
	
	for attempt := 0; attempt < 3; attempt++ {
		if err := fn(); err != nil {
			lastErr = err
			
			if !m.handler.ShouldRetry(err, attempt) {
				return m.handler.HandleError(ctx, err, operation)
			}
			
			if attempt < 2 { // Don't delay after the last attempt
				delay := m.handler.GetRetryDelay(attempt)
				select {
				case <-ctx.Done():
					return m.handler.HandleError(ctx, ctx.Err(), operation)
				case <-time.After(delay):
					// Continue to next attempt
				}
			}
		} else {
			return nil // Success
		}
	}
	
	return m.handler.HandleError(ctx, lastErr, operation)
}
