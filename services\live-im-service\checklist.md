# Live IM Service Development Checklist | 实时消息服务开发检查清单

<!--
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-30 10:00:00
Modified: 2025-07-11 10:00:00
-->

## Table of Contents | 目录

- [Development Standards | 开发标准](#development-standards--开发标准)
- [Testing Requirements | 测试要求](#testing-requirements--测试要求)
- [Quality Gates | 质量门禁](#quality-gates--质量门禁)
- [Project-Specific Items | 项目特定检查项](#project-specific-items--项目特定检查项)
- [Pre-Commit Checklist | 提交前检查清单](#pre-commit-checklist--提交前检查清单)
- [Release Checklist | 发布检查清单](#release-checklist--发布检查清单)

---

## Development Standards | 开发标准

### Code Formatting and Style | 代码格式和风格

#### English | 英文
- [ ] **Go Formatting**: Code is formatted using `gofmt` or `goimports`
- [ ] **Naming Conventions**: Follow Go naming conventions (PascalCase for exported, camelCase for unexported)
- [ ] **Package Names**: Use short, lowercase package names without underscores
- [ ] **Function Length**: Keep functions under 50 lines when possible
- [ ] **Cyclomatic Complexity**: Maintain complexity score below 10 per function

#### Chinese | 中文
- [ ] **Go 格式化**: 使用 `gofmt` 或 `goimports` 格式化代码
- [ ] **命名约定**: 遵循 Go 命名约定（导出用 PascalCase，未导出用 camelCase）
- [ ] **包名**: 使用简短的小写包名，不使用下划线
- [ ] **函数长度**: 尽可能保持函数在 50 行以内
- [ ] **圈复杂度**: 保持每个函数的复杂度评分低于 10

```go
// ✅ Good Example | 良好示例
func (h *Hub) RegisterClient(client *model.Client) {
    h.register <- client
}

// ❌ Bad Example | 错误示例
func (h *Hub) register_client(Client *model.Client) {
    h.register<-Client
}
```

### Import Organization | 导入组织

#### English | 英文
- [ ] **Standard Library First**: Standard library imports come first
- [ ] **Third-Party Second**: External dependencies in the middle
- [ ] **Local Packages Last**: Project-specific imports at the bottom
- [ ] **No Unused Imports**: Remove all unused imports
- [ ] **Group Separation**: Use blank lines to separate import groups

#### Chinese | 中文
- [ ] **标准库优先**: 标准库导入放在最前面
- [ ] **第三方其次**: 外部依赖放在中间
- [ ] **本地包最后**: 项目特定导入放在最后
- [ ] **无未使用导入**: 删除所有未使用的导入
- [ ] **分组分隔**: 使用空行分隔导入组

```go
// ✅ Good Example | 良好示例
import (
    "context"
    "fmt"
    "time"

    "github.com/go-redis/redis/v8"
    "github.com/sirupsen/logrus"

    "cina.club/services/live-im-service/internal/domain/model"
)
```

### Error Handling | 错误处理

#### English | 英文
- [ ] **Explicit Error Handling**: Never ignore errors with `_`
- [ ] **Error Wrapping**: Use `fmt.Errorf` with `%w` verb for error wrapping
- [ ] **Context Preservation**: Include relevant context in error messages
- [ ] **Early Returns**: Use early returns for error conditions
- [ ] **Logging Standards**: Log errors at appropriate levels

#### Chinese | 中文
- [ ] **显式错误处理**: 永远不要用 `_` 忽略错误
- [ ] **错误包装**: 使用 `fmt.Errorf` 和 `%w` 动词包装错误
- [ ] **上下文保留**: 在错误消息中包含相关上下文
- [ ] **早期返回**: 对错误条件使用早期返回
- [ ] **日志标准**: 在适当级别记录错误

```go
// ✅ Good Example | 良好示例
func (r *RedisRateLimiter) Allow(ctx context.Context, userID uuid.UUID, action string) (bool, error) {
    result, err := r.client.Eval(ctx, script, keys, args...).Result()
    if err != nil {
        return false, fmt.Errorf("failed to check rate limit for user %s action %s: %w", userID, action, err)
    }
    return result.(int64) == 1, nil
}
```

### Interface Design | 接口设计

#### English | 英文
- [ ] **Small Interfaces**: Keep interfaces focused and small (1-3 methods)
- [ ] **Dependency Injection**: Use interfaces for dependency injection
- [ ] **Accept Interfaces**: Accept interfaces, return concrete types
- [ ] **Context First**: Context should be the first parameter
- [ ] **Error Last**: Error should be the last return value

#### Chinese | 中文
- [ ] **小接口**: 保持接口专注且小巧（1-3 个方法）
- [ ] **依赖注入**: 使用接口进行依赖注入
- [ ] **接受接口**: 接受接口，返回具体类型
- [ ] **上下文优先**: Context 应该是第一个参数
- [ ] **错误最后**: Error 应该是最后一个返回值

```go
// ✅ Good Example | 良好示例
type RateLimiter interface {
    Allow(ctx context.Context, userID uuid.UUID, action string) (bool, error)
    Reset(ctx context.Context, userID uuid.UUID, action string) error
}
```

### Documentation Requirements | 文档要求

#### English | 英文
- [ ] **Public API Documentation**: All exported functions have godoc comments
- [ ] **Package Documentation**: Package-level documentation exists
- [ ] **Complex Logic Comments**: Non-obvious code has explanatory comments
- [ ] **README Updates**: README.md reflects current functionality
- [ ] **Architecture Documentation**: High-level design documented

#### Chinese | 中文
- [ ] **公共 API 文档**: 所有导出函数都有 godoc 注释
- [ ] **包级文档**: 存在包级文档
- [ ] **复杂逻辑注释**: 非显而易见的代码有解释性注释
- [ ] **README 更新**: README.md 反映当前功能
- [ ] **架构文档**: 高级设计已文档化

```go
// ✅ Good Example | 良好示例
// RedisRateLimiter implements rate limiting using Redis as the backend store.
// It uses a token bucket algorithm to control the rate of requests per user.
type RedisRateLimiter struct {
    client redis.Client
    config RateLimitConfig
    logger *logrus.Logger
}
```

---

## Testing Requirements | 测试要求

### Coverage Targets | 覆盖率目标

#### English | 英文
- [ ] **Adapter Layer**: Minimum 90% test coverage
- [ ] **Service Layer**: Minimum 95% test coverage
- [ ] **Domain Layer**: 100% test coverage
- [ ] **Integration Tests**: Cover all critical paths
- [ ] **Benchmark Tests**: Performance-critical components

#### Chinese | 中文
- [ ] **适配器层**: 最低 90% 测试覆盖率
- [ ] **服务层**: 最低 95% 测试覆盖率
- [ ] **领域层**: 100% 测试覆盖率
- [ ] **集成测试**: 覆盖所有关键路径
- [ ] **基准测试**: 性能关键组件

### Test Structure | 测试结构

#### English | 英文
- [ ] **Test Naming**: Use `TestFunctionName_Scenario` pattern
- [ ] **Table-Driven Tests**: Use table-driven tests for multiple scenarios
- [ ] **Setup/Teardown**: Proper test setup and cleanup
- [ ] **Test Isolation**: Tests should not depend on each other
- [ ] **Deterministic**: Tests should produce consistent results

#### Chinese | 中文
- [ ] **测试命名**: 使用 `TestFunctionName_Scenario` 模式
- [ ] **表驱动测试**: 对多种场景使用表驱动测试
- [ ] **设置/清理**: 正确的测试设置和清理
- [ ] **测试隔离**: 测试之间不应相互依赖
- [ ] **确定性**: 测试应产生一致的结果

```go
// ✅ Good Example | 良好示例
func TestRedisRateLimiter_Allow_Success(t *testing.T) {
    tests := []struct {
        name     string
        userID   uuid.UUID
        action   string
        expected bool
    }{
        {"barrage allowed", uuid.New(), "barrage", true},
        {"like allowed", uuid.New(), "like", true},
    }

    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // Test implementation
        })
    }
}
```

### Mock vs Real Dependencies | 模拟与真实依赖

#### English | 英文
- [ ] **Structure Tests**: Use real clients for structure validation
- [ ] **Mock External Services**: Mock external HTTP/gRPC services
- [ ] **Real Database Tests**: Use testcontainers for database integration
- [ ] **Mock-Free Approach**: Prefer real dependencies when possible
- [ ] **Isolation**: Ensure tests don't affect each other

#### Chinese | 中文
- [ ] **结构测试**: 使用真实客户端进行结构验证
- [ ] **模拟外部服务**: 模拟外部 HTTP/gRPC 服务
- [ ] **真实数据库测试**: 使用 testcontainers 进行数据库集成
- [ ] **无模拟方法**: 尽可能使用真实依赖
- [ ] **隔离**: 确保测试不会相互影响

```go
// ✅ Good Example | 良好示例
func createTestRedisClient() *redis.Client {
    return redis.NewClient(&redis.Options{
        Addr:     "localhost:6379",
        Password: "",
        DB:       15, // Use test database
    })
}
```

### Performance Testing | 性能测试

#### English | 英文
- [ ] **Benchmark Tests**: Critical paths have benchmark tests
- [ ] **Memory Profiling**: Memory usage is profiled
- [ ] **Concurrency Tests**: Race conditions are tested
- [ ] **Load Testing**: System handles expected load
- [ ] **Regression Testing**: Performance doesn't degrade

#### Chinese | 中文
- [ ] **基准测试**: 关键路径有基准测试
- [ ] **内存分析**: 内存使用已分析
- [ ] **并发测试**: 竞态条件已测试
- [ ] **负载测试**: 系统处理预期负载
- [ ] **回归测试**: 性能不会退化

```go
// ✅ Good Example | 良好示例
func BenchmarkRedisRateLimiter_Allow(b *testing.B) {
    rateLimiter := setupRateLimiter()
    userID := uuid.New()

    b.ResetTimer()
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            rateLimiter.Allow(context.Background(), userID, "barrage")
        }
    })
}
```

---

## Quality Gates | 质量门禁

### Build Requirements | 构建要求

#### English | 英文
- [ ] **Compilation Success**: `go build ./...` passes without errors
- [ ] **Test Execution**: `go test ./...` passes all tests
- [ ] **Race Detection**: `go test -race ./...` passes without race conditions
- [ ] **Vet Analysis**: `go vet ./...` passes without warnings
- [ ] **Module Verification**: `go mod verify` succeeds

#### Chinese | 中文
- [ ] **编译成功**: `go build ./...` 无错误通过
- [ ] **测试执行**: `go test ./...` 通过所有测试
- [ ] **竞态检测**: `go test -race ./...` 无竞态条件通过
- [ ] **静态分析**: `go vet ./...` 无警告通过
- [ ] **模块验证**: `go mod verify` 成功

```bash
# ✅ Quality Gate Commands | 质量门禁命令
go build ./...
go test ./...
go test -race ./...
go vet ./...
go mod verify
go mod tidy
```

### Code Quality | 代码质量

#### English | 英文
- [ ] **Linting**: Code passes golangci-lint checks
- [ ] **Complexity**: Cyclomatic complexity within limits
- [ ] **Duplication**: No significant code duplication
- [ ] **Dead Code**: No unused code or variables
- [ ] **Security**: No security vulnerabilities detected

#### Chinese | 中文
- [ ] **代码检查**: 代码通过 golangci-lint 检查
- [ ] **复杂度**: 圈复杂度在限制范围内
- [ ] **重复**: 无重大代码重复
- [ ] **死代码**: 无未使用的代码或变量
- [ ] **安全**: 未检测到安全漏洞

### Security Checks | 安全检查

#### English | 英文
- [ ] **Dependency Scanning**: No known vulnerabilities in dependencies
- [ ] **Secret Detection**: No hardcoded secrets or credentials
- [ ] **Input Validation**: All user inputs are properly validated
- [ ] **Authentication**: Proper JWT token validation
- [ ] **Authorization**: Role-based access control implemented

#### Chinese | 中文
- [ ] **依赖扫描**: 依赖项中无已知漏洞
- [ ] **密钥检测**: 无硬编码密钥或凭据
- [ ] **输入验证**: 所有用户输入都经过适当验证
- [ ] **身份验证**: 正确的 JWT 令牌验证
- [ ] **授权**: 实现基于角色的访问控制

```go
// ✅ Good Example | 良好示例
func (a *AuthService) ValidateToken(ctx context.Context, tokenString string) (*port.UserInfo, error) {
    if tokenString == "" {
        return nil, fmt.Errorf("token is required")
    }

    token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
        return []byte(a.jwtSecret), nil
    })

    if err != nil {
        return nil, fmt.Errorf("invalid token: %w", err)
    }

    // Additional validation...
}
```

### Performance Requirements | 性能要求

#### English | 英文
- [ ] **Response Time**: API responses under 100ms for 95th percentile
- [ ] **Memory Usage**: Memory usage within acceptable limits
- [ ] **CPU Usage**: CPU usage optimized for concurrent operations
- [ ] **Database Performance**: Database queries optimized
- [ ] **Cache Hit Rate**: Redis cache hit rate above 90%

#### Chinese | 中文
- [ ] **响应时间**: API 响应 95% 分位数低于 100ms
- [ ] **内存使用**: 内存使用在可接受范围内
- [ ] **CPU 使用**: CPU 使用针对并发操作优化
- [ ] **数据库性能**: 数据库查询已优化
- [ ] **缓存命中率**: Redis 缓存命中率高于 90%

---

## Project-Specific Items | 项目特定检查项

### Protobuf Integration | Protobuf 集成

#### English | 英文
- [ ] **Schema Validation**: `.proto` files follow naming conventions
- [ ] **Code Generation**: Generated `.pb.go` files are up-to-date
- [ ] **Compatibility**: Protobuf messages implement required interfaces
- [ ] **Serialization**: Message serialization/deserialization works correctly
- [ ] **Version Compatibility**: Backward compatibility maintained

#### Chinese | 中文
- [ ] **模式验证**: `.proto` 文件遵循命名约定
- [ ] **代码生成**: 生成的 `.pb.go` 文件是最新的
- [ ] **兼容性**: Protobuf 消息实现所需接口
- [ ] **序列化**: 消息序列化/反序列化正常工作
- [ ] **版本兼容**: 保持向后兼容性

```bash
# ✅ Protobuf Commands | Protobuf 命令
protoc --go_out=. --go_opt=paths=source_relative proto/live_im.proto
go generate ./...
```

### Redis Integration | Redis 集成

#### English | 英文
- [ ] **Connection Handling**: Proper Redis connection management
- [ ] **Error Recovery**: Graceful handling of Redis connection failures
- [ ] **Performance**: Redis operations are optimized
- [ ] **Data Consistency**: Redis data structures maintain consistency
- [ ] **Memory Management**: Proper key expiration and cleanup

#### Chinese | 中文
- [ ] **连接处理**: 正确的 Redis 连接管理
- [ ] **错误恢复**: 优雅处理 Redis 连接失败
- [ ] **性能**: Redis 操作已优化
- [ ] **数据一致性**: Redis 数据结构保持一致性
- [ ] **内存管理**: 正确的键过期和清理

```go
// ✅ Good Example | 良好示例
func NewRedisClient(config RedisConfig) *redis.Client {
    return redis.NewClient(&redis.Options{
        Addr:         config.Addr,
        Password:     config.Password,
        DB:           config.DB,
        PoolSize:     config.PoolSize,
        MinIdleConns: config.MinIdleConns,
        MaxRetries:   config.MaxRetries,
    })
}
```

### WebSocket Handling | WebSocket 处理

#### English | 英文
- [ ] **Connection Lifecycle**: Proper WebSocket connection management
- [ ] **Message Routing**: Correct message routing to clients
- [ ] **Error Handling**: Graceful handling of connection errors
- [ ] **Rate Limiting**: WebSocket rate limiting implemented
- [ ] **Authentication**: WebSocket authentication working

#### Chinese | 中文
- [ ] **连接生命周期**: 正确的 WebSocket 连接管理
- [ ] **消息路由**: 正确的消息路由到客户端
- [ ] **错误处理**: 优雅处理连接错误
- [ ] **速率限制**: 实现 WebSocket 速率限制
- [ ] **身份验证**: WebSocket 身份验证正常工作

```go
// ✅ Good Example | 良好示例
func (h *Handler) HandleWebSocket(c *gin.Context) {
    conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
    if err != nil {
        h.logger.WithError(err).Error("Failed to upgrade connection")
        return
    }
    defer conn.Close()

    // Handle connection lifecycle
}
```

### Rate Limiting | 速率限制

#### English | 英文
- [ ] **Algorithm Implementation**: Token bucket algorithm correctly implemented
- [ ] **Configuration**: Rate limits properly configured per action type
- [ ] **Redis Scripts**: Lua scripts for atomic operations
- [ ] **Error Handling**: Proper handling of rate limit exceeded
- [ ] **Monitoring**: Rate limiting metrics collected

#### Chinese | 中文
- [ ] **算法实现**: 令牌桶算法正确实现
- [ ] **配置**: 每种操作类型的速率限制正确配置
- [ ] **Redis 脚本**: 用于原子操作的 Lua 脚本
- [ ] **错误处理**: 正确处理速率限制超出
- [ ] **监控**: 收集速率限制指标

```lua
-- ✅ Good Example | 良好示例
-- Rate limiting Lua script
local key = KEYS[1]
local limit = tonumber(ARGV[1])
local window = tonumber(ARGV[2])
local current = redis.call('GET', key)

if current == false then
    redis.call('SET', key, 1)
    redis.call('EXPIRE', key, window)
    return 1
end

if tonumber(current) < limit then
    return redis.call('INCR', key)
else
    return 0
end
```

### Authentication & Authorization | 身份验证和授权

#### English | 英文
- [ ] **JWT Validation**: Proper JWT token validation
- [ ] **Role Checking**: User roles are properly validated
- [ ] **Permission System**: Permission-based access control
- [ ] **Token Expiration**: Token expiration properly handled
- [ ] **Security Headers**: Appropriate security headers set

#### Chinese | 中文
- [ ] **JWT 验证**: 正确的 JWT 令牌验证
- [ ] **角色检查**: 用户角色得到正确验证
- [ ] **权限系统**: 基于权限的访问控制
- [ ] **令牌过期**: 正确处理令牌过期
- [ ] **安全头**: 设置适当的安全头

```go
// ✅ Good Example | 良好示例
func (a *AuthService) ValidateUserRole(userInfo *port.UserInfo, requiredRole model.UserRole) bool {
    switch requiredRole {
    case model.RoleAdmin:
        return userInfo.Role == model.RoleAdmin
    case model.RoleModerator:
        return userInfo.Role == model.RoleAdmin || userInfo.Role == model.RoleModerator
    case model.RoleStreamer:
        return userInfo.Role != model.RoleViewer
    default:
        return true // RoleViewer or any authenticated user
    }
}
```

---

## Pre-Commit Checklist | 提交前检查清单

### Code Quality | 代码质量

#### English | 英文
- [ ] All tests pass locally (`go test ./...`)
- [ ] Code is properly formatted (`gofmt -s -w .`)
- [ ] No race conditions (`go test -race ./...`)
- [ ] No linting errors (`golangci-lint run`)
- [ ] No debug statements or TODO comments in production code

#### Chinese | 中文
- [ ] 所有测试在本地通过 (`go test ./...`)
- [ ] 代码格式正确 (`gofmt -s -w .`)
- [ ] 无竞态条件 (`go test -race ./...`)
- [ ] 无代码检查错误 (`golangci-lint run`)
- [ ] 生产代码中无调试语句或 TODO 注释

### Documentation | 文档

#### English | 英文
- [ ] Public functions have godoc comments
- [ ] README.md updated if needed
- [ ] Architecture documentation updated
- [ ] API documentation current
- [ ] Changelog updated for significant changes

#### Chinese | 中文
- [ ] 公共函数有 godoc 注释
- [ ] 如需要已更新 README.md
- [ ] 架构文档已更新
- [ ] API 文档是最新的
- [ ] 重大更改已更新变更日志

### Git Practices | Git 实践

#### English | 英文
- [ ] Commit message follows conventional format
- [ ] Changes are logically grouped in commits
- [ ] No sensitive information in commit history
- [ ] Branch is up-to-date with main/master
- [ ] Merge conflicts resolved properly

#### Chinese | 中文
- [ ] 提交消息遵循约定格式
- [ ] 更改在提交中逻辑分组
- [ ] 提交历史中无敏感信息
- [ ] 分支与 main/master 保持最新
- [ ] 合并冲突正确解决

```bash
# ✅ Good Commit Message | 良好的提交消息
feat(rate-limiter): add token bucket algorithm for Redis rate limiting

- Implement token bucket algorithm using Lua scripts
- Add configuration for different action types
- Include comprehensive test coverage
- Update documentation with usage examples

Closes #123
```

---

## Release Checklist | 发布检查清单

### Pre-Release | 发布前

#### English | 英文
- [ ] All quality gates passed in CI/CD
- [ ] Performance benchmarks meet requirements
- [ ] Security scan completed without critical issues
- [ ] Load testing completed successfully
- [ ] Database migrations tested

#### Chinese | 中文
- [ ] CI/CD 中所有质量门禁通过
- [ ] 性能基准满足要求
- [ ] 安全扫描完成，无关键问题
- [ ] 负载测试成功完成
- [ ] 数据库迁移已测试

### Release Process | 发布流程

#### English | 英文
- [ ] Version tagged appropriately (semantic versioning)
- [ ] Release notes prepared
- [ ] Deployment scripts tested
- [ ] Rollback plan prepared
- [ ] Monitoring alerts configured

#### Chinese | 中文
- [ ] 版本标记适当（语义版本控制）
- [ ] 发布说明已准备
- [ ] 部署脚本已测试
- [ ] 回滚计划已准备
- [ ] 监控警报已配置

### Post-Release | 发布后

#### English | 英文
- [ ] Service health checks passing
- [ ] Performance metrics within expected ranges
- [ ] Error rates within acceptable limits
- [ ] User feedback monitored
- [ ] Documentation updated in production

#### Chinese | 中文
- [ ] 服务健康检查通过
- [ ] 性能指标在预期范围内
- [ ] 错误率在可接受限制内
- [ ] 用户反馈已监控
- [ ] 生产环境文档已更新

---

## Tools and Commands | 工具和命令

### Development Tools | 开发工具

```bash
# Code formatting | 代码格式化
gofmt -s -w .
goimports -w .

# Testing | 测试
go test ./...
go test -race ./...
go test -cover ./...
go test -bench=. ./...

# Quality checks | 质量检查
go vet ./...
golangci-lint run
gosec ./...

# Dependencies | 依赖管理
go mod tidy
go mod verify
go mod download

# Build | 构建
go build ./...
go build -race ./...
```

### Useful Links | 有用链接

#### English | 英文
- [Go Code Review Comments](https://github.com/golang/go/wiki/CodeReviewComments)
- [Effective Go](https://golang.org/doc/effective_go.html)
- [Go Testing](https://golang.org/pkg/testing/)
- [Redis Best Practices](https://redis.io/docs/manual/patterns/)
- [WebSocket RFC](https://tools.ietf.org/html/rfc6455)

#### Chinese | 中文
- [Go 代码审查注释](https://github.com/golang/go/wiki/CodeReviewComments)
- [高效 Go 编程](https://golang.org/doc/effective_go.html)
- [Go 测试](https://golang.org/pkg/testing/)
- [Redis 最佳实践](https://redis.io/docs/manual/patterns/)
- [WebSocket RFC](https://tools.ietf.org/html/rfc6455)

---

*This checklist should be reviewed and updated regularly to reflect current best practices and project requirements.*

*此检查清单应定期审查和更新，以反映当前的最佳实践和项目要求。*