/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package integration

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"cina.club/services/live-im-service/internal/adapter/redis"
	"cina.club/services/live-im-service/internal/adapter/serializer"
	"cina.club/services/live-im-service/internal/domain/model"
)

// TestRedisIntegration tests Redis integration with real Redis instance
func TestRedisIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Setup Redis client
	redisClient := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       15, // Use test database
	})

	// Test Redis connectivity
	ctx := context.Background()
	err := redisClient.Ping(ctx).Err()
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}

	// Clean up test data
	defer func() {
		redisClient.FlushDB(ctx)
		redisClient.Close()
	}()

	logger := logrus.New()
	logger.SetLevel(logrus.WarnLevel) // Reduce noise in tests

	t.Run("Broadcaster Integration", func(t *testing.T) {
		testBroadcasterIntegration(t, redisClient, logger)
	})

	t.Run("RoomStore Integration", func(t *testing.T) {
		testRoomStoreIntegration(t, redisClient, logger)
	})

	t.Run("RateLimiter Integration", func(t *testing.T) {
		testRateLimiterIntegration(t, redisClient, logger)
	})
}

func testBroadcasterIntegration(t *testing.T, redisClient *redis.Client, logger *logrus.Logger) {
	serializer := serializer.NewJSONSerializer()
	broadcaster := redis.NewBroadcaster(redisClient, serializer, logger)

	ctx := context.Background()
	roomID := "test-room-" + uuid.New().String()

	// Test message broadcasting
	testMessage := model.Message{
		MessageID: uuid.New().String(),
		Type:      model.MessageTypeBarrage,
		RoomID:    roomID,
		Content:   "Integration test message",
		Timestamp: time.Now(),
		FromUser: &model.User{
			UserID:   uuid.New(),
			Username: "testuser",
			Role:     model.RoleViewer,
		},
	}

	// Subscribe to messages
	messageReceived := make(chan model.Message, 1)
	err := broadcaster.Subscribe(ctx, roomID, func(msg model.Message) {
		messageReceived <- msg
	})
	require.NoError(t, err)

	// Give subscription time to establish
	time.Sleep(100 * time.Millisecond)

	// Broadcast message
	err = broadcaster.Broadcast(ctx, roomID, testMessage)
	require.NoError(t, err)

	// Wait for message
	select {
	case receivedMsg := <-messageReceived:
		assert.Equal(t, testMessage.MessageID, receivedMsg.MessageID)
		assert.Equal(t, testMessage.Type, receivedMsg.Type)
		assert.Equal(t, testMessage.Content, receivedMsg.Content)
		assert.Equal(t, testMessage.RoomID, receivedMsg.RoomID)
		assert.NotNil(t, receivedMsg.FromUser)
		assert.Equal(t, testMessage.FromUser.Username, receivedMsg.FromUser.Username)
	case <-time.After(5 * time.Second):
		t.Fatal("Message not received within timeout")
	}

	// Clean up
	broadcaster.Unsubscribe(roomID)
}

func testRoomStoreIntegration(t *testing.T, redisClient *redis.Client, logger *logrus.Logger) {
	roomStore := redis.NewRoomStore(redisClient, logger)

	ctx := context.Background()
	roomID := "test-room-" + uuid.New().String()
	userID1 := uuid.New()
	userID2 := uuid.New()

	// Test user subscription
	err := roomStore.Subscribe(ctx, userID1, roomID)
	require.NoError(t, err)

	err = roomStore.Subscribe(ctx, userID2, roomID)
	require.NoError(t, err)

	// Test room member retrieval
	members, err := roomStore.GetRoomMembers(ctx, roomID)
	require.NoError(t, err)
	assert.Len(t, members, 2)
	assert.Contains(t, members, userID1)
	assert.Contains(t, members, userID2)

	// Test user room retrieval
	rooms, err := roomStore.GetUserRooms(ctx, userID1)
	require.NoError(t, err)
	assert.Contains(t, rooms, roomID)

	// Test user in room check
	inRoom, err := roomStore.IsUserInRoom(ctx, userID1, roomID)
	require.NoError(t, err)
	assert.True(t, inRoom)

	// Test room count
	count, err := roomStore.GetRoomCount(ctx, roomID)
	require.NoError(t, err)
	assert.Equal(t, 2, count)

	// Test unsubscription
	err = roomStore.Unsubscribe(ctx, userID1, roomID)
	require.NoError(t, err)

	// Verify user is no longer in room
	inRoom, err = roomStore.IsUserInRoom(ctx, userID1, roomID)
	require.NoError(t, err)
	assert.False(t, inRoom)

	// Verify room count decreased
	count, err = roomStore.GetRoomCount(ctx, roomID)
	require.NoError(t, err)
	assert.Equal(t, 1, count)

	// Clean up
	roomStore.Unsubscribe(ctx, userID2, roomID)
}

func testRateLimiterIntegration(t *testing.T, redisClient *redis.Client, logger *logrus.Logger) {
	config := &redis.RateLimiterConfig{
		Enabled:    true,
		BarrageRPS: 2,
		LikeRPS:    5,
		GiftRPS:    1,
		WindowSize: time.Second,
		MaxBurst:   3,
	}

	rateLimiter := redis.NewRedisRateLimiter(redisClient, config, logger)

	ctx := context.Background()
	userID := uuid.New()

	// Test rate limiting for barrage messages
	// Should allow first few requests within burst limit
	for i := 0; i < 3; i++ {
		allowed, err := rateLimiter.Allow(ctx, userID, "barrage")
		require.NoError(t, err)
		assert.True(t, allowed, "Request %d should be allowed within burst limit", i+1)
	}

	// Next request should be rate limited
	allowed, err := rateLimiter.Allow(ctx, userID, "barrage")
	require.NoError(t, err)
	assert.False(t, allowed, "Request should be rate limited after burst")

	// Test different action types
	allowed, err = rateLimiter.Allow(ctx, userID, "like")
	require.NoError(t, err)
	assert.True(t, allowed, "Like action should be allowed (different rate limit)")

	// Test rate limit reset
	remaining, err := rateLimiter.GetRemaining(ctx, userID, "barrage")
	require.NoError(t, err)
	assert.GreaterOrEqual(t, remaining, 0)

	// Test reset functionality
	err = rateLimiter.Reset(ctx, userID, "barrage")
	require.NoError(t, err)

	// Should be allowed again after reset
	allowed, err = rateLimiter.Allow(ctx, userID, "barrage")
	require.NoError(t, err)
	assert.True(t, allowed, "Request should be allowed after reset")
}

// TestRedisFailover tests Redis connection failure scenarios
func TestRedisFailover(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Test with invalid Redis configuration
	redisClient := redis.NewClient(&redis.Options{
		Addr:     "localhost:9999", // Invalid port
		Password: "",
		DB:       0,
	})
	defer redisClient.Close()

	logger := logrus.New()
	logger.SetLevel(logrus.WarnLevel)

	ctx := context.Background()

	t.Run("Broadcaster Failover", func(t *testing.T) {
		serializer := serializer.NewJSONSerializer()
		broadcaster := redis.NewBroadcaster(redisClient, serializer, logger)

		// Should handle connection failure gracefully
		err := broadcaster.Subscribe(ctx, "test-room", func(msg model.Message) {})
		assert.Error(t, err, "Should return error for failed Redis connection")

		testMessage := model.Message{
			MessageID: uuid.New().String(),
			Type:      model.MessageTypeBarrage,
			Content:   "Test message",
		}

		err = broadcaster.Broadcast(ctx, "test-room", testMessage)
		assert.Error(t, err, "Should return error for failed Redis connection")
	})

	t.Run("RoomStore Failover", func(t *testing.T) {
		roomStore := redis.NewRoomStore(redisClient, logger)

		userID := uuid.New()
		roomID := "test-room"

		// Should handle connection failure gracefully
		err := roomStore.Subscribe(ctx, userID, roomID)
		assert.Error(t, err, "Should return error for failed Redis connection")

		_, err = roomStore.GetRoomMembers(ctx, roomID)
		assert.Error(t, err, "Should return error for failed Redis connection")
	})

	t.Run("RateLimiter Failover", func(t *testing.T) {
		config := &redis.RateLimiterConfig{
			Enabled:    true,
			BarrageRPS: 1,
			WindowSize: time.Second,
			MaxBurst:   1,
		}

		rateLimiter := redis.NewRedisRateLimiter(redisClient, config, logger)

		userID := uuid.New()

		// Should handle connection failure gracefully
		allowed, err := rateLimiter.Allow(ctx, userID, "barrage")
		assert.Error(t, err, "Should return error for failed Redis connection")
		assert.False(t, allowed, "Should deny requests when Redis is unavailable")
	})
}
