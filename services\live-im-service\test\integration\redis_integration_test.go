/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package integration

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestRedisIntegration tests Redis integration with real Redis instance
func TestRedisIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Setup Redis client
	redisClient := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       15, // Use test database
	})

	// Test Redis connectivity
	ctx := context.Background()
	err := redisClient.Ping(ctx).Err()
	if err != nil {
		t.Skipf("Redis not available: %v", err)
	}

	// Clean up test data
	defer func() {
		redisClient.FlushDB(ctx)
		redisClient.Close()
	}()

	logger := logrus.New()
	logger.SetLevel(logrus.WarnLevel) // Reduce noise in tests

	t.Run("Broadcaster Integration", func(t *testing.T) {
		testBroadcasterIntegration(t, redisClient, logger)
	})

	t.Run("RoomStore Integration", func(t *testing.T) {
		testRoomStoreIntegration(t, redisClient, logger)
	})

	t.Run("RateLimiter Integration", func(t *testing.T) {
		testRateLimiterIntegration(t, redisClient, logger)
	})
}

func testBroadcasterIntegration(t *testing.T, redisClient *redis.Client, logger *logrus.Logger) {
	// Test basic Redis pub/sub functionality
	ctx := context.Background()
	roomID := "test-room-" + uuid.New().String()

	// Test Redis pub/sub directly
	pubsub := redisClient.Subscribe(ctx, roomID)
	defer pubsub.Close()

	// Test publishing a message
	testData := `{"message_id":"test-123","type":"barrage","content":"test message"}`
	err := redisClient.Publish(ctx, roomID, testData).Err()
	require.NoError(t, err)

	// Test receiving the message
	msg, err := pubsub.ReceiveMessage(ctx)
	require.NoError(t, err)
	assert.Equal(t, roomID, msg.Channel)
	assert.Equal(t, testData, msg.Payload)

	t.Log("Redis pub/sub functionality working correctly")
}

func testRoomStoreIntegration(t *testing.T, redisClient *redis.Client, logger *logrus.Logger) {
	// Test Redis set operations for room membership
	ctx := context.Background()
	roomID := "test-room-" + uuid.New().String()
	userID1 := uuid.New().String()
	userID2 := uuid.New().String()

	// Test adding users to room set
	err := redisClient.SAdd(ctx, "room:"+roomID+":members", userID1, userID2).Err()
	require.NoError(t, err)

	// Test getting room members
	members, err := redisClient.SMembers(ctx, "room:"+roomID+":members").Result()
	require.NoError(t, err)
	assert.Len(t, members, 2)
	assert.Contains(t, members, userID1)
	assert.Contains(t, members, userID2)

	// Test checking membership
	isMember, err := redisClient.SIsMember(ctx, "room:"+roomID+":members", userID1).Result()
	require.NoError(t, err)
	assert.True(t, isMember)

	// Test getting member count
	count, err := redisClient.SCard(ctx, "room:"+roomID+":members").Result()
	require.NoError(t, err)
	assert.Equal(t, int64(2), count)

	// Test removing member
	err = redisClient.SRem(ctx, "room:"+roomID+":members", userID1).Err()
	require.NoError(t, err)

	// Verify member removed
	count, err = redisClient.SCard(ctx, "room:"+roomID+":members").Result()
	require.NoError(t, err)
	assert.Equal(t, int64(1), count)

	// Clean up
	redisClient.Del(ctx, "room:"+roomID+":members")

	t.Log("Redis set operations for room store working correctly")
}

func testRateLimiterIntegration(t *testing.T, redisClient *redis.Client, logger *logrus.Logger) {
	// Test Redis operations for rate limiting using basic Redis commands
	ctx := context.Background()
	userID := uuid.New().String()
	key := "rate_limit:" + userID + ":barrage"

	// Test setting rate limit counter
	err := redisClient.Set(ctx, key, 1, time.Minute).Err()
	require.NoError(t, err)

	// Test incrementing counter
	count, err := redisClient.Incr(ctx, key).Result()
	require.NoError(t, err)
	assert.Equal(t, int64(2), count)

	// Test getting current count
	currentCount, err := redisClient.Get(ctx, key).Int64()
	require.NoError(t, err)
	assert.Equal(t, int64(2), currentCount)

	// Test TTL
	ttl, err := redisClient.TTL(ctx, key).Result()
	require.NoError(t, err)
	assert.Greater(t, ttl, time.Duration(0))

	// Test deletion (reset)
	err = redisClient.Del(ctx, key).Err()
	require.NoError(t, err)

	// Verify key is deleted
	exists, err := redisClient.Exists(ctx, key).Result()
	require.NoError(t, err)
	assert.Equal(t, int64(0), exists)

	t.Log("Redis rate limiting operations working correctly")
}

// TestRedisFailover tests Redis connection failure scenarios
func TestRedisFailover(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	// Test with invalid Redis configuration
	redisClient := redis.NewClient(&redis.Options{
		Addr:     "localhost:9999", // Invalid port
		Password: "",
		DB:       0,
	})
	defer redisClient.Close()

	ctx := context.Background()

	t.Run("Basic Redis Connection Failure", func(t *testing.T) {
		// Test basic Redis operations with invalid connection
		err := redisClient.Ping(ctx).Err()
		assert.Error(t, err, "Should return error for failed Redis connection")

		// Test set operation
		err = redisClient.Set(ctx, "test-key", "test-value", time.Minute).Err()
		assert.Error(t, err, "Should return error for failed Redis connection")

		// Test get operation
		_, err = redisClient.Get(ctx, "test-key").Result()
		assert.Error(t, err, "Should return error for failed Redis connection")

		t.Log("Redis connection failure handling working correctly")
	})
}
