/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package circuitbreaker

import (
	"context"
	"errors"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestManager_GetOrCreate(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	config := ManagerConfig{
		DefaultConfig: Config{
			MaxRequests: 5,
			Interval:    60 * time.Second,
			Timeout:     30 * time.Second,
		},
	}

	manager := NewManager(config, logger)
	defer manager.Close()

	t.Run("Create new circuit breaker", func(t *testing.T) {
		cb := manager.GetOrCreate("test-service")
		assert.NotNil(t, cb)
		assert.Equal(t, "test-service", cb.Name())
		assert.Equal(t, StateClosed, cb.State())
	})

	t.Run("Get existing circuit breaker", func(t *testing.T) {
		cb1 := manager.GetOrCreate("test-service")
		cb2 := manager.GetOrCreate("test-service")
		assert.Equal(t, cb1, cb2) // Should be the same instance
	})

	t.Run("Create with custom config", func(t *testing.T) {
		customConfig := Config{
			MaxRequests: 10,
			Interval:    120 * time.Second,
			Timeout:     60 * time.Second,
		}
		cb := manager.GetOrCreate("custom-service", customConfig)
		assert.NotNil(t, cb)
		assert.Equal(t, "custom-service", cb.Name())
	})
}

func TestManager_Execute(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	config := ManagerConfig{
		DefaultConfig: Config{
			MaxRequests: 3,
			Timeout:     50 * time.Millisecond,
			ReadyToTrip: func(counts Counts) bool {
				return counts.ConsecutiveFailures >= 2
			},
		},
	}

	manager := NewManager(config, logger)
	defer manager.Close()

	t.Run("Successful execution", func(t *testing.T) {
		err := manager.Execute("test-service", func() error {
			return nil
		})
		assert.NoError(t, err)
	})

	t.Run("Failed execution", func(t *testing.T) {
		testErr := errors.New("test error")
		err := manager.Execute("test-service", func() error {
			return testErr
		})
		assert.Equal(t, testErr, err)
	})

	t.Run("Circuit breaker protection", func(t *testing.T) {
		testErr := errors.New("test error")

		// Force circuit to open - need more failures based on default config
		for i := 0; i < 10; i++ {
			manager.Execute("failing-service", func() error {
				return testErr
			})
		}

		// Next request should be rejected
		err := manager.Execute("failing-service", func() error {
			return nil
		})
		assert.Equal(t, ErrOpenState, err)
	})
}

func TestManager_ExecuteWithContext(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	config := ManagerConfig{
		DefaultConfig: Config{
			MaxRequests: 3,
			Timeout:     50 * time.Millisecond,
		},
	}

	manager := NewManager(config, logger)
	defer manager.Close()

	t.Run("Context cancellation", func(t *testing.T) {
		ctx, cancel := context.WithCancel(context.Background())
		cancel()

		err := manager.ExecuteWithContext(ctx, "test-service", func(ctx context.Context) error {
			select {
			case <-ctx.Done():
				return ctx.Err()
			case <-time.After(100 * time.Millisecond):
				return nil
			}
		})

		assert.Equal(t, context.Canceled, err)
	})
}

func TestManager_GetStats(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	config := ManagerConfig{
		DefaultConfig: Config{
			MaxRequests: 3,
			Timeout:     50 * time.Millisecond,
		},
	}

	manager := NewManager(config, logger)
	defer manager.Close()

	t.Run("Get statistics", func(t *testing.T) {
		// Execute some requests
		manager.Execute("service1", func() error { return nil })
		manager.Execute("service1", func() error { return errors.New("error") })
		manager.Execute("service2", func() error { return nil })

		stats := manager.GetStats()
		assert.Len(t, stats, 2)

		service1Stats := stats["service1"]
		assert.Equal(t, "service1", service1Stats.Name)
		assert.Equal(t, uint32(2), service1Stats.Requests)
		assert.Equal(t, uint32(1), service1Stats.TotalSuccesses)
		assert.Equal(t, uint32(1), service1Stats.TotalFailures)
		assert.Equal(t, 0.5, service1Stats.SuccessRate)

		service2Stats := stats["service2"]
		assert.Equal(t, "service2", service2Stats.Name)
		assert.Equal(t, uint32(1), service2Stats.Requests)
		assert.Equal(t, uint32(1), service2Stats.TotalSuccesses)
		assert.Equal(t, uint32(0), service2Stats.TotalFailures)
		assert.Equal(t, 1.0, service2Stats.SuccessRate)
	})
}

func TestManager_Reset(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	config := ManagerConfig{
		DefaultConfig: Config{
			MaxRequests: 3,
			Timeout:     50 * time.Millisecond,
			ReadyToTrip: func(counts Counts) bool {
				return counts.ConsecutiveFailures >= 2
			},
		},
	}

	manager := NewManager(config, logger)
	defer manager.Close()

	t.Run("Reset circuit breaker", func(t *testing.T) {
		testErr := errors.New("test error")

		// Force circuit to open - need more failures based on default config
		for i := 0; i < 10; i++ {
			manager.Execute("test-service", func() error {
				return testErr
			})
		}

		cb, exists := manager.GetBreaker("test-service")
		require.True(t, exists)
		assert.Equal(t, StateOpen, cb.State())

		// Reset the circuit breaker
		err := manager.Reset("test-service")
		assert.NoError(t, err)

		cb, exists = manager.GetBreaker("test-service")
		require.True(t, exists)
		assert.Equal(t, StateClosed, cb.State())

		// Should be able to execute requests again
		err = manager.Execute("test-service", func() error {
			return nil
		})
		assert.NoError(t, err)
	})

	t.Run("Reset non-existent circuit breaker", func(t *testing.T) {
		err := manager.Reset("non-existent")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "not found")
	})
}

func TestManager_WithMetrics(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	metrics := NewDefaultMetricsCollector(logger)
	config := ManagerConfig{
		DefaultConfig: Config{
			MaxRequests: 3,
			Timeout:     50 * time.Millisecond,
		},
		MetricsCollector: metrics,
	}

	manager := NewManager(config, logger)
	defer manager.Close()

	t.Run("Metrics are collected", func(t *testing.T) {
		// This test mainly ensures no panics occur when metrics are enabled
		err := manager.Execute("test-service", func() error {
			return nil
		})
		assert.NoError(t, err)

		err = manager.Execute("test-service", func() error {
			return errors.New("test error")
		})
		assert.Error(t, err)
	})
}

func TestManager_PredefinedServices(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	serviceConfigs := map[string]Config{
		"redis": {
			MaxRequests: 5,
			Timeout:     30 * time.Second,
		},
		"database": {
			MaxRequests: 10,
			Timeout:     60 * time.Second,
		},
	}

	config := ManagerConfig{
		DefaultConfig:  Config{MaxRequests: 3},
		ServiceConfigs: serviceConfigs,
	}

	manager := NewManager(config, logger)
	defer manager.Close()

	t.Run("Predefined services are created", func(t *testing.T) {
		cb, exists := manager.GetBreaker("redis")
		assert.True(t, exists)
		assert.Equal(t, "redis", cb.Name())

		cb, exists = manager.GetBreaker("database")
		assert.True(t, exists)
		assert.Equal(t, "database", cb.Name())
	})

	t.Run("Get all breakers includes predefined", func(t *testing.T) {
		breakers := manager.GetAllBreakers()
		assert.Len(t, breakers, 2)
		assert.Contains(t, breakers, "redis")
		assert.Contains(t, breakers, "database")
	})
}

func TestDefaultMetricsCollector(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	collector := NewDefaultMetricsCollector(logger)

	t.Run("All methods work without panic", func(t *testing.T) {
		assert.NotPanics(t, func() {
			collector.RecordRequest("test", StateClosed)
			collector.RecordSuccess("test", 100*time.Millisecond)
			collector.RecordFailure("test", 200*time.Millisecond, errors.New("test"))
			collector.RecordStateChange("test", StateClosed, StateOpen)
		})
	})
}

func BenchmarkManager_Execute(b *testing.B) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	config := ManagerConfig{
		DefaultConfig: Config{
			MaxRequests: 10,
		},
	}

	manager := NewManager(config, logger)
	defer manager.Close()

	b.Run("Successful execution", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			manager.Execute("benchmark-service", func() error {
				return nil
			})
		}
	})

	b.Run("Failed execution", func(b *testing.B) {
		testErr := errors.New("benchmark error")
		for i := 0; i < b.N; i++ {
			manager.Execute("benchmark-service-fail", func() error {
				return testErr
			})
		}
	})
}
