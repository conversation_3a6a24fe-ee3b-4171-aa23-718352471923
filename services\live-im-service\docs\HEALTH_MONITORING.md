# Health Checks and Monitoring Guide

This document describes the comprehensive health check and monitoring system implemented in the Live IM Service.

## Overview

The Live IM Service implements a robust health check and monitoring system that provides:

- **Multi-level Health Checks**: Application, dependency, and infrastructure health monitoring
- **Kubernetes-ready Endpoints**: Liveness and readiness probes for container orchestration
- **Prometheus Metrics**: Comprehensive metrics collection for observability
- **Real-time Monitoring**: HTTP endpoints for real-time health status
- **Alerting Integration**: Health status changes for monitoring systems
- **Performance Tracking**: Response times, throughput, and resource utilization

## Health Check System

### Core Components

1. **Health Service**: Centralized health check management
2. **Health Checkers**: Individual component health validators
3. **HTTP Handler**: REST API for health status
4. **Metrics Collector**: Prometheus metrics integration

### Health Status Levels

- **Healthy**: Component is fully operational
- **Degraded**: Component is functional but with reduced performance
- **Unhealthy**: Component is not functioning properly
- **Unknown**: Component status cannot be determined

## Health Check Endpoints

### Comprehensive Health Check

```
GET /health
```

Returns detailed health status of all components:

```json
{
  "status": "healthy",
  "message": "All 5 health checks are healthy",
  "timestamp": "2025-07-11T10:00:00Z",
  "uptime": "3600s",
  "version": "1.0.0",
  "environment": "production",
  "checks": {
    "redis": {
      "name": "redis",
      "status": "healthy",
      "message": "Redis is healthy",
      "duration": "5ms",
      "timestamp": "2025-07-11T10:00:00Z",
      "details": {
        "ping_duration_ms": 2,
        "set_duration_ms": 1,
        "get_duration_ms": 2,
        "total_duration_ms": 5
      }
    },
    "hub": {
      "name": "hub",
      "status": "healthy",
      "message": "Hub is healthy",
      "duration": "1ms",
      "details": {
        "connected_clients": 150,
        "active_rooms": 25,
        "messages_per_second": 45.2
      }
    }
  },
  "summary": {
    "total": 5,
    "healthy": 5,
    "degraded": 0,
    "unhealthy": 0,
    "unknown": 0
  }
}
```

### Liveness Probe

```
GET /health/live
```

Simple liveness check for Kubernetes:

```json
{
  "status": "alive",
  "timestamp": "2025-07-11T10:00:00Z",
  "uptime": "3600s"
}
```

### Readiness Probe

```
GET /health/ready
```

Readiness check for critical dependencies:

```json
{
  "status": "ready",
  "timestamp": "2025-07-11T10:00:00Z",
  "checks": {
    "redis": {
      "status": "healthy",
      "message": "Redis is healthy"
    }
  }
}
```

### Individual Health Checks

```
GET /health/check/{name}
```

Check specific component health:

```json
{
  "name": "redis",
  "status": "healthy",
  "message": "Redis is healthy",
  "duration": "5ms",
  "timestamp": "2025-07-11T10:00:00Z",
  "details": {
    "ping_duration_ms": 2,
    "operation_success": true
  }
}
```

## Built-in Health Checkers

### Redis Health Checker

Monitors Redis connectivity and performance:

- **Connectivity**: PING command response
- **Read/Write**: SET/GET operations
- **Performance**: Operation latency thresholds
- **Data Integrity**: Value verification

```go
redisChecker := health.NewRedisHealthChecker(
    redisClient,
    5*time.Second,  // timeout
    true,           // critical
    logger,
)
```

### Hub Health Checker

Monitors the Hub service status:

- **Statistics**: Connected clients, active rooms
- **Performance**: Messages per second, broadcasts per second
- **Responsiveness**: Hub API response times

```go
hubChecker := health.NewHubHealthChecker(
    hubService,
    3*time.Second,  // timeout
    true,           // critical
    logger,
)
```

### HTTP Service Health Checker

Monitors external HTTP services:

- **Connectivity**: HTTP request success
- **Response Time**: Latency monitoring
- **Status Codes**: HTTP response validation

```go
liveAPIChecker := health.NewHTTPServiceHealthChecker(
    "live_api",
    "https://api.live.cina.club/health",
    10*time.Second, // timeout
    true,           // critical
    logger,
)
```

### Auth Service Health Checker

Monitors authentication service:

- **Responsiveness**: Token validation response time
- **Availability**: Service connectivity

```go
authChecker := health.NewAuthServiceHealthChecker(
    authService,
    5*time.Second,  // timeout
    true,           // critical
    logger,
)
```

### Application Health Checker

Monitors the application itself:

- **Uptime**: Application runtime
- **Stability**: Startup completion status
- **Version**: Application version information

```go
appChecker := health.NewApplicationHealthChecker(
    startTime,
    "1.0.0",        // version
    1*time.Second,  // timeout
    false,          // not critical
    logger,
)
```

## Prometheus Metrics

### HTTP Metrics

- `http_requests_total`: Total HTTP requests by method, endpoint, status
- `http_request_duration_seconds`: Request duration histogram
- `http_request_size_bytes`: Request size histogram
- `http_response_size_bytes`: Response size histogram
- `http_requests_in_flight`: Current in-flight requests

### WebSocket Metrics

- `websocket_connections_total`: Total WebSocket connections
- `websocket_connections_active`: Active connections by room
- `websocket_messages_total`: Total messages by type and direction
- `websocket_message_size_bytes`: Message size histogram
- `websocket_connection_duration_seconds`: Connection duration

### Hub Metrics

- `hub_clients_connected`: Current connected clients
- `hub_active_rooms`: Number of active rooms
- `hub_messages_total`: Total messages by type
- `hub_broadcasts_total`: Total broadcasts by room
- `hub_messages_per_second`: Current message rate
- `hub_broadcasts_per_second`: Current broadcast rate

### Room Metrics

- `room_members_total`: Members per room
- `room_messages_total`: Messages per room and type
- `room_joins_total`: Room joins by room
- `room_leaves_total`: Room leaves by room

### Message Processing Metrics

- `message_processing_duration_seconds`: Processing time by type and status
- `message_validation_total`: Validation results by type
- `message_dropped_total`: Dropped messages by reason

### External Service Metrics

- `redis_operations_total`: Redis operations by type and status
- `redis_operation_duration_seconds`: Redis operation duration
- `external_service_calls_total`: External service calls
- `external_service_duration_seconds`: External service call duration

### System Metrics

- `system_uptime_seconds`: System uptime
- `memory_usage_bytes`: Memory usage
- `goroutines_active`: Active goroutines

## Configuration

### Health Service Configuration

```go
healthConfig := health.Config{
    Version:     "1.0.0",
    Environment: "production",
    Timeout:     30 * time.Second,
}

healthService := health.NewService(healthConfig, logger)
```

### Registering Health Checkers

```go
// Register critical checkers
healthService.RegisterChecker(redisChecker)
healthService.RegisterChecker(hubChecker)
healthService.RegisterChecker(authChecker)

// Register non-critical checkers
healthService.RegisterChecker(appChecker)
```

### HTTP Handler Setup

```go
healthHandler := health.NewHTTPHandler(healthService, logger)

// Register routes
api := router.Group("/api/v1")
healthHandler.RegisterRoutes(api)

// Add middleware
router.Use(healthHandler.Middleware())
```

### Metrics Setup

```go
metricsManager := metrics.NewMetricsManager(logger, startTime)

// Add HTTP metrics middleware
router.Use(metricsManager.GetHTTPMiddleware().Middleware())

// Expose Prometheus metrics
router.GET("/metrics", metricsManager.GetHTTPMiddleware().PrometheusHandler())
```

## Integration Examples

### Kubernetes Deployment

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: live-im-service
spec:
  template:
    spec:
      containers:
      - name: live-im-service
        image: live-im-service:latest
        ports:
        - containerPort: 8080
        livenessProbe:
          httpGet:
            path: /api/v1/health/live
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /api/v1/health/ready
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
```

### Prometheus Configuration

```yaml
scrape_configs:
  - job_name: 'live-im-service'
    static_configs:
      - targets: ['live-im-service:8080']
    metrics_path: /metrics
    scrape_interval: 15s
```

### Grafana Dashboard

Key metrics to monitor:

- **Service Health**: Overall health status and component status
- **Performance**: Request latency, message processing time
- **Throughput**: Messages per second, requests per second
- **Errors**: Error rates, failed health checks
- **Resources**: Memory usage, goroutines, connection counts

### Alerting Rules

```yaml
groups:
  - name: live-im-service
    rules:
      - alert: ServiceUnhealthy
        expr: health_check_status{checker="redis"} == 3
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Live IM Service is unhealthy"
          
      - alert: HighErrorRate
        expr: rate(http_requests_total{status_code=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
```

## Best Practices

### Health Check Design

1. **Keep Checks Fast**: Health checks should complete quickly (< 5 seconds)
2. **Test Real Dependencies**: Check actual connectivity, not just configuration
3. **Use Appropriate Timeouts**: Set reasonable timeouts for each checker
4. **Mark Critical Dependencies**: Identify which checks affect readiness

### Monitoring Strategy

1. **Monitor Key Metrics**: Focus on user-impacting metrics
2. **Set Appropriate Thresholds**: Avoid alert fatigue with proper thresholds
3. **Use Multiple Levels**: Implement warning and critical alert levels
4. **Test Alerting**: Regularly test alert delivery and response procedures

### Performance Considerations

1. **Concurrent Checks**: Health checks run concurrently for better performance
2. **Caching**: Consider caching health check results for high-frequency calls
3. **Minimal Overhead**: Metrics collection should have minimal performance impact
4. **Resource Limits**: Set appropriate resource limits for monitoring components

## Troubleshooting

### Common Issues

1. **Health Check Timeouts**: Increase timeout values or optimize check logic
2. **False Positives**: Review check logic and thresholds
3. **Missing Metrics**: Verify Prometheus configuration and network connectivity
4. **High Memory Usage**: Monitor metrics collection overhead

### Debug Mode

Enable detailed health check logging:

```go
logger.SetLevel(logrus.DebugLevel)
```

### Manual Health Checks

Test individual components:

```bash
# Check overall health
curl http://localhost:8080/api/v1/health

# Check specific component
curl http://localhost:8080/api/v1/health/check/redis

# Check readiness
curl http://localhost:8080/api/v1/health/ready
```
