/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-27 13:00:00
Modified: 2025-06-27 13:00:00
*/

package service

import (
	"context"
	"time"

	"github.com/google/uuid"
	"github.com/pkg/errors"
	"github.com/sirupsen/logrus"

	"cina.club/services/live-gateway-service/internal/application/port"
	"cina.club/services/live-gateway-service/internal/domain/model"
)

// GatewayService implements port.GatewayService interface.
type GatewayService struct {
	cache          port.CacheRepository
	mediaAdapter   port.MediaServerAdapter
	loadBalancer   port.LoadBalancer
	liveAPIClient  port.LiveAPIClient
	eventPublisher port.EventPublisher
	eventStore     port.EventStore
	config         *GatewayConfig
	logger         *logrus.Logger
}

// GatewayConfig defines the gateway configuration.
type GatewayConfig struct {
	DefaultTTL               time.Duration `json:"default_ttl"`
	MaxConcurrentStreams     int           `json:"max_concurrent_streams"`
	LoadBalanceStrategy      string        `json:"load_balance_strategy"`
	EnableMetrics            bool          `json:"enable_metrics"`
	WebhookTimeout           time.Duration `json:"webhook_timeout"`
	AuthTimeout              time.Duration `json:"auth_timeout"`
	AllowAuthFallbackOnError bool          `json:"allow_auth_fallback_on_error"`
}

// NewGatewayService creates a new GatewayService instance.
func NewGatewayService(
	cache port.CacheRepository,
	mediaAdapter port.MediaServerAdapter,
	loadBalancer port.LoadBalancer,
	liveAPIClient port.LiveAPIClient,
	eventPublisher port.EventPublisher,
	eventStore port.EventStore,
	config *GatewayConfig,
	logger *logrus.Logger,
) port.GatewayService {
	if config == nil {
		config = getDefaultConfig()
	}
	if logger == nil {
		logger = logrus.New()
	}
	return &GatewayService{
		cache:          cache,
		mediaAdapter:   mediaAdapter,
		loadBalancer:   loadBalancer,
		liveAPIClient:  liveAPIClient,
		eventPublisher: eventPublisher,
		eventStore:     eventStore,
		config:         config,
		logger:         logger,
	}
}

// RequestPushURL requests a push URL.
func (s *GatewayService) RequestPushURL(ctx context.Context, req *port.CreateStreamRequest) (*model.PushURL, error) {
	// Validate request
	if req.RoomID == uuid.Nil {
		return nil, errors.New("room_id is required")
	}
	if req.UserID == uuid.Nil {
		return nil, errors.New("user_id is required")
	}
	if req.Protocol == "" {
		return nil, errors.New("protocol is required")
	}
	if req.Protocol != model.StreamProtocolRTMP && req.Protocol != model.StreamProtocolWebRTC && req.Protocol != model.StreamProtocolSRT {
		return nil, errors.New("unsupported protocol")
	}
	if req.Quality == "" {
		return nil, errors.New("quality is required")
	}
	if req.ClientIP == "" {
		return nil, errors.New("client_ip is required")
	}

	// Generate a unique stream key and auth token
	streamKey, err := model.NewStreamKey()
	if err != nil {
		return nil, errors.Wrap(err, "failed to generate stream key")
	}

	authToken, err := model.NewAuthToken()
	if err != nil {
		return nil, errors.Wrap(err, "failed to generate auth token")
	}

	// Set TTL
	ttl := s.config.DefaultTTL

	// Convert quality to config
	qualityConfig := &model.StreamQualityConfig{
		Level: req.Quality,
	}
	switch req.Quality {
	case model.StreamQualityLow:
		qualityConfig.MaxBitrate = 1000000 // 1 Mbps
		qualityConfig.MaxFramerate = 24
	case model.StreamQualityMedium:
		qualityConfig.MaxBitrate = 2500000 // 2.5 Mbps
		qualityConfig.MaxFramerate = 30
	case model.StreamQualityHigh:
		qualityConfig.MaxBitrate = 5000000 // 5 Mbps
		qualityConfig.MaxFramerate = 60
	}

	// Select the optimal node
	node, err := s.selectOptimalNode(ctx, req.Protocol, req.Quality, req.ClientIP)
	if err != nil {
		return nil, errors.Wrap(err, "failed to select optimal node")
	}

	// Create stream mapping
	mapping := &model.StreamMapping{
		StreamKey:  streamKey,
		RoomID:     req.RoomID,
		UserID:     req.UserID,
		AuthToken:  authToken,
		ServerNode: node.ID,
		CreatedAt:  time.Now(),
		ExpiresAt:  time.Now().Add(ttl),
	}

	// Store stream mapping in cache
	if err := s.cache.StoreStreamMapping(ctx, mapping); err != nil {
		return nil, errors.Wrap(err, "failed to store stream mapping")
	}

	// Generate push URL through the adapter
	pushURLReq := &port.PushURLRequest{
		RoomID:     req.RoomID.String(),
		UserID:     req.UserID.String(),
		StreamKey:  streamKey,
		AuthToken:  authToken,
		Protocol:   req.Protocol,
		Quality:    req.Quality,
		ServerNode: node.ID,
		TTL:        ttl,
		ClientIP:   req.ClientIP,
		UserAgent:  req.UserAgent,
	}

	pushURL, err := s.mediaAdapter.GeneratePushURL(ctx, pushURLReq)
	if err != nil {
		// Clean up cache
		s.cache.DeleteStreamMapping(ctx, streamKey)
		return nil, errors.Wrap(err, "failed to generate push URL")
	}

	s.logger.WithFields(logrus.Fields{
		"room_id":     req.RoomID,
		"user_id":     req.UserID,
		"stream_key":  streamKey,
		"server_node": node.ID,
		"protocol":    req.Protocol,
	}).Info("Push URL generated successfully")

	return pushURL, nil
}

// RequestPlayURLs requests a list of play URLs.
func (s *GatewayService) RequestPlayURLs(ctx context.Context, req *port.RequestPlayURLsRequest) ([]*model.PlayURL, error) {
	// Validate stream key
	mapping, err := s.cache.GetStreamMapping(ctx, req.StreamKey)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get stream mapping")
	}

	if mapping == nil {
		return nil, errors.New("stream not found")
	}

	if mapping.ExpiresAt.Before(time.Now()) {
		return nil, errors.New("stream has expired")
	}

	// Convert quality to config
	qualityConfig := &model.StreamQualityConfig{
		Level: req.Quality,
	}
	switch req.Quality {
	case model.StreamQualityLow:
		qualityConfig.MaxBitrate = 1000000 // 1 Mbps
		qualityConfig.MaxFramerate = 24
	case model.StreamQualityMedium:
		qualityConfig.MaxBitrate = 2500000 // 2.5 Mbps
		qualityConfig.MaxFramerate = 30
	case model.StreamQualityHigh:
		qualityConfig.MaxBitrate = 5000000 // 5 Mbps
		qualityConfig.MaxFramerate = 60
	}

	// Generate play URLs through the adapter
	playURLs, err := s.mediaAdapter.GeneratePlayURLs(ctx, req)
	if err != nil {
		return nil, errors.Wrap(err, "failed to generate play URLs")
	}

	s.logger.WithFields(logrus.Fields{
		"room_id":    mapping.RoomID,
		"stream_key": req.StreamKey,
		"protocols":  req.Protocols,
		"urls_count": len(playURLs),
	}).Info("Play URLs generated successfully")

	return playURLs, nil
}

// HandleWebhook handles webhook callbacks.
func (s *GatewayService) HandleWebhook(ctx context.Context, webhookReq *port.WebhookRequest) (*port.WebhookResponse, error) {
	// Process based on event type
	switch webhookReq.EventType {
	case model.WebhookEventTypePublish:
		return s.handlePublishEvent(ctx, webhookReq)
	case model.WebhookEventTypeUnpublish:
		return s.handleUnpublishEvent(ctx, webhookReq)
	case model.WebhookEventTypeDVR:
		return s.handleDVREvent(ctx, webhookReq)
	case model.WebhookEventTypeSnapshot:
		return s.handleSnapshotEvent(ctx, webhookReq)
	default:
		s.logger.WithField("event_type", webhookReq.EventType).Warn("Unknown webhook event type")
		return &port.WebhookResponse{Success: true}, nil
	}
}

// handlePublishEvent handles the stream publish event.
func (s *GatewayService) handlePublishEvent(ctx context.Context, webhookReq *port.WebhookRequest) (*port.WebhookResponse, error) {
	// Get stream mapping
	mapping, err := s.cache.GetStreamMapping(ctx, webhookReq.StreamKey)
	if err != nil {
		s.logger.WithError(err).WithField("stream_key", webhookReq.StreamKey).Error("Failed to get stream mapping")
		return &port.WebhookResponse{
			Success: false,
			Message: "Stream not found",
		}, nil
	}

	if mapping == nil {
		return &port.WebhookResponse{
			Success: false,
			Message: "Invalid stream key",
		}, nil
	}

	// Synchronous authentication
	authReq := &port.AuthRequest{
		StreamKey:  webhookReq.StreamKey,
		AuthToken:  webhookReq.AuthToken,
		RoomID:     mapping.RoomID,
		UserID:     mapping.UserID,
		ClientIP:   webhookReq.ClientIP,
		UserAgent:  webhookReq.UserAgent,
		ServerNode: webhookReq.ServerNode,
		Protocol:   webhookReq.Protocol,
		Timestamp:  webhookReq.Timestamp,
	}

	authCtx, cancel := context.WithTimeout(ctx, s.config.AuthTimeout)
	defer cancel()

	authResp, err := s.liveAPIClient.CheckPushAuth(authCtx, authReq)
	if err != nil {
		s.logger.WithError(err).Error("Push authentication failed")
		if s.config.AllowAuthFallbackOnError {
			s.logger.Warn("Allowing push due to auth fallback policy.")
			// Still need to perform async notification, but with limited info
			go func() {
				publishEvent := &port.StreamPublishedEvent{
					RoomID:     mapping.RoomID,
					StreamKey:  webhookReq.StreamKey,
					ServerNode: webhookReq.ServerNode,
					Timestamp:  webhookReq.Timestamp,
				}
				if err := s.liveAPIClient.NotifyStreamPublished(context.Background(), publishEvent); err != nil {
					s.logger.WithError(err).Error("Failed to notify stream published during fallback")
				}
			}()
			return &port.WebhookResponse{Success: true}, nil
		}

		return &port.WebhookResponse{
			Success: false,
			Message: "Authentication failed",
		}, nil
	}

	if !authResp.Allowed {
		return &port.WebhookResponse{
			Success: false,
			Message: authResp.Reason,
		}, nil
	}

	// Asynchronous notification
	go func() {
		publishEvent := &port.StreamPublishedEvent{
			RoomID:     mapping.RoomID,
			StreamKey:  webhookReq.StreamKey,
			ServerNode: webhookReq.ServerNode,
			Protocol:   webhookReq.Protocol,
			// Quality: ...
			ClientIP:  webhookReq.ClientIP,
			UserAgent: webhookReq.UserAgent,
			Timestamp: webhookReq.Timestamp,
		}
		if err := s.liveAPIClient.NotifyStreamPublished(context.Background(), publishEvent); err != nil {
			s.logger.WithError(err).Error("Failed to notify stream published")
		}
	}()

	s.logger.WithField("stream_key", webhookReq.StreamKey).Info("Stream published successfully")
	return &port.WebhookResponse{Success: true}, nil
}

// handleUnpublishEvent handles the stream unpublish event.
func (s *GatewayService) handleUnpublishEvent(ctx context.Context, webhookReq *port.WebhookRequest) (*port.WebhookResponse, error) {
	// Get stream mapping
	mapping, err := s.cache.GetStreamMapping(ctx, webhookReq.StreamKey)
	if err != nil {
		// If mapping not found, it might have been cleaned up, log a warning
		s.logger.WithError(err).WithField("stream_key", webhookReq.StreamKey).Warn("Failed to get stream mapping on unpublish")
	}

	// Asynchronous notification
	go func() {
		unpublishEvent := &port.StreamUnpublishedEvent{
			// RoomID needs to be retrieved from mapping or other sources
			StreamKey:  webhookReq.StreamKey,
			ServerNode: webhookReq.ServerNode,
			Reason:     "normal", // Or parse from webhookReq.Data
			// Duration, Stats need to be parsed from webhookReq.Data
			Timestamp: webhookReq.Timestamp,
		}
		if mapping != nil {
			unpublishEvent.RoomID = mapping.RoomID
		}

		if err := s.liveAPIClient.NotifyStreamUnpublished(context.Background(), unpublishEvent); err != nil {
			s.logger.WithError(err).Error("Failed to notify stream unpublished")
		}
	}()

	// Clean up resources
	s.cache.DeleteStreamMapping(ctx, webhookReq.StreamKey)
	s.cache.DeleteStreamStats(ctx, webhookReq.StreamKey)

	s.logger.WithField("stream_key", webhookReq.StreamKey).Info("Stream unpublished")
	return &port.WebhookResponse{Success: true}, nil
}

// handleDVREvent handles the DVR completion event.
func (s *GatewayService) handleDVREvent(ctx context.Context, webhookReq *port.WebhookRequest) (*port.WebhookResponse, error) {
	mapping, err := s.cache.GetStreamMapping(ctx, webhookReq.StreamKey)
	if err != nil {
		s.logger.WithError(err).WithField("stream_key", webhookReq.StreamKey).Warn("Failed to get stream mapping for DVR event")
		// Continue processing as some info might be in the webhook itself
	}

	go func() {
		dvrEvent := &port.RecordingCompletedEvent{
			StreamKey: webhookReq.StreamKey,
			// Other fields need to be parsed from webhookReq.Data
			Timestamp: webhookReq.Timestamp,
		}
		if mapping != nil {
			dvrEvent.RoomID = mapping.RoomID
		}

		if err := s.liveAPIClient.NotifyRecordingCompleted(context.Background(), dvrEvent); err != nil {
			s.logger.WithError(err).Error("Failed to notify recording completed")
		}
	}()

	s.logger.WithField("stream_key", webhookReq.StreamKey).Info("DVR event received")
	return &port.WebhookResponse{Success: true}, nil
}

// handleSnapshotEvent handles the snapshot completion event.
func (s *GatewayService) handleSnapshotEvent(ctx context.Context, webhookReq *port.WebhookRequest) (*port.WebhookResponse, error) {
	mapping, err := s.cache.GetStreamMapping(ctx, webhookReq.StreamKey)
	if err != nil {
		s.logger.WithError(err).WithField("stream_key", webhookReq.StreamKey).Warn("Failed to get stream mapping for snapshot event")
	}

	go func() {
		snapshotEvent := &port.ModerationSubmissionEvent{
			StreamKey: webhookReq.StreamKey,
			// Other fields need to be parsed from webhookReq.Data
			Timestamp: webhookReq.Timestamp,
		}
		if mapping != nil {
			snapshotEvent.RoomID = mapping.RoomID
		}

		if err := s.liveAPIClient.SubmitForModeration(context.Background(), snapshotEvent); err != nil {
			s.logger.WithError(err).Error("Failed to submit for moderation")
		}
	}()

	s.logger.WithField("stream_key", webhookReq.StreamKey).Info("Snapshot event received")
	return &port.WebhookResponse{Success: true}, nil
}

// KickStream kicks a stream.
func (s *GatewayService) KickStream(ctx context.Context, req *port.KickStreamRequest) error {
	mapping, err := s.cache.GetStreamMapping(ctx, req.StreamKey)
	if err != nil {
		return errors.Wrap(err, "stream not found")
	}
	if mapping == nil {
		return errors.New("stream not found")
	}

	err = s.mediaAdapter.KickStream(ctx, req.StreamKey)
	if err != nil {
		return errors.Wrap(err, "failed to kick stream via media adapter")
	}

	// Asynchronous notification
	go func() {
		kickEvent := &port.StreamKickedEvent{
			RoomID:     mapping.RoomID,
			StreamKey:  req.StreamKey,
			ServerNode: mapping.ServerNode,
			Reason:     req.Reason,
			// AdminID needs to be passed in or retrieved from context
			Timestamp: time.Now(),
		}
		if err := s.liveAPIClient.NotifyStreamKicked(context.Background(), kickEvent); err != nil {
			s.logger.WithError(err).Error("Failed to notify stream kicked")
		}
	}()

	s.logger.WithField("stream_key", req.StreamKey).Info("Stream kicked successfully")
	return nil
}

// GetStreamInfo retrieves information about a stream.
func (s *GatewayService) GetStreamInfo(ctx context.Context, streamKey string) (*model.Stream, error) {
	mapping, err := s.cache.GetStreamMapping(ctx, streamKey)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get stream mapping")
	}
	if mapping == nil {
		return nil, errors.New("stream not found")
	}

	// Get real-time info from media server
	streamInfo, err := s.mediaAdapter.GetStreamInfo(ctx, streamKey)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get stream info from media adapter")
	}

	// Combine information
	stream := &model.Stream{
		StreamKey:  streamKey,
		RoomID:     mapping.RoomID,
		UserID:     mapping.UserID,
		AuthToken:  mapping.AuthToken,
		ServerNode: mapping.ServerNode,
		CreatedAt:  mapping.CreatedAt,
		ExpiresAt:  mapping.ExpiresAt,
	}

	if streamInfo != nil {
		stream.Status = streamInfo.Status
		stream.Protocol = streamInfo.Protocol
		stream.Quality = streamInfo.Quality
		stream.Stats = streamInfo.Stats
		if streamInfo.StartedAt != nil {
			stream.StartedAt = *streamInfo.StartedAt
		}
		if streamInfo.LastSeenAt != nil {
			stream.LastSeenAt = *streamInfo.LastSeenAt
		}
	}

	return stream, nil
}

// GetServerStats retrieves server statistics.
func (s *GatewayService) GetServerStats(ctx context.Context) (*model.MetricsInfo, error) {
	nodes, err := s.loadBalancer.GetAvailableNodes(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get available nodes")
	}

	var totalStreams int
	var totalBitrate int64
	var totalViewers int

	// Aggregate stats from all nodes
	for _, node := range nodes {
		stats, err := s.mediaAdapter.GetServerStats(ctx, node.ID)
		if err != nil {
			s.logger.WithError(err).WithField("node_id", node.ID).Warn("Failed to get stats for node")
			continue
		}
		if stats != nil {
			totalStreams += stats.CurrentStreams
			totalBitrate += stats.NetworkIn
		}
	}

	metrics := &model.MetricsInfo{
		LastUpdated:       time.Now(),
		ActiveStreams:     int64(totalStreams),
		TotalBandwidthIn:  totalBitrate,
		ConcurrentViewers: int64(totalViewers),
		ActiveNodes:       int64(len(nodes)),
	}

	return metrics, nil
}

// HealthCheck performs a health check on the service and its dependencies.
func (s *GatewayService) HealthCheck(ctx context.Context) error {
	if err := s.mediaAdapter.HealthCheck(ctx); err != nil {
		return errors.Wrap(err, "media adapter health check failed")
	}
	// Check other dependencies like LiveAPI service
	return nil
}

// PublishEvent publishes a single event.
func (s *GatewayService) PublishEvent(ctx context.Context, event *model.Event) error {
	if event == nil {
		return errors.New("event cannot be nil")
	}

	// Validate event
	if err := event.Validate(); err != nil {
		return errors.Wrap(err, "invalid event")
	}

	// Store event
	if err := s.eventStore.StoreEvent(ctx, event); err != nil {
		return errors.Wrap(err, "failed to store event")
	}

	// Publish event through event publisher
	if err := s.eventPublisher.PublishEvent(ctx, event); err != nil {
		return errors.Wrap(err, "failed to publish event")
	}

	s.logger.WithFields(logrus.Fields{
		"event_type": event.Type,
		"stream_key": event.StreamKey,
	}).Debug("Event published successfully")

	return nil
}

// ProcessEventBatch processes a batch of events.
func (s *GatewayService) ProcessEventBatch(ctx context.Context, events []*model.Event) error {
	if len(events) == 0 {
		return errors.New("event batch cannot be empty")
	}

	// Store events in batch
	if err := s.eventStore.StoreBatch(ctx, events); err != nil {
		return errors.Wrap(err, "failed to store event batch")
	}

	// Publish events individually
	for _, event := range events {
		if err := s.eventPublisher.PublishEvent(ctx, event); err != nil {
			return errors.Wrapf(err, "failed to publish event %s", event.ID)
		}
	}

	s.logger.WithField("batch_size", len(events)).Debug("Event batch processed successfully")

	return nil
}

// QueryEvents queries events within a time range.
func (s *GatewayService) QueryEvents(ctx context.Context, startTime, endTime time.Time) ([]*model.Event, error) {
	if startTime.After(endTime) {
		return nil, errors.New("start time must be before end time")
	}

	// Query events through event store
	events, err := s.eventStore.QueryEvents(ctx, startTime, endTime)
	if err != nil {
		return nil, errors.Wrap(err, "failed to query events")
	}

	s.logger.WithFields(logrus.Fields{
		"start_time": startTime,
		"end_time":   endTime,
		"count":      len(events),
	}).Debug("Events queried successfully")

	return events, nil
}

// AggregateStreamStats aggregates statistics for the specified streams.
func (s *GatewayService) AggregateStreamStats(ctx context.Context, streamKeys []string) (map[string]interface{}, error) {
	if len(streamKeys) == 0 {
		return nil, errors.New("stream keys cannot be empty")
	}

	stats := make(map[string]interface{})

	// Aggregate stats for each stream
	for _, streamKey := range streamKeys {
		// Get stream info
		stream, err := s.GetStreamInfo(ctx, streamKey)
		if err != nil {
			if errors.Is(err, port.ErrStreamNotFound) {
				continue // Skip not found streams
			}
			return nil, errors.Wrapf(err, "failed to get stream info for %s", streamKey)
		}

		// Add stream stats to aggregation
		if stream.Stats != nil {
			stats[streamKey] = stream.Stats
		}
	}

	s.logger.WithFields(logrus.Fields{
		"stream_count": len(streamKeys),
		"stats_count":  len(stats),
	}).Debug("Stream stats aggregated successfully")

	return stats, nil
}

// RegisterNode registers a new media server node.
func (s *GatewayService) RegisterNode(ctx context.Context, node *model.MediaNode) error {
	if err := node.Validate(); err != nil {
		return errors.Wrap(err, "invalid node configuration")
	}

	if err := s.cache.StoreNode(ctx, node); err != nil {
		return errors.Wrap(err, "failed to register node")
	}

	s.logger.WithField("node_id", node.ID).Info("Node registered successfully")
	return nil
}

// UpdateNodeStatus updates the status of a media server node.
func (s *GatewayService) UpdateNodeStatus(ctx context.Context, nodeID string, status model.NodeStatus) error {
	node, err := s.cache.GetNode(ctx, nodeID)
	if err != nil {
		return errors.Wrap(err, "failed to get node")
	}

	if node == nil {
		return errors.New("node not found")
	}

	if err := s.cache.UpdateNodeStatus(ctx, nodeID, status); err != nil {
		return errors.Wrap(err, "failed to update node status")
	}

	s.logger.WithFields(logrus.Fields{
		"node_id": nodeID,
		"status":  status,
	}).Info("Node status updated successfully")

	return nil
}

// GetNodeByID retrieves a media server node by its ID.
func (s *GatewayService) GetNodeByID(ctx context.Context, nodeID string) (*model.MediaNode, error) {
	node, err := s.cache.GetNode(ctx, nodeID)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get node")
	}

	if node == nil {
		return nil, errors.New("node not found")
	}

	return node, nil
}

// GetAllNodes retrieves all media server nodes.
func (s *GatewayService) GetAllNodes(ctx context.Context) ([]*model.MediaNode, error) {
	nodes, err := s.cache.GetAllNodes(ctx)
	if err != nil {
		return nil, errors.Wrap(err, "failed to get all nodes")
	}

	return nodes, nil
}

// UpdateNodeStats updates the stats of a media server node.
func (s *GatewayService) UpdateNodeStats(ctx context.Context, nodeID string, stats *model.NodeStats) error {
	node, err := s.cache.GetNode(ctx, nodeID)
	if err != nil {
		return errors.Wrap(err, "failed to get node")
	}

	if node == nil {
		return errors.New("node not found")
	}

	if err := s.cache.UpdateNodeStats(ctx, nodeID, stats); err != nil {
		return errors.Wrap(err, "failed to update node stats")
	}

	s.logger.WithField("node_id", nodeID).Debug("Node stats updated successfully")
	return nil
}

// SelectNode selects a suitable node for a stream based on region and protocol.
func (s *GatewayService) SelectNode(ctx context.Context, region string, protocol model.StreamProtocol) (*model.MediaNode, error) {
	// Create selection request
	req := &port.NodeSelectionRequest{
		Region:   region,
		Protocol: protocol,
		Strategy: model.LoadBalanceStrategy(s.config.LoadBalanceStrategy),
	}

	// Delegate to load balancer
	node, err := s.loadBalancer.SelectNodeByRequest(ctx, req)
	if err != nil {
		return nil, errors.Wrap(err, "failed to select node")
	}

	s.logger.WithFields(logrus.Fields{
		"node_id":  node.ID,
		"region":   region,
		"protocol": protocol,
	}).Debug("Node selected successfully")

	return node, nil
}

// selectOptimalNode selects the best node for a new stream.
func (s *GatewayService) selectOptimalNode(ctx context.Context, protocol model.StreamProtocol, quality model.StreamQuality, clientIP string) (*model.MediaNode, error) {
	// TODO: Implement region detection based on clientIP
	region := "" // Default to empty region for now

	// Create selection request
	req := &port.NodeSelectionRequest{
		Region:   region,
		Protocol: protocol,
		Quality:  quality,
		Strategy: model.LoadBalanceStrategy(s.config.LoadBalanceStrategy),
		ClientIP: clientIP,
	}

	// Delegate to load balancer
	node, err := s.loadBalancer.SelectNodeByRequest(ctx, req)
	if err != nil {
		return nil, errors.Wrap(err, "failed to select node")
	}

	s.logger.WithFields(logrus.Fields{
		"node_id":  node.ID,
		"protocol": protocol,
		"quality":  quality,
	}).Debug("Node selected successfully")

	return node, nil
}

func getDefaultConfig() *GatewayConfig {
	return &GatewayConfig{
		DefaultTTL:               time.Hour * 24,
		MaxConcurrentStreams:     1000,
		LoadBalanceStrategy:      "least_load",
		EnableMetrics:            true,
		WebhookTimeout:           time.Second * 5,
		AuthTimeout:              time.Second * 3,
		AllowAuthFallbackOnError: false,
	}
}
