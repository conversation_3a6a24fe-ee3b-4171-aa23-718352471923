@echo off
REM Live IM Service Benchmark Runner for Windows
REM Copyright (c) 2025 Cina.Club
REM All rights reserved.

setlocal enabledelayedexpansion

REM Configuration
set BENCHMARK_DIR=./test/benchmark
set RESULTS_DIR=./benchmark-results
for /f "tokens=2 delims==" %%a in ('wmic OS Get localdatetime /value') do set "dt=%%a"
set TIMESTAMP=%dt:~0,8%_%dt:~8,6%
set RESULTS_FILE=%RESULTS_DIR%/benchmark_results_%TIMESTAMP%.txt

REM Create results directory
if not exist "%RESULTS_DIR%" mkdir "%RESULTS_DIR%"

echo === Live IM Service Performance Benchmarks ===
echo Timestamp: %date% %time%
echo Results will be saved to: %RESULTS_FILE%
echo.

REM Start benchmarking
echo Starting benchmark suite... > "%RESULTS_FILE%"
echo System Information: >> "%RESULTS_FILE%"
echo OS: Windows >> "%RESULTS_FILE%"
echo Go Version: >> "%RESULTS_FILE%"
go version >> "%RESULTS_FILE%"
echo. >> "%RESULTS_FILE%"

echo 1. Message Processing Benchmarks
echo === Message Benchmarks - Message creation, cloning, and validation === >> "%RESULTS_FILE%"
echo Timestamp: %date% %time% >> "%RESULTS_FILE%"
echo. >> "%RESULTS_FILE%"

go test -bench=BenchmarkMessage -benchmem %BENCHMARK_DIR% >> "%RESULTS_FILE%" 2>&1
if %errorlevel% equ 0 (
    echo [32m✅ Message benchmarks completed successfully[0m
) else (
    echo [31m❌ Message benchmarks failed[0m
)

echo. >> "%RESULTS_FILE%"
echo ---------------------------------------- >> "%RESULTS_FILE%"
echo. >> "%RESULTS_FILE%"

echo 2. JSON Serialization Benchmarks
echo === Serialization Benchmarks - JSON serialization and deserialization === >> "%RESULTS_FILE%"
echo Timestamp: %date% %time% >> "%RESULTS_FILE%"
echo. >> "%RESULTS_FILE%"

go test -bench=BenchmarkJSON -benchmem %BENCHMARK_DIR% >> "%RESULTS_FILE%" 2>&1
if %errorlevel% equ 0 (
    echo [32m✅ Serialization benchmarks completed successfully[0m
) else (
    echo [31m❌ Serialization benchmarks failed[0m
)

echo. >> "%RESULTS_FILE%"
echo ---------------------------------------- >> "%RESULTS_FILE%"
echo. >> "%RESULTS_FILE%"

echo 3. Concurrent Operations Benchmarks
echo === Concurrent Benchmarks - Concurrent message processing === >> "%RESULTS_FILE%"
echo Timestamp: %date% %time% >> "%RESULTS_FILE%"
echo. >> "%RESULTS_FILE%"

go test -bench=Concurrent -benchmem %BENCHMARK_DIR% >> "%RESULTS_FILE%" 2>&1
if %errorlevel% equ 0 (
    echo [32m✅ Concurrent benchmarks completed successfully[0m
) else (
    echo [31m❌ Concurrent benchmarks failed[0m
)

echo. >> "%RESULTS_FILE%"
echo ---------------------------------------- >> "%RESULTS_FILE%"
echo. >> "%RESULTS_FILE%"

echo 4. Memory Allocation Benchmarks
echo === Memory Benchmarks - Memory allocation patterns === >> "%RESULTS_FILE%"
echo Timestamp: %date% %time% >> "%RESULTS_FILE%"
echo. >> "%RESULTS_FILE%"

go test -bench=BenchmarkSerializationMemoryAllocation -benchmem %BENCHMARK_DIR% >> "%RESULTS_FILE%" 2>&1
if %errorlevel% equ 0 (
    echo [32m✅ Memory benchmarks completed successfully[0m
) else (
    echo [31m❌ Memory benchmarks failed[0m
)

echo. >> "%RESULTS_FILE%"
echo ---------------------------------------- >> "%RESULTS_FILE%"
echo. >> "%RESULTS_FILE%"

REM Check for Redis
redis-cli ping >nul 2>&1
if %errorlevel% equ 0 (
    echo 5. Redis Operations Benchmarks
    echo [32mRedis server detected, running Redis benchmarks...[0m
    echo === Redis Benchmarks - Redis operations performance === >> "%RESULTS_FILE%"
    echo Timestamp: %date% %time% >> "%RESULTS_FILE%"
    echo. >> "%RESULTS_FILE%"
    
    go test -bench=BenchmarkRedis -benchmem %BENCHMARK_DIR% >> "%RESULTS_FILE%" 2>&1
    if %errorlevel% equ 0 (
        echo [32m✅ Redis benchmarks completed successfully[0m
    ) else (
        echo [31m❌ Redis benchmarks failed[0m
    )
) else (
    echo 5. Redis Operations Benchmarks
    echo [33m⚠️  Redis server not available, skipping Redis benchmarks[0m
    echo === Redis Benchmarks - SKIPPED === >> "%RESULTS_FILE%"
    echo Redis server not available >> "%RESULTS_FILE%"
    echo. >> "%RESULTS_FILE%"
)

echo. >> "%RESULTS_FILE%"
echo ---------------------------------------- >> "%RESULTS_FILE%"
echo. >> "%RESULTS_FILE%"

REM Generate summary
echo 6. Generating Performance Summary
echo === Performance Summary === >> "%RESULTS_FILE%"
echo Generated: %date% %time% >> "%RESULTS_FILE%"
echo. >> "%RESULTS_FILE%"

echo Performance Analysis: >> "%RESULTS_FILE%"
echo - Check detailed results above for specific metrics >> "%RESULTS_FILE%"
echo - Message creation should be under 1000 ns/op >> "%RESULTS_FILE%"
echo - JSON serialization should be under 10000 ns/op for simple messages >> "%RESULTS_FILE%"
echo. >> "%RESULTS_FILE%"

echo Recommendations: >> "%RESULTS_FILE%"
echo 1. Monitor message creation performance (target: ^<1000 ns/op) >> "%RESULTS_FILE%"
echo 2. Optimize large message serialization if ^>10000 ns/op >> "%RESULTS_FILE%"
echo 3. Consider object pooling for high-allocation operations >> "%RESULTS_FILE%"
echo 4. Run benchmarks regularly to detect performance regressions >> "%RESULTS_FILE%"

echo.
echo === Benchmark Suite Completed ===
echo [32mResults saved to: %RESULTS_FILE%[0m
echo.
echo [34mQuick Summary:[0m
findstr /C:"PASS" "%RESULTS_FILE%" >nul
if %errorlevel% equ 0 (
    echo [32m✅ All benchmarks completed successfully[0m
) else (
    echo [31m❌ Some benchmarks failed - check results file[0m
)

echo.
echo [33m💡 To analyze results in detail:[0m
echo [33m   type "%RESULTS_FILE%"[0m
echo [33m   or open the file in your preferred editor[0m
echo.
echo [33m💡 To run specific benchmarks:[0m
echo [33m   go test -bench=BenchmarkMessage -benchmem ./test/benchmark[0m
echo [33m   go test -bench=BenchmarkJSON -benchmem ./test/benchmark[0m
echo.
echo [34mHappy benchmarking! 🚀[0m

pause
