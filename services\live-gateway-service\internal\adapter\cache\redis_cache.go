/*
Copyright (c) 2025 Cina.Club
All rights reserved.
*/

package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"

	"cina.club/services/live-gateway-service/internal/application/port"
	"cina.club/services/live-gateway-service/internal/domain/model"
)

const (
	// Maximum allowed TTL is 7 days
	maxTTL = 7 * 24 * time.Hour
)

// validateTTL validates that TTL is within acceptable bounds
func validateTTL(ttl time.Duration) error {
	if ttl <= 0 {
		return fmt.Errorf("TTL must be positive, got %v", ttl)
	}
	if ttl > maxTTL {
		return fmt.Errorf("TTL %v exceeds maximum allowed value of %v", ttl, maxTTL)
	}
	return nil
}

// RedisCache implements the Cache interface using Redis
type RedisCache struct {
	client *redis.Client
	config *port.CacheConfig
	keyGen *keyGenerator
}

// keyGenerator helps generate consistent Redis keys
type keyGenerator struct{}

// NewRedisCache creates a new Redis cache instance
func NewRedisCache(client *redis.Client, config *port.CacheConfig) *RedisCache {
	return &RedisCache{
		client: client,
		config: config,
		keyGen: &keyGenerator{},
	}
}

// Key generation methods
func (kg *keyGenerator) StreamMappingKey(streamKey string) string {
	return fmt.Sprintf("stream:mapping:%s", streamKey)
}

func (kg *keyGenerator) StreamStatsKey(streamKey string) string {
	return fmt.Sprintf("stream:stats:%s", streamKey)
}

func (kg *keyGenerator) NodeLoadKey(nodeID string) string {
	return fmt.Sprintf("node:load:%s", nodeID)
}

// Cache interface methods
func (c *RedisCache) StoreStreamMapping(ctx context.Context, mapping *model.StreamMapping) error {
	// Validate TTL before proceeding
	if err := validateTTL(c.config.StreamMappingTTL); err != nil {
		return fmt.Errorf("invalid StreamMappingTTL: %w", err)
	}

	data, err := json.Marshal(mapping)
	if err != nil {
		return fmt.Errorf("failed to marshal stream mapping: %w", err)
	}

	key := c.keyGen.StreamMappingKey(mapping.StreamKey)
	return c.client.Set(ctx, key, data, c.config.StreamMappingTTL).Err()
}

func (c *RedisCache) GetStreamMapping(ctx context.Context, streamKey string) (*model.StreamMapping, error) {
	key := c.keyGen.StreamMappingKey(streamKey)
	data, err := c.client.Get(ctx, key).Bytes()
	if err == redis.Nil {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get stream mapping: %w", err)
	}

	var mapping model.StreamMapping
	if err := json.Unmarshal(data, &mapping); err != nil {
		return nil, fmt.Errorf("failed to unmarshal stream mapping: %w", err)
	}
	return &mapping, nil
}

func (c *RedisCache) DeleteStreamMapping(ctx context.Context, streamKey string) error {
	key := c.keyGen.StreamMappingKey(streamKey)
	return c.client.Del(ctx, key).Err()
}

func (c *RedisCache) StoreStreamStats(ctx context.Context, streamKey string, stats *model.StreamStats) error {
	// Validate TTL before proceeding
	if err := validateTTL(c.config.StreamStatsTTL); err != nil {
		return fmt.Errorf("invalid StreamStatsTTL: %w", err)
	}

	data, err := json.Marshal(stats)
	if err != nil {
		return fmt.Errorf("failed to marshal stream stats: %w", err)
	}

	key := c.keyGen.StreamStatsKey(streamKey)
	return c.client.Set(ctx, key, data, c.config.StreamStatsTTL).Err()
}

func (c *RedisCache) GetStreamStats(ctx context.Context, streamKey string) (*model.StreamStats, error) {
	key := c.keyGen.StreamStatsKey(streamKey)
	data, err := c.client.Get(ctx, key).Bytes()
	if err == redis.Nil {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get stream stats: %w", err)
	}

	var stats model.StreamStats
	if err := json.Unmarshal(data, &stats); err != nil {
		return nil, fmt.Errorf("failed to unmarshal stream stats: %w", err)
	}
	return &stats, nil
}

func (c *RedisCache) DeleteStreamStats(ctx context.Context, streamKey string) error {
	key := c.keyGen.StreamStatsKey(streamKey)
	return c.client.Del(ctx, key).Err()
}

func (c *RedisCache) StoreNodeLoad(ctx context.Context, nodeID string, load *model.NodeLoad) error {
	// Validate TTL before proceeding
	if err := validateTTL(c.config.NodeLoadTTL); err != nil {
		return fmt.Errorf("invalid NodeLoadTTL: %w", err)
	}

	data, err := json.Marshal(load)
	if err != nil {
		return fmt.Errorf("failed to marshal node load: %w", err)
	}

	key := c.keyGen.NodeLoadKey(nodeID)
	return c.client.Set(ctx, key, data, c.config.NodeLoadTTL).Err()
}

func (c *RedisCache) GetNodeLoad(ctx context.Context, nodeID string) (*model.NodeLoad, error) {
	key := c.keyGen.NodeLoadKey(nodeID)
	data, err := c.client.Get(ctx, key).Bytes()
	if err == redis.Nil {
		return nil, nil
	}
	if err != nil {
		return nil, fmt.Errorf("failed to get node load: %w", err)
	}

	var load model.NodeLoad
	if err := json.Unmarshal(data, &load); err != nil {
		return nil, fmt.Errorf("failed to unmarshal node load: %w", err)
	}
	return &load, nil
}

func (c *RedisCache) DeleteNodeLoad(ctx context.Context, nodeID string) error {
	key := c.keyGen.NodeLoadKey(nodeID)
	return c.client.Del(ctx, key).Err()
}

func (c *RedisCache) ListNodeLoads(ctx context.Context) (map[string]*model.NodeLoad, error) {
	pattern := "node:load:*"
	keys, err := c.client.Keys(ctx, pattern).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to list node loads: %w", err)
	}

	loads := make(map[string]*model.NodeLoad)
	for _, key := range keys {
		data, err := c.client.Get(ctx, key).Bytes()
		if err != nil {
			continue
		}

		var load model.NodeLoad
		if err := json.Unmarshal(data, &load); err != nil {
			continue
		}
		loads[load.NodeID] = &load
	}
	return loads, nil
}

func (c *RedisCache) Ping(ctx context.Context) error {
	return c.client.Ping(ctx).Err()
}

func (c *RedisCache) Close() error {
	return c.client.Close()
}
