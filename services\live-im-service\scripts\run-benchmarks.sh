#!/bin/bash

# Live IM Service Benchmark Runner
# Copyright (c) 2025 Cina.Club
# All rights reserved.

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BENCHMARK_DIR="./test/benchmark"
RESULTS_DIR="./benchmark-results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
RESULTS_FILE="$RESULTS_DIR/benchmark_results_$TIMESTAMP.txt"

# Create results directory
mkdir -p "$RESULTS_DIR"

echo -e "${BLUE}=== Live IM Service Performance Benchmarks ===${NC}"
echo -e "${BLUE}Timestamp: $(date)${NC}"
echo -e "${BLUE}Results will be saved to: $RESULTS_FILE${NC}"
echo ""

# Function to run benchmark and capture results
run_benchmark() {
    local name="$1"
    local pattern="$2"
    local description="$3"
    
    echo -e "${YELLOW}Running $name benchmarks...${NC}"
    echo "=== $name Benchmarks - $description ===" >> "$RESULTS_FILE"
    echo "Timestamp: $(date)" >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
    
    if go test -bench="$pattern" -benchmem "$BENCHMARK_DIR" >> "$RESULTS_FILE" 2>&1; then
        echo -e "${GREEN}✅ $name benchmarks completed successfully${NC}"
    else
        echo -e "${RED}❌ $name benchmarks failed${NC}"
        return 1
    fi
    
    echo "" >> "$RESULTS_FILE"
    echo "----------------------------------------" >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
}

# Function to check if Redis is available
check_redis() {
    if command -v redis-cli >/dev/null 2>&1; then
        if redis-cli ping >/dev/null 2>&1; then
            return 0
        fi
    fi
    return 1
}

# Start benchmarking
echo "Starting benchmark suite..." > "$RESULTS_FILE"
echo "System Information:" >> "$RESULTS_FILE"
echo "OS: $(uname -s)" >> "$RESULTS_FILE"
echo "Architecture: $(uname -m)" >> "$RESULTS_FILE"
echo "Go Version: $(go version)" >> "$RESULTS_FILE"
if command -v lscpu >/dev/null 2>&1; then
    echo "CPU: $(lscpu | grep 'Model name' | cut -d':' -f2 | xargs)" >> "$RESULTS_FILE"
elif command -v sysctl >/dev/null 2>&1; then
    echo "CPU: $(sysctl -n machdep.cpu.brand_string 2>/dev/null || echo 'Unknown')" >> "$RESULTS_FILE"
fi
echo "" >> "$RESULTS_FILE"

# Run individual benchmark suites
echo -e "${BLUE}1. Message Processing Benchmarks${NC}"
run_benchmark "Message" "BenchmarkMessage" "Message creation, cloning, and validation"

echo -e "${BLUE}2. JSON Serialization Benchmarks${NC}"
run_benchmark "Serialization" "BenchmarkJSON" "JSON serialization and deserialization"

echo -e "${BLUE}3. Concurrent Operations Benchmarks${NC}"
run_benchmark "Concurrent" "Concurrent" "Concurrent message processing"

echo -e "${BLUE}4. Memory Allocation Benchmarks${NC}"
run_benchmark "Memory" "BenchmarkSerializationMemoryAllocation" "Memory allocation patterns"

# Redis benchmarks (optional)
if check_redis; then
    echo -e "${BLUE}5. Redis Operations Benchmarks${NC}"
    echo -e "${GREEN}Redis server detected, running Redis benchmarks...${NC}"
    run_benchmark "Redis" "BenchmarkRedis" "Redis operations performance"
else
    echo -e "${YELLOW}5. Redis Operations Benchmarks${NC}"
    echo -e "${YELLOW}⚠️  Redis server not available, skipping Redis benchmarks${NC}"
    echo "=== Redis Benchmarks - SKIPPED ===" >> "$RESULTS_FILE"
    echo "Redis server not available" >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
fi

# Generate summary
echo -e "${BLUE}6. Generating Performance Summary${NC}"
echo "=== Performance Summary ===" >> "$RESULTS_FILE"
echo "Generated: $(date)" >> "$RESULTS_FILE"
echo "" >> "$RESULTS_FILE"

# Extract key metrics from results
if grep -q "BenchmarkMessage" "$RESULTS_FILE"; then
    echo "Message Creation Performance:" >> "$RESULTS_FILE"
    grep "BenchmarkMessageCreation" "$RESULTS_FILE" | head -6 >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
fi

if grep -q "BenchmarkJSON" "$RESULTS_FILE"; then
    echo "Serialization Performance:" >> "$RESULTS_FILE"
    grep "BenchmarkJSONSerialization" "$RESULTS_FILE" | head -5 >> "$RESULTS_FILE"
    echo "" >> "$RESULTS_FILE"
fi

# Performance analysis
echo "Performance Analysis:" >> "$RESULTS_FILE"
echo "- Message creation: $(grep -o '[0-9.]*' <<< $(grep "BarrageMessage-" "$RESULTS_FILE" | head -1) | head -1) ns/op" >> "$RESULTS_FILE"
echo "- JSON serialization: $(grep -o '[0-9.]*' <<< $(grep "SerializeSimpleMessage-" "$RESULTS_FILE" | head -1) | head -1) ns/op" >> "$RESULTS_FILE"
echo "" >> "$RESULTS_FILE"

# Recommendations
echo "Recommendations:" >> "$RESULTS_FILE"
echo "1. Monitor message creation performance (target: <1000 ns/op)" >> "$RESULTS_FILE"
echo "2. Optimize large message serialization if >10000 ns/op" >> "$RESULTS_FILE"
echo "3. Consider object pooling for high-allocation operations" >> "$RESULTS_FILE"
echo "4. Run benchmarks regularly to detect performance regressions" >> "$RESULTS_FILE"

echo ""
echo -e "${GREEN}=== Benchmark Suite Completed ===${NC}"
echo -e "${GREEN}Results saved to: $RESULTS_FILE${NC}"
echo ""
echo -e "${BLUE}Quick Summary:${NC}"
if grep -q "PASS" "$RESULTS_FILE"; then
    echo -e "${GREEN}✅ All benchmarks completed successfully${NC}"
else
    echo -e "${RED}❌ Some benchmarks failed - check results file${NC}"
fi

# Display key metrics
if grep -q "BenchmarkMessageCreation/BarrageMessage-" "$RESULTS_FILE"; then
    BARRAGE_PERF=$(grep "BenchmarkMessageCreation/BarrageMessage-" "$RESULTS_FILE" | awk '{print $3}')
    echo -e "${BLUE}📊 Message Creation: $BARRAGE_PERF${NC}"
fi

if grep -q "BenchmarkJSONSerialization/SerializeSimpleMessage-" "$RESULTS_FILE"; then
    SERIALIZE_PERF=$(grep "BenchmarkJSONSerialization/SerializeSimpleMessage-" "$RESULTS_FILE" | awk '{print $3}')
    echo -e "${BLUE}📊 JSON Serialization: $SERIALIZE_PERF${NC}"
fi

echo ""
echo -e "${YELLOW}💡 To analyze results in detail:${NC}"
echo -e "${YELLOW}   cat $RESULTS_FILE${NC}"
echo -e "${YELLOW}   or open the file in your preferred editor${NC}"
echo ""
echo -e "${YELLOW}💡 To run specific benchmarks:${NC}"
echo -e "${YELLOW}   go test -bench=BenchmarkMessage -benchmem ./test/benchmark${NC}"
echo -e "${YELLOW}   go test -bench=BenchmarkJSON -benchmem ./test/benchmark${NC}"
echo ""
echo -e "${BLUE}Happy benchmarking! 🚀${NC}"
