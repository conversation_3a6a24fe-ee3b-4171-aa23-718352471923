@echo off
REM Copyright (c) 2025 Cina.Club
REM All rights reserved.
REM Created: 2025-07-11 10:12:13
REM Modified: 2025-07-11 10:12:13

setlocal enabledelayedexpansion

echo Generating protobuf files...

set SCRIPT_DIR=%~dp0
set SERVICE_DIR=%SCRIPT_DIR%..
set PROTO_DIR=%SERVICE_DIR%\proto
set OUTPUT_DIR=%SERVICE_DIR%\proto\live_im

echo Proto directory: %PROTO_DIR%
echo Output directory: %OUTPUT_DIR%

REM Create output directory if it doesn't exist
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

REM Generate Go protobuf files
protoc ^
    --proto_path="%PROTO_DIR%" ^
    --go_out="%OUTPUT_DIR%" ^
    --go_opt=paths=source_relative ^
    "%PROTO_DIR%\live_im.proto"

if %ERRORLEVEL% neq 0 (
    echo Error: Protobuf generation failed!
    exit /b 1
)

echo Protobuf generation completed successfully!
echo Generated files:
dir "%OUTPUT_DIR%\*.pb.go" /b 2>nul

endlocal
