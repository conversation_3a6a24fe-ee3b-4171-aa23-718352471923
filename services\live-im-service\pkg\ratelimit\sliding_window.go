/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package ratelimit

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// SlidingWindowLimiter implements rate limiting using the sliding window algorithm
type SlidingWindowLimiter struct {
	client           *redis.Client
	config           Config
	action           Action
	tier             Tier
	keyPrefix        string
	logger           *logrus.Logger
	metricsCollector MetricsCollector
}

// NewSlidingWindowLimiter creates a new sliding window rate limiter
func NewSlidingWindowLimiter(
	client *redis.Client,
	config Config,
	action Action,
	tier Tier,
	keyPrefix string,
	logger *logrus.Logger,
	metricsCollector MetricsCollector,
) *SlidingWindowLimiter {
	return &SlidingWindowLimiter{
		client:           client,
		config:           config,
		action:           action,
		tier:             tier,
		keyPrefix:        keyPrefix,
		logger:           logger,
		metricsCollector: metricsCollector,
	}
}

// Allow checks if a single request is allowed
func (s *SlidingWindowLimiter) Allow(ctx context.Context, userID uuid.UUID, action Action) (*Result, error) {
	return s.AllowN(ctx, userID, action, 1)
}

// AllowN checks if N requests are allowed using sliding window algorithm
func (s *SlidingWindowLimiter) AllowN(ctx context.Context, userID uuid.UUID, action Action, n int) (*Result, error) {
	start := time.Now()
	defer func() {
		if s.metricsCollector != nil {
			s.metricsCollector.RecordLatency(s.action, s.tier, time.Since(start))
		}
	}()

	if !s.config.Enabled {
		return &Result{
			Allowed:   true,
			Limit:     s.config.Limit,
			Remaining: s.config.Limit,
			ResetTime: time.Now().Add(s.config.Window),
		}, nil
	}

	// Check if user is blocked
	blocked, blockedUntil, err := s.IsBlocked(ctx, userID, action)
	if err != nil {
		return nil, fmt.Errorf("failed to check block status: %w", err)
	}

	if blocked {
		result := &Result{
			Allowed:      false,
			Limit:        s.config.Limit,
			Remaining:    0,
			Blocked:      true,
			BlockedUntil: blockedUntil,
			RetryAfter:   time.Until(blockedUntil),
		}

		if s.metricsCollector != nil {
			s.metricsCollector.RecordRequest(s.action, s.tier, false)
		}

		return result, nil
	}

	// Execute sliding window algorithm using Lua script for atomicity
	windowKey := s.getWindowKey(userID)
	now := time.Now()
	windowStart := now.Add(-s.config.Window)

	luaScript := `
		local window_key = KEYS[1]
		local limit = tonumber(ARGV[1])
		local requests_to_add = tonumber(ARGV[2])
		local window_start = tonumber(ARGV[3])
		local now = tonumber(ARGV[4])
		local window_seconds = tonumber(ARGV[5])
		
		-- Remove expired entries from the sliding window
		redis.call('ZREMRANGEBYSCORE', window_key, 0, window_start)
		
		-- Get current count in the window
		local current_count = redis.call('ZCARD', window_key)
		
		-- Check if adding the requests would exceed the limit
		local allowed = (current_count + requests_to_add) <= limit
		local remaining = limit - current_count
		
		if allowed then
			-- Add the new requests to the window
			for i = 1, requests_to_add do
				-- Use microsecond precision to avoid collisions
				local score = now * 1000000 + i
				redis.call('ZADD', window_key, score, score)
			end
			remaining = remaining - requests_to_add
		end
		
		-- Set expiration for the key
		redis.call('EXPIRE', window_key, window_seconds * 2)
		
		-- Calculate reset time (when the oldest entry will expire)
		local oldest_entries = redis.call('ZRANGE', window_key, 0, 0, 'WITHSCORES')
		local reset_time = now + window_seconds
		if #oldest_entries > 0 then
			local oldest_score = tonumber(oldest_entries[2])
			local oldest_time = math.floor(oldest_score / 1000000)
			reset_time = oldest_time + window_seconds
		end
		
		return {
			allowed and 1 or 0,
			remaining,
			limit,
			reset_time,
			current_count + (allowed and requests_to_add or 0)
		}
	`

	result, err := s.client.Eval(ctx, luaScript, []string{windowKey},
		s.config.Limit,                 // limit
		n,                              // requests_to_add
		windowStart.UnixMicro(),        // window_start
		now.UnixMicro(),                // now
		int(s.config.Window.Seconds()), // window_seconds
	).Result()

	if err != nil {
		return nil, fmt.Errorf("failed to execute sliding window script: %w", err)
	}

	resultSlice := result.([]interface{})
	allowed := resultSlice[0].(int64) == 1
	remaining := int(resultSlice[1].(int64))
	limit := int(resultSlice[2].(int64))
	resetTime := time.Unix(resultSlice[3].(int64), 0)
	currentUsage := int(resultSlice[4].(int64))

	// If request is denied and we should block the user
	if !allowed && s.config.BlockDuration > 0 {
		// Check if we should block based on consecutive failures
		consecutiveFailures, err := s.getConsecutiveFailures(ctx, userID)
		if err != nil {
			s.logger.WithError(err).Warn("Failed to get consecutive failures")
		} else if consecutiveFailures >= 3 { // Block after 3 consecutive failures for sliding window
			blockErr := s.Block(ctx, userID, action, s.config.BlockDuration)
			if blockErr != nil {
				s.logger.WithError(blockErr).Warn("Failed to block user")
			} else {
				if s.metricsCollector != nil {
					s.metricsCollector.RecordBlock(s.action, s.tier, s.config.BlockDuration)
				}
			}
		}
	}

	// Record metrics
	if s.metricsCollector != nil {
		s.metricsCollector.RecordRequest(s.action, s.tier, allowed)
		s.metricsCollector.UpdateCurrentLimits(s.action, s.tier, limit, currentUsage)
	}

	// Update consecutive failures counter
	if allowed {
		s.resetConsecutiveFailures(ctx, userID)
	} else {
		s.incrementConsecutiveFailures(ctx, userID)
	}

	return &Result{
		Allowed:   allowed,
		Limit:     limit,
		Remaining: remaining,
		ResetTime: resetTime,
	}, nil
}

// Reserve reserves N tokens for future use (not applicable for sliding window)
func (s *SlidingWindowLimiter) Reserve(ctx context.Context, userID uuid.UUID, action Action, n int) (*Result, error) {
	// For sliding window, we can't really "reserve" requests, so we just check if they would be allowed
	return s.AllowN(ctx, userID, action, 0) // Check without consuming
}

// Reset resets the rate limit for a user and action
func (s *SlidingWindowLimiter) Reset(ctx context.Context, userID uuid.UUID, action Action) error {
	windowKey := s.getWindowKey(userID)
	failuresKey := s.getFailuresKey(userID)
	blockKey := s.getBlockKey(userID)

	pipe := s.client.Pipeline()
	pipe.Del(ctx, windowKey)
	pipe.Del(ctx, failuresKey)
	pipe.Del(ctx, blockKey)

	_, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to reset rate limit: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id": userID.String(),
		"action":  string(action),
		"tier":    string(s.tier),
	}).Info("Rate limit reset")

	return nil
}

// GetStats returns current statistics
func (s *SlidingWindowLimiter) GetStats(ctx context.Context, userID uuid.UUID, action Action) (*Stats, error) {
	windowKey := s.getWindowKey(userID)
	blockKey := s.getBlockKey(userID)

	now := time.Now()
	windowStart := now.Add(-s.config.Window)

	// Clean up expired entries and get current count
	pipe := s.client.Pipeline()
	pipe.ZRemRangeByScore(ctx, windowKey, "0", fmt.Sprintf("%d", windowStart.UnixMicro()))
	countCmd := pipe.ZCard(ctx, windowKey)
	blockCmd := pipe.Get(ctx, blockKey)

	_, err := pipe.Exec(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get stats: %w", err)
	}

	currentUsage := int(countCmd.Val())
	remaining := s.config.Limit - currentUsage

	// Check block status
	isBlocked := false
	var blockExpiry time.Time
	if blockResult := blockCmd.Val(); blockResult != "" {
		if blockTime, parseErr := strconv.ParseInt(blockResult, 10, 64); parseErr == nil {
			blockExpiry = time.UnixMilli(blockTime)
			isBlocked = time.Now().Before(blockExpiry)
		}
	}

	// Get oldest and newest entries for window calculation
	oldestEntries, _ := s.client.ZRangeWithScores(ctx, windowKey, 0, 0).Result()
	newestEntries, _ := s.client.ZRangeWithScores(ctx, windowKey, -1, -1).Result()

	var windowStartActual, lastRequest time.Time
	if len(oldestEntries) > 0 {
		windowStartActual = time.UnixMicro(int64(oldestEntries[0].Score))
	} else {
		windowStartActual = windowStart
	}

	if len(newestEntries) > 0 {
		lastRequest = time.UnixMicro(int64(newestEntries[0].Score))
	}

	return &Stats{
		Action:          action,
		UserID:          userID,
		Limit:           s.config.Limit,
		Usage:           currentUsage,
		Remaining:       remaining,
		WindowStart:     windowStartActual,
		WindowEnd:       windowStartActual.Add(s.config.Window),
		TotalRequests:   currentUsage,
		BlockedRequests: 0, // Would need additional tracking
		LastRequest:     lastRequest,
		IsBlocked:       isBlocked,
		BlockExpiry:     blockExpiry,
	}, nil
}

// Block manually blocks a user for a specific action
func (s *SlidingWindowLimiter) Block(ctx context.Context, userID uuid.UUID, action Action, duration time.Duration) error {
	blockKey := s.getBlockKey(userID)
	expiry := time.Now().Add(duration)

	err := s.client.Set(ctx, blockKey, expiry.UnixMilli(), duration).Err()
	if err != nil {
		return fmt.Errorf("failed to block user: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":  userID.String(),
		"action":   string(action),
		"tier":     string(s.tier),
		"duration": duration.String(),
		"expiry":   expiry.Format(time.RFC3339),
	}).Warn("User blocked")

	return nil
}

// Unblock manually unblocks a user for a specific action
func (s *SlidingWindowLimiter) Unblock(ctx context.Context, userID uuid.UUID, action Action) error {
	blockKey := s.getBlockKey(userID)

	err := s.client.Del(ctx, blockKey).Err()
	if err != nil {
		return fmt.Errorf("failed to unblock user: %w", err)
	}

	if s.metricsCollector != nil {
		s.metricsCollector.RecordUnblock(s.action, s.tier)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id": userID.String(),
		"action":  string(action),
		"tier":    string(s.tier),
	}).Info("User unblocked")

	return nil
}

// IsBlocked checks if a user is currently blocked
func (s *SlidingWindowLimiter) IsBlocked(ctx context.Context, userID uuid.UUID, action Action) (bool, time.Time, error) {
	blockKey := s.getBlockKey(userID)

	result, err := s.client.Get(ctx, blockKey).Result()
	if err == redis.Nil {
		return false, time.Time{}, nil
	}
	if err != nil {
		return false, time.Time{}, fmt.Errorf("failed to check block status: %w", err)
	}

	blockTime, err := strconv.ParseInt(result, 10, 64)
	if err != nil {
		return false, time.Time{}, fmt.Errorf("failed to parse block time: %w", err)
	}

	expiry := time.UnixMilli(blockTime)
	if time.Now().After(expiry) {
		// Block has expired, clean it up
		s.client.Del(ctx, blockKey)
		return false, time.Time{}, nil
	}

	return true, expiry, nil
}

// Helper methods

func (s *SlidingWindowLimiter) getWindowKey(userID uuid.UUID) string {
	return fmt.Sprintf("%s:window:%s:%s:%s", s.keyPrefix, string(s.action), string(s.tier), userID.String())
}

func (s *SlidingWindowLimiter) getBlockKey(userID uuid.UUID) string {
	return fmt.Sprintf("%s:block:%s:%s:%s", s.keyPrefix, string(s.action), string(s.tier), userID.String())
}

func (s *SlidingWindowLimiter) getFailuresKey(userID uuid.UUID) string {
	return fmt.Sprintf("%s:failures:%s:%s:%s", s.keyPrefix, string(s.action), string(s.tier), userID.String())
}

func (s *SlidingWindowLimiter) getConsecutiveFailures(ctx context.Context, userID uuid.UUID) (int, error) {
	failuresKey := s.getFailuresKey(userID)
	result, err := s.client.Get(ctx, failuresKey).Result()
	if err == redis.Nil {
		return 0, nil
	}
	if err != nil {
		return 0, err
	}

	failures, err := strconv.Atoi(result)
	if err != nil {
		return 0, err
	}

	return failures, nil
}

func (s *SlidingWindowLimiter) incrementConsecutiveFailures(ctx context.Context, userID uuid.UUID) {
	failuresKey := s.getFailuresKey(userID)
	pipe := s.client.Pipeline()
	pipe.Incr(ctx, failuresKey)
	pipe.Expire(ctx, failuresKey, s.config.Window*2) // Expire after 2 windows
	_, _ = pipe.Exec(ctx)
}

func (s *SlidingWindowLimiter) resetConsecutiveFailures(ctx context.Context, userID uuid.UUID) {
	failuresKey := s.getFailuresKey(userID)
	s.client.Del(ctx, failuresKey)
}
