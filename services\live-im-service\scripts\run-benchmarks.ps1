# Live IM Service Benchmark Runner for PowerShell
# Copyright (c) 2025 Cina.Club
# All rights reserved.

param(
    [switch]$SkipRedis = $false,
    [string]$OutputDir = "./benchmark-results"
)

# Configuration
$BenchmarkDir = "./test/benchmark"
$Timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$ResultsFile = "$OutputDir/benchmark_results_$Timestamp.txt"

# Create results directory
if (!(Test-Path $OutputDir)) {
    New-Item -ItemType Directory -Path $OutputDir -Force | Out-Null
}

Write-Host "=== Live IM Service Performance Benchmarks ===" -ForegroundColor Blue
Write-Host "Timestamp: $(Get-Date)" -ForegroundColor Blue
Write-Host "Results will be saved to: $ResultsFile" -ForegroundColor Blue
Write-Host ""

# Function to run benchmark and capture results
function Run-Benchmark {
    param(
        [string]$Name,
        [string]$Pattern,
        [string]$Description
    )
    
    Write-Host "Running $Name benchmarks..." -ForegroundColor Yellow
    Add-Content -Path $ResultsFile -Value "=== $Name Benchmarks - $Description ==="
    Add-Content -Path $ResultsFile -Value "Timestamp: $(Get-Date)"
    Add-Content -Path $ResultsFile -Value ""
    
    try {
        $output = go test -bench=$Pattern -benchmem $BenchmarkDir 2>&1
        Add-Content -Path $ResultsFile -Value $output
        Write-Host "✅ $Name benchmarks completed successfully" -ForegroundColor Green
        return $true
    }
    catch {
        Write-Host "❌ $Name benchmarks failed" -ForegroundColor Red
        Add-Content -Path $ResultsFile -Value "ERROR: $($_.Exception.Message)"
        return $false
    }
    finally {
        Add-Content -Path $ResultsFile -Value ""
        Add-Content -Path $ResultsFile -Value "----------------------------------------"
        Add-Content -Path $ResultsFile -Value ""
    }
}

# Function to check if Redis is available
function Test-Redis {
    try {
        $null = redis-cli ping 2>$null
        return $true
    }
    catch {
        return $false
    }
}

# Start benchmarking
Add-Content -Path $ResultsFile -Value "Starting benchmark suite..."
Add-Content -Path $ResultsFile -Value "System Information:"
Add-Content -Path $ResultsFile -Value "OS: $($env:OS)"
Add-Content -Path $ResultsFile -Value "Computer: $($env:COMPUTERNAME)"
Add-Content -Path $ResultsFile -Value "User: $($env:USERNAME)"
$goVersion = go version
Add-Content -Path $ResultsFile -Value "Go Version: $goVersion"
Add-Content -Path $ResultsFile -Value ""

$allPassed = $true

# Run individual benchmark suites
Write-Host "1. Message Processing Benchmarks" -ForegroundColor Blue
$result = Run-Benchmark -Name "Message" -Pattern "BenchmarkMessage" -Description "Message creation, cloning, and validation"
$allPassed = $allPassed -and $result

Write-Host "2. JSON Serialization Benchmarks" -ForegroundColor Blue
$result = Run-Benchmark -Name "Serialization" -Pattern "BenchmarkJSON" -Description "JSON serialization and deserialization"
$allPassed = $allPassed -and $result

Write-Host "3. Concurrent Operations Benchmarks" -ForegroundColor Blue
$result = Run-Benchmark -Name "Concurrent" -Pattern "Concurrent" -Description "Concurrent message processing"
$allPassed = $allPassed -and $result

Write-Host "4. Memory Allocation Benchmarks" -ForegroundColor Blue
$result = Run-Benchmark -Name "Memory" -Pattern "BenchmarkSerializationMemoryAllocation" -Description "Memory allocation patterns"
$allPassed = $allPassed -and $result

# Redis benchmarks (optional)
if (-not $SkipRedis -and (Test-Redis)) {
    Write-Host "5. Redis Operations Benchmarks" -ForegroundColor Blue
    Write-Host "Redis server detected, running Redis benchmarks..." -ForegroundColor Green
    $result = Run-Benchmark -Name "Redis" -Pattern "BenchmarkRedis" -Description "Redis operations performance"
    $allPassed = $allPassed -and $result
} else {
    Write-Host "5. Redis Operations Benchmarks" -ForegroundColor Blue
    if ($SkipRedis) {
        Write-Host "⚠️  Redis benchmarks skipped by user request" -ForegroundColor Yellow
    } else {
        Write-Host "⚠️  Redis server not available, skipping Redis benchmarks" -ForegroundColor Yellow
    }
    Add-Content -Path $ResultsFile -Value "=== Redis Benchmarks - SKIPPED ==="
    Add-Content -Path $ResultsFile -Value "Redis server not available or skipped"
    Add-Content -Path $ResultsFile -Value ""
}

# Generate summary
Write-Host "6. Generating Performance Summary" -ForegroundColor Blue
Add-Content -Path $ResultsFile -Value "=== Performance Summary ==="
Add-Content -Path $ResultsFile -Value "Generated: $(Get-Date)"
Add-Content -Path $ResultsFile -Value ""

# Extract key metrics from results
$content = Get-Content $ResultsFile
$messageResults = $content | Where-Object { $_ -match "BenchmarkMessageCreation" } | Select-Object -First 6
$serializationResults = $content | Where-Object { $_ -match "BenchmarkJSONSerialization" } | Select-Object -First 5

if ($messageResults) {
    Add-Content -Path $ResultsFile -Value "Message Creation Performance:"
    $messageResults | ForEach-Object { Add-Content -Path $ResultsFile -Value $_ }
    Add-Content -Path $ResultsFile -Value ""
}

if ($serializationResults) {
    Add-Content -Path $ResultsFile -Value "Serialization Performance:"
    $serializationResults | ForEach-Object { Add-Content -Path $ResultsFile -Value $_ }
    Add-Content -Path $ResultsFile -Value ""
}

# Performance analysis
Add-Content -Path $ResultsFile -Value "Performance Analysis:"
$barrageResult = $content | Where-Object { $_ -match "BarrageMessage-" } | Select-Object -First 1
if ($barrageResult) {
    $perf = ($barrageResult -split '\s+')[2]
    Add-Content -Path $ResultsFile -Value "- Message creation: $perf"
}

$serializeResult = $content | Where-Object { $_ -match "SerializeSimpleMessage-" } | Select-Object -First 1
if ($serializeResult) {
    $perf = ($serializeResult -split '\s+')[2]
    Add-Content -Path $ResultsFile -Value "- JSON serialization: $perf"
}
Add-Content -Path $ResultsFile -Value ""

# Recommendations
Add-Content -Path $ResultsFile -Value "Recommendations:"
Add-Content -Path $ResultsFile -Value "1. Monitor message creation performance (target: <1000 ns/op)"
Add-Content -Path $ResultsFile -Value "2. Optimize large message serialization if >10000 ns/op"
Add-Content -Path $ResultsFile -Value "3. Consider object pooling for high-allocation operations"
Add-Content -Path $ResultsFile -Value "4. Run benchmarks regularly to detect performance regressions"

Write-Host ""
Write-Host "=== Benchmark Suite Completed ===" -ForegroundColor Green
Write-Host "Results saved to: $ResultsFile" -ForegroundColor Green
Write-Host ""

Write-Host "Quick Summary:" -ForegroundColor Blue
if ($allPassed) {
    Write-Host "✅ All benchmarks completed successfully" -ForegroundColor Green
} else {
    Write-Host "❌ Some benchmarks failed - check results file" -ForegroundColor Red
}

# Display key metrics
$barrageResult = $content | Where-Object { $_ -match "BenchmarkMessageCreation/BarrageMessage-" } | Select-Object -First 1
if ($barrageResult) {
    $perf = ($barrageResult -split '\s+')[2]
    Write-Host "📊 Message Creation: $perf" -ForegroundColor Blue
}

$serializeResult = $content | Where-Object { $_ -match "BenchmarkJSONSerialization/SerializeSimpleMessage-" } | Select-Object -First 1
if ($serializeResult) {
    $perf = ($serializeResult -split '\s+')[2]
    Write-Host "📊 JSON Serialization: $perf" -ForegroundColor Blue
}

Write-Host ""
Write-Host "💡 To analyze results in detail:" -ForegroundColor Yellow
Write-Host "   Get-Content '$ResultsFile'" -ForegroundColor Yellow
Write-Host "   or open the file in your preferred editor" -ForegroundColor Yellow
Write-Host ""
Write-Host "💡 To run specific benchmarks:" -ForegroundColor Yellow
Write-Host "   go test -bench=BenchmarkMessage -benchmem ./test/benchmark" -ForegroundColor Yellow
Write-Host "   go test -bench=BenchmarkJSON -benchmem ./test/benchmark" -ForegroundColor Yellow
Write-Host ""
Write-Host "Happy benchmarking! 🚀" -ForegroundColor Blue
