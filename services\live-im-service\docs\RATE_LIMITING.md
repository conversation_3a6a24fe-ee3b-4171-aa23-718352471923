# Rate Limiting Guide

This document describes the comprehensive rate limiting system implemented in the Live IM Service.

## Overview

The Live IM Service implements a sophisticated rate limiting system that provides:

- **Multiple Algorithms**: Token bucket, sliding window, fixed window, and leaky bucket
- **Tier-based Limiting**: Different limits for different user tiers (Anonymous, Basic, Premium, VIP, Admin)
- **Action-specific Limits**: Granular control over different types of actions
- **Redis-backed Storage**: Distributed rate limiting with Redis
- **Automatic Blocking**: Temporary blocking for abusive users
- **Comprehensive Metrics**: Detailed monitoring and alerting
- **HTTP/WebSocket Middleware**: Easy integration with web services

## Rate Limiting Algorithms

### Token Bucket Algorithm

The token bucket algorithm maintains a bucket of tokens that are consumed by requests and refilled at a constant rate.

**Characteristics:**
- Allows burst traffic up to bucket capacity
- Smooth rate limiting with configurable refill rate
- Good for APIs that need to handle occasional spikes

**Configuration:**
```go
config := ratelimit.Config{
    Algorithm:   ratelimit.AlgorithmTokenBucket,
    Limit:       100,           // Maximum requests per window
    Burst:       20,            // Bucket capacity (burst size)
    Window:      time.Minute,   // Time window
    RefillRate:  time.Second,   // Token refill interval
}
```

### Sliding Window Algorithm

The sliding window algorithm tracks requests in a rolling time window, providing more accurate rate limiting.

**Characteristics:**
- Precise rate limiting without window edge effects
- Memory efficient with automatic cleanup
- Better for strict rate limiting requirements

**Configuration:**
```go
config := ratelimit.Config{
    Algorithm:   ratelimit.AlgorithmSlidingWindow,
    Limit:       100,           // Maximum requests per window
    Window:      time.Minute,   // Sliding window duration
}
```

## User Tiers

The system supports different user tiers with varying rate limits:

### Anonymous Users
- **Tier**: `TierAnonymous`
- **Characteristics**: Lowest limits, IP-based identification
- **Use Case**: Unauthenticated users

### Basic Users
- **Tier**: `TierBasic`
- **Characteristics**: Standard limits for registered users
- **Use Case**: Regular authenticated users

### Premium Users
- **Tier**: `TierPremium`
- **Characteristics**: Higher limits for paying customers
- **Use Case**: Subscription-based users

### VIP Users
- **Tier**: `TierVIP`
- **Characteristics**: Very high limits for special users
- **Use Case**: Influencers, content creators

### Admin Users
- **Tier**: `TierAdmin`
- **Characteristics**: Highest limits or no limits
- **Use Case**: System administrators

## Action Types

Different actions have different rate limiting requirements:

### Barrage/Chat Messages
- **Action**: `ActionBarrage`
- **Typical Limits**: 1-5 messages per second
- **Use Case**: Chat messages, comments

### Like Actions
- **Action**: `ActionLike`
- **Typical Limits**: 10-50 likes per minute
- **Use Case**: Like buttons, reactions

### Gift Sending
- **Action**: `ActionGift`
- **Typical Limits**: 1-3 gifts per minute
- **Use Case**: Virtual gifts, donations

### Connection Attempts
- **Action**: `ActionConnection`
- **Typical Limits**: 10-100 connections per minute
- **Use Case**: WebSocket connections, login attempts

### API Calls
- **Action**: `ActionAPI`
- **Typical Limits**: 100-1000 requests per minute
- **Use Case**: General API endpoints

### File Operations
- **Actions**: `ActionUpload`, `ActionDownload`
- **Typical Limits**: 5-20 operations per minute
- **Use Case**: File uploads and downloads

## Configuration

### Manager Configuration

```go
config := ratelimit.ManagerConfig{
    Default: ratelimit.Config{
        Algorithm:   ratelimit.AlgorithmSlidingWindow,
        Enabled:     true,
        Limit:       100,
        Window:      time.Minute,
        BlockDuration: 5 * time.Minute,
    },
    Actions: map[ratelimit.Action]ratelimit.ActionConfig{
        ratelimit.ActionBarrage: {
            Base: ratelimit.Config{
                Algorithm: ratelimit.AlgorithmTokenBucket,
                Limit:     5,
                Burst:     10,
                Window:    time.Minute,
                RefillRate: 10 * time.Second,
            },
            Tiers: map[ratelimit.Tier]ratelimit.TierConfig{
                ratelimit.TierAnonymous: {Multiplier: 0.5},
                ratelimit.TierBasic:     {Multiplier: 1.0},
                ratelimit.TierPremium:   {Multiplier: 2.0},
                ratelimit.TierVIP:       {Multiplier: 5.0},
                ratelimit.TierAdmin:     {Multiplier: 10.0},
            },
        },
        ratelimit.ActionLike: {
            Base: ratelimit.Config{
                Algorithm: ratelimit.AlgorithmSlidingWindow,
                Limit:     30,
                Window:    time.Minute,
            },
            Tiers: map[ratelimit.Tier]ratelimit.TierConfig{
                ratelimit.TierAnonymous: {Multiplier: 0.3},
                ratelimit.TierBasic:     {Multiplier: 1.0},
                ratelimit.TierPremium:   {Multiplier: 2.0},
            },
        },
    },
    EnableMetrics:    true,
    EnableLogging:    true,
    KeyPrefix:        "live_im_ratelimit",
    CleanupInterval:  5 * time.Minute,
}
```

### HTTP Middleware Configuration

```go
middlewareConfig := ratelimit.MiddlewareConfig{
    SkipPaths: []string{
        "/health",
        "/metrics",
        "/static",
    },
    SkipMethods: []string{"OPTIONS"},
    DefaultAction: ratelimit.ActionAPI,
    DefaultTier:   ratelimit.TierBasic,
    Headers: ratelimit.HeaderConfig{
        IncludeHeaders: true,
        LimitHeader:    "X-RateLimit-Limit",
        RemainingHeader: "X-RateLimit-Remaining",
        ResetHeader:    "X-RateLimit-Reset",
        RetryAfterHeader: "Retry-After",
    },
}
```

## Usage Examples

### Basic Setup

```go
// Create Redis client
redisClient := redis.NewClient(&redis.Options{
    Addr: "localhost:6379",
})

// Create rate limiting manager
manager := ratelimit.NewRateLimitManager(
    redisClient,
    config,
    logger,
    metricsCollector,
    eventHandler,
)

// Create HTTP middleware
middleware := ratelimit.NewHTTPMiddleware(manager, logger, middlewareConfig)

// Apply to Gin router
router.Use(middleware.Middleware())
```

### Manual Rate Limiting

```go
// Check if user can perform action
userID := uuid.New()
result, err := manager.Allow(ctx, userID, ratelimit.ActionBarrage, ratelimit.TierBasic)
if err != nil {
    return err
}

if !result.Allowed {
    if result.Blocked {
        return fmt.Errorf("user blocked until %v", result.BlockedUntil)
    }
    return fmt.Errorf("rate limit exceeded, try again in %v", result.RetryAfter)
}

// Process the request
processBarrageMessage(message)
```

### Endpoint-specific Rate Limiting

```go
// Apply specific rate limiting to sensitive endpoints
api.POST("/barrage", 
    middleware.EndpointMiddleware(ratelimit.ActionBarrage, ratelimit.TierBasic),
    handleBarrage,
)

api.POST("/gift", 
    middleware.EndpointMiddleware(ratelimit.ActionGift, ratelimit.TierBasic),
    handleGift,
)

api.POST("/upload", 
    middleware.EndpointMiddleware(ratelimit.ActionUpload, ratelimit.TierBasic),
    handleUpload,
)
```

### WebSocket Rate Limiting

```go
wsMiddleware := ratelimit.NewWebSocketMiddleware(manager, logger)

// Check connection rate limit
result, err := wsMiddleware.CheckConnection(userID, userTier)
if err != nil || !result.Allowed {
    ws.Close()
    return
}

// Check message rate limit
result, err = wsMiddleware.CheckMessage(userID, ratelimit.ActionBarrage, userTier)
if err != nil || !result.Allowed {
    // Drop message or send error
    return
}
```

## HTTP Headers

When rate limiting headers are enabled, the following headers are included in responses:

```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1625097600
Retry-After: 60
```

## Error Responses

### Rate Limit Exceeded

```json
{
  "error": "Rate limit exceeded",
  "message": "Too many requests",
  "limit": 100,
  "remaining": 0,
  "reset_time": "2025-07-11T10:30:00Z",
  "retry_after": 60
}
```

### User Blocked

```json
{
  "error": "User temporarily blocked",
  "message": "Too many failed requests",
  "blocked_until": "2025-07-11T10:35:00Z",
  "retry_after": 300
}
```

## Monitoring and Metrics

### Prometheus Metrics

The rate limiting system exposes the following metrics:

```
# Request metrics
rate_limit_requests_total{action="barrage",tier="basic",allowed="true"} 1500
rate_limit_requests_total{action="barrage",tier="basic",allowed="false"} 50

# Block metrics
rate_limit_blocks_total{action="barrage",tier="basic"} 5
rate_limit_block_duration_seconds{action="barrage",tier="basic"} 300

# Current limits
rate_limit_current_limit{action="barrage",tier="basic"} 100
rate_limit_current_usage{action="barrage",tier="basic"} 75

# Latency metrics
rate_limit_check_duration_seconds{action="barrage",tier="basic"} 0.001
```

### Statistics API

```go
// Get user statistics
stats, err := manager.GetUserStats(ctx, userID)
for action, stat := range stats {
    fmt.Printf("Action: %s, Usage: %d/%d, Remaining: %d\n", 
        action, stat.Usage, stat.Limit, stat.Remaining)
}

// Get action statistics
actionStats, err := manager.GetActionStats(ctx, ratelimit.ActionBarrage)

// Get global statistics
globalStats, err := manager.GetGlobalStats(ctx)
```

## Best Practices

### Algorithm Selection

1. **Token Bucket**: Use for APIs that need to handle burst traffic
2. **Sliding Window**: Use for strict rate limiting requirements
3. **Fixed Window**: Use for simple, less precise rate limiting
4. **Leaky Bucket**: Use for smooth, consistent rate limiting

### Tier Configuration

1. **Anonymous Users**: Conservative limits to prevent abuse
2. **Authenticated Users**: Reasonable limits for normal usage
3. **Premium Users**: Higher limits as a service benefit
4. **Admin Users**: Very high or no limits for operational needs

### Action Granularity

1. **Separate Actions**: Use different actions for different types of operations
2. **Appropriate Limits**: Set limits based on the cost and impact of each action
3. **Business Logic**: Align rate limits with business requirements

### Error Handling

1. **Graceful Degradation**: Handle rate limiting errors gracefully
2. **User Feedback**: Provide clear error messages with retry information
3. **Fallback Mechanisms**: Implement fallbacks when rate limiting fails

### Performance Optimization

1. **Redis Optimization**: Use Redis pipelines and Lua scripts for atomicity
2. **Caching**: Cache rate limiting results when appropriate
3. **Cleanup**: Regular cleanup of expired rate limiting data
4. **Monitoring**: Monitor rate limiting performance and adjust as needed

## Troubleshooting

### Common Issues

1. **High Latency**: Check Redis performance and network connectivity
2. **False Positives**: Review rate limiting thresholds and algorithms
3. **Memory Usage**: Monitor Redis memory usage and implement cleanup
4. **Configuration Errors**: Validate rate limiting configuration

### Debug Mode

Enable detailed logging for troubleshooting:

```go
logger.SetLevel(logrus.DebugLevel)
```

### Testing Rate Limits

```bash
# Test rate limiting with curl
for i in {1..10}; do
  curl -H "X-User-ID: test-user" http://localhost:8080/api/barrage
  sleep 1
done
```

## Security Considerations

1. **IP-based Fallback**: Use IP-based rate limiting for anonymous users
2. **Distributed Attacks**: Implement global rate limiting for distributed attacks
3. **Bypass Prevention**: Ensure rate limiting cannot be easily bypassed
4. **Monitoring**: Monitor for rate limiting bypass attempts
5. **Escalation**: Implement escalating penalties for repeated violations
