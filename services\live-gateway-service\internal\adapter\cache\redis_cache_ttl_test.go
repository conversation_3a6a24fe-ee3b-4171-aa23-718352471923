package cache

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"cina.club/services/live-gateway-service/internal/domain/model"
)

func TestRedisCacheTTLManagement(t *testing.T) {
	cache, mr, cleanup := setupTestRedis(t)
	defer cleanup()

	ctx := context.Background()

	tests := []struct {
		name     string
		ttl      time.Duration
		keyType  string
		setupKey func() string
	}{
		{
			name:    "Stream mapping TTL",
			ttl:     cache.config.StreamMappingTTL,
			keyType: "stream_mapping",
			setupKey: func() string {
				mapping := &model.StreamMapping{
					StreamKey: "test-stream",
					RoomID:    uuid.New(),
					UserID:    uuid.New(),
				}
				err := cache.StoreStreamMapping(ctx, mapping)
				require.NoError(t, err)
				return cache.keyGen.StreamMappingKey(mapping.StreamKey)
			},
		},
		{
			name:    "Stream stats TTL",
			ttl:     cache.config.StreamStatsTTL,
			keyType: "stream_stats",
			setupKey: func() string {
				stats := &model.StreamStats{
					VideoBitrate: 5000000,
					AudioBitrate: 128000,
				}
				err := cache.StoreStreamStats(ctx, "test-stream", stats)
				require.NoError(t, err)
				return cache.keyGen.StreamStatsKey("test-stream")
			},
		},
		{
			name:    "Node load TTL",
			ttl:     cache.config.NodeLoadTTL,
			keyType: "node_load",
			setupKey: func() string {
				load := &model.NodeLoad{
					NodeID:    "test-node",
					LoadScore: 0.75,
				}
				err := cache.StoreNodeLoad(ctx, load.NodeID, load)
				require.NoError(t, err)
				return cache.keyGen.NodeLoadKey(load.NodeID)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 设置键并验证TTL
			key := tt.setupKey()
			assert.True(t, mr.Exists(key))
			ttl := mr.TTL(key)
			assert.Equal(t, tt.ttl, ttl)

			// 快进一半TTL时间
			mr.FastForward(tt.ttl / 2)
			assert.True(t, mr.Exists(key))

			// 快进剩余TTL时间
			mr.FastForward(tt.ttl / 2)
			assert.False(t, mr.Exists(key))
		})
	}
}

func TestRedisCacheTTLUpdate(t *testing.T) {
	cache, mr, cleanup := setupTestRedis(t)
	defer cleanup()

	ctx := context.Background()

	// 测试流映射TTL更新
	t.Run("Update stream mapping TTL", func(t *testing.T) {
		mapping := &model.StreamMapping{
			StreamKey: "test-stream",
			RoomID:    uuid.New(),
			UserID:    uuid.New(),
		}

		// 首次存储
		err := cache.StoreStreamMapping(ctx, mapping)
		require.NoError(t, err)

		key := cache.keyGen.StreamMappingKey(mapping.StreamKey)
		initialTTL := mr.TTL(key)

		// 快进一半时间
		mr.FastForward(cache.config.StreamMappingTTL / 2)
		midTTL := mr.TTL(key)
		assert.Less(t, midTTL, initialTTL)

		// 更新映射
		err = cache.StoreStreamMapping(ctx, mapping)
		require.NoError(t, err)

		// 验证TTL被重置
		newTTL := mr.TTL(key)
		assert.Equal(t, initialTTL, newTTL)
	})

	// 测试流统计TTL更新
	t.Run("Update stream stats TTL", func(t *testing.T) {
		stats := &model.StreamStats{
			VideoBitrate: 5000000,
			AudioBitrate: 128000,
		}

		// 首次存储
		err := cache.StoreStreamStats(ctx, "test-stream", stats)
		require.NoError(t, err)

		key := cache.keyGen.StreamStatsKey("test-stream")
		initialTTL := mr.TTL(key)

		// 快进一半时间
		mr.FastForward(cache.config.StreamStatsTTL / 2)
		midTTL := mr.TTL(key)
		assert.Less(t, midTTL, initialTTL)

		// 更新统计信息
		err = cache.StoreStreamStats(ctx, "test-stream", stats)
		require.NoError(t, err)

		// 验证TTL被重置
		newTTL := mr.TTL(key)
		assert.Equal(t, initialTTL, newTTL)
	})

	// 测试节点负载TTL更新
	t.Run("Update node load TTL", func(t *testing.T) {
		load := &model.NodeLoad{
			NodeID:    "test-node",
			LoadScore: 0.75,
		}

		// 首次存储
		err := cache.StoreNodeLoad(ctx, load.NodeID, load)
		require.NoError(t, err)

		key := cache.keyGen.NodeLoadKey(load.NodeID)
		initialTTL := mr.TTL(key)

		// 快进一半时间
		mr.FastForward(cache.config.NodeLoadTTL / 2)
		midTTL := mr.TTL(key)
		assert.Less(t, midTTL, initialTTL)

		// 更新负载信息
		err = cache.StoreNodeLoad(ctx, load.NodeID, load)
		require.NoError(t, err)

		// 验证TTL被重置
		newTTL := mr.TTL(key)
		assert.Equal(t, initialTTL, newTTL)
	})
}

func TestRedisCacheTTLExpiration(t *testing.T) {
	cache, mr, cleanup := setupTestRedis(t)
	defer cleanup()

	ctx := context.Background()

	// 测试流映射过期处理
	t.Run("Stream mapping expiration", func(t *testing.T) {
		mapping := &model.StreamMapping{
			StreamKey: "test-stream",
			RoomID:    uuid.New(),
			UserID:    uuid.New(),
		}

		err := cache.StoreStreamMapping(ctx, mapping)
		require.NoError(t, err)

		// 快进超过TTL时间
		mr.FastForward(cache.config.StreamMappingTTL + time.Second)

		// 验证数据已过期
		result, err := cache.GetStreamMapping(ctx, mapping.StreamKey)
		assert.NoError(t, err)
		assert.Nil(t, result)
	})

	// 测试流统计过期处理
	t.Run("Stream stats expiration", func(t *testing.T) {
		stats := &model.StreamStats{
			VideoBitrate: 5000000,
			AudioBitrate: 128000,
		}

		err := cache.StoreStreamStats(ctx, "test-stream", stats)
		require.NoError(t, err)

		// 快进超过TTL时间
		mr.FastForward(cache.config.StreamStatsTTL + time.Second)

		// 验证数据已过期
		result, err := cache.GetStreamStats(ctx, "test-stream")
		assert.NoError(t, err)
		assert.Nil(t, result)
	})

	// 测试节点负载过期处理
	t.Run("Node load expiration", func(t *testing.T) {
		load := &model.NodeLoad{
			NodeID:    "test-node",
			LoadScore: 0.75,
		}

		err := cache.StoreNodeLoad(ctx, load.NodeID, load)
		require.NoError(t, err)

		// 快进超过TTL时间
		mr.FastForward(cache.config.NodeLoadTTL + time.Second)

		// 验证数据已过期
		result, err := cache.GetNodeLoad(ctx, load.NodeID)
		assert.NoError(t, err)
		assert.Nil(t, result)

		// 验证节点列表不包含过期节点
		loads, err := cache.ListNodeLoads(ctx)
		assert.NoError(t, err)
		assert.NotContains(t, loads, load.NodeID)
	})
}

func TestRedisCacheTTLBoundary(t *testing.T) {
	cache, mr, cleanup := setupTestRedis(t)
	defer cleanup()

	ctx := context.Background()

	// 测试零TTL
	t.Run("Zero TTL", func(t *testing.T) {
		cache.config.StreamMappingTTL = 0
		mapping := &model.StreamMapping{
			StreamKey: "test-stream",
			RoomID:    uuid.New(),
			UserID:    uuid.New(),
		}

		err := cache.StoreStreamMapping(ctx, mapping)
		assert.Error(t, err)

		// Verify key doesn't exist in Redis
		key := cache.keyGen.StreamMappingKey(mapping.StreamKey)
		assert.False(t, mr.Exists(key))
	})

	// 测试负TTL
	t.Run("Negative TTL", func(t *testing.T) {
		cache.config.StreamStatsTTL = -time.Hour
		stats := &model.StreamStats{
			VideoBitrate: 5000000,
			AudioBitrate: 128000,
		}

		err := cache.StoreStreamStats(ctx, "test-stream", stats)
		assert.Error(t, err)

		// Verify key doesn't exist in Redis
		key := cache.keyGen.StreamStatsKey("test-stream")
		assert.False(t, mr.Exists(key))
	})

	// 测试极大TTL
	t.Run("Very large TTL", func(t *testing.T) {
		cache.config.NodeLoadTTL = time.Hour * 24 * 365 * 10 // 10年
		load := &model.NodeLoad{
			NodeID:    "test-node",
			LoadScore: 0.75,
		}

		err := cache.StoreNodeLoad(ctx, load.NodeID, load)
		assert.Error(t, err)

		// Verify key doesn't exist in Redis
		key := cache.keyGen.NodeLoadKey(load.NodeID)
		assert.False(t, mr.Exists(key))
	})
}

func TestRedisCacheTTLConcurrent(t *testing.T) {
	cache, mr, cleanup := setupTestRedis(t)
	defer cleanup()

	ctx := context.Background()
	const numOperations = 100

	// 测试并发TTL更新
	t.Run("Concurrent TTL updates", func(t *testing.T) {
		load := &model.NodeLoad{
			NodeID:    "test-node",
			LoadScore: 0.75,
		}

		// 首次存储
		err := cache.StoreNodeLoad(ctx, load.NodeID, load)
		require.NoError(t, err)

		key := cache.keyGen.NodeLoadKey(load.NodeID)
		initialTTL := mr.TTL(key)

		// 快进一半时间
		mr.FastForward(cache.config.NodeLoadTTL / 2)

		// 并发更新
		done := make(chan bool)
		for i := 0; i < numOperations; i++ {
			go func() {
				err := cache.StoreNodeLoad(ctx, load.NodeID, load)
				assert.NoError(t, err)
				done <- true
			}()
		}

		// 等待所有更新完成
		for i := 0; i < numOperations; i++ {
			<-done
		}

		// 验证TTL被重置
		newTTL := mr.TTL(key)
		assert.Equal(t, initialTTL, newTTL)
	})
}
