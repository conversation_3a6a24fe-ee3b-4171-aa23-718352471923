/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:12:13
Modified: 2025-07-11 10:12:13
*/

// Code generated by protoc-gen-go. DO NOT EDIT.
// Manual implementation for testing purposes

package live_im

import (
	"google.golang.org/protobuf/reflect/protoreflect"
	"google.golang.org/protobuf/runtime/protoimpl"
	"google.golang.org/protobuf/types/known/timestamppb"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// MessageType defines the type of message
type MessageType int32

const (
	MessageType_MESSAGE_TYPE_UNSPECIFIED  MessageType = 0
	MessageType_MESSAGE_TYPE_AUTH         MessageType = 1
	MessageType_MESSAGE_TYPE_AUTH_RESULT  MessageType = 2
	MessageType_MESSAGE_TYPE_BARRAGE      MessageType = 3
	MessageType_MESSAGE_TYPE_LIKE         MessageType = 4
	MessageType_MESSAGE_TYPE_GIFT         MessageType = 5
	MessageType_MESSAGE_TYPE_PING         MessageType = 6
	MessageType_MESSAGE_TYPE_PONG         MessageType = 7
	MessageType_MESSAGE_TYPE_ERROR        MessageType = 8
	MessageType_MESSAGE_TYPE_RATE_LIMITED MessageType = 9
	MessageType_MESSAGE_TYPE_NEW_BARRAGE  MessageType = 10
	MessageType_MESSAGE_TYPE_LIKE_BURST   MessageType = 11
	MessageType_MESSAGE_TYPE_NEW_GIFT     MessageType = 12
	MessageType_MESSAGE_TYPE_USER_JOIN    MessageType = 13
	MessageType_MESSAGE_TYPE_USER_LEAVE   MessageType = 14
	MessageType_MESSAGE_TYPE_ROOM_UPDATE  MessageType = 15
)

// UserRole defines user roles
type UserRole int32

const (
	UserRole_USER_ROLE_UNSPECIFIED UserRole = 0
	UserRole_USER_ROLE_VIEWER      UserRole = 1
	UserRole_USER_ROLE_STREAMER    UserRole = 2
	UserRole_USER_ROLE_MODERATOR   UserRole = 3
	UserRole_USER_ROLE_ADMIN       UserRole = 4
	UserRole_USER_ROLE_VIP         UserRole = 5
)

// User represents a user
type User struct {
	UserId   string   `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	Username string   `protobuf:"bytes,2,opt,name=username,proto3" json:"username,omitempty"`
	Avatar   string   `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Role     UserRole `protobuf:"varint,4,opt,name=role,proto3,enum=live_im.UserRole" json:"role,omitempty"`
	Level    int32    `protobuf:"varint,5,opt,name=level,proto3" json:"level,omitempty"`
	VipLevel int32    `protobuf:"varint,6,opt,name=vip_level,json=vipLevel,proto3" json:"vip_level,omitempty"`
	Badges   []string `protobuf:"bytes,7,rep,name=badges,proto3" json:"badges,omitempty"`
}

// RoomInfo represents room information
type RoomInfo struct {
	RoomId         string                 `protobuf:"bytes,1,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	Title          string                 `protobuf:"bytes,2,opt,name=title,proto3" json:"title,omitempty"`
	Status         string                 `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`
	OnlineCount    int32                  `protobuf:"varint,4,opt,name=online_count,json=onlineCount,proto3" json:"online_count,omitempty"`
	StartTime      *timestamppb.Timestamp `protobuf:"bytes,5,opt,name=start_time,json=startTime,proto3" json:"start_time,omitempty"`
	StreamerId     string                 `protobuf:"bytes,6,opt,name=streamer_id,json=streamerId,proto3" json:"streamer_id,omitempty"`
	StreamerName   string                 `protobuf:"bytes,7,opt,name=streamer_name,json=streamerName,proto3" json:"streamer_name,omitempty"`
	StreamerAvatar string                 `protobuf:"bytes,8,opt,name=streamer_avatar,json=streamerAvatar,proto3" json:"streamer_avatar,omitempty"`
}

// Message represents a live IM message
type Message struct {
	MessageId    string                 `protobuf:"bytes,1,opt,name=message_id,json=messageId,proto3" json:"message_id,omitempty"`
	Type         MessageType            `protobuf:"varint,2,opt,name=type,proto3,enum=live_im.MessageType" json:"type,omitempty"`
	Timestamp    *timestamppb.Timestamp `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Token        string                 `protobuf:"bytes,4,opt,name=token,proto3" json:"token,omitempty"`
	RoomId       string                 `protobuf:"bytes,5,opt,name=room_id,json=roomId,proto3" json:"room_id,omitempty"`
	Content      string                 `protobuf:"bytes,6,opt,name=content,proto3" json:"content,omitempty"`
	Count        int32                  `protobuf:"varint,7,opt,name=count,proto3" json:"count,omitempty"`
	GiftId       string                 `protobuf:"bytes,8,opt,name=gift_id,json=giftId,proto3" json:"gift_id,omitempty"`
	ToUserId     string                 `protobuf:"bytes,9,opt,name=to_user_id,json=toUserId,proto3" json:"to_user_id,omitempty"`
	FromUser     *User                  `protobuf:"bytes,10,opt,name=from_user,json=fromUser,proto3" json:"from_user,omitempty"`
	Success      bool                   `protobuf:"varint,11,opt,name=success,proto3" json:"success,omitempty"`
	ErrorMessage string                 `protobuf:"bytes,12,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	RoomInfo     *RoomInfo              `protobuf:"bytes,13,opt,name=room_info,json=roomInfo,proto3" json:"room_info,omitempty"`
	OnlineUsers  []*User                `protobuf:"bytes,14,rep,name=online_users,json=onlineUsers,proto3" json:"online_users,omitempty"`
	Metadata     map[string]string      `protobuf:"bytes,15,rep,name=metadata,proto3" json:"metadata,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

// Stub methods to satisfy protobuf interface
func (x *User) Reset()                             {}
func (x *User) String() string                     { return "" }
func (x *User) ProtoMessage()                      {}
func (x *User) ProtoReflect() protoreflect.Message { return nil }

func (x *RoomInfo) Reset()                             {}
func (x *RoomInfo) String() string                     { return "" }
func (x *RoomInfo) ProtoMessage()                      {}
func (x *RoomInfo) ProtoReflect() protoreflect.Message { return nil }

func (x *Message) Reset()                             {}
func (x *Message) String() string                     { return "" }
func (x *Message) ProtoMessage()                      {}
func (x *Message) ProtoReflect() protoreflect.Message { return nil }

// Getter methods
func (x *User) GetUserId() string   { return x.UserId }
func (x *User) GetUsername() string { return x.Username }
func (x *User) GetAvatar() string   { return x.Avatar }
func (x *User) GetRole() UserRole   { return x.Role }
func (x *User) GetLevel() int32     { return x.Level }
func (x *User) GetVipLevel() int32  { return x.VipLevel }
func (x *User) GetBadges() []string { return x.Badges }

func (x *RoomInfo) GetRoomId() string                    { return x.RoomId }
func (x *RoomInfo) GetTitle() string                     { return x.Title }
func (x *RoomInfo) GetStatus() string                    { return x.Status }
func (x *RoomInfo) GetOnlineCount() int32                { return x.OnlineCount }
func (x *RoomInfo) GetStartTime() *timestamppb.Timestamp { return x.StartTime }
func (x *RoomInfo) GetStreamerId() string                { return x.StreamerId }
func (x *RoomInfo) GetStreamerName() string              { return x.StreamerName }
func (x *RoomInfo) GetStreamerAvatar() string            { return x.StreamerAvatar }

func (x *Message) GetMessageId() string                 { return x.MessageId }
func (x *Message) GetType() MessageType                 { return x.Type }
func (x *Message) GetTimestamp() *timestamppb.Timestamp { return x.Timestamp }
func (x *Message) GetToken() string                     { return x.Token }
func (x *Message) GetRoomId() string                    { return x.RoomId }
func (x *Message) GetContent() string                   { return x.Content }
func (x *Message) GetCount() int32                      { return x.Count }
func (x *Message) GetGiftId() string                    { return x.GiftId }
func (x *Message) GetToUserId() string                  { return x.ToUserId }
func (x *Message) GetFromUser() *User                   { return x.FromUser }
func (x *Message) GetSuccess() bool                     { return x.Success }
func (x *Message) GetErrorMessage() string              { return x.ErrorMessage }
func (x *Message) GetRoomInfo() *RoomInfo               { return x.RoomInfo }
func (x *Message) GetOnlineUsers() []*User              { return x.OnlineUsers }
func (x *Message) GetMetadata() map[string]string       { return x.Metadata }
