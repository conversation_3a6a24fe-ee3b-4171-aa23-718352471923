/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package ratelimit

import (
	"context"
	"testing"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// MockMetricsCollector implements MetricsCollector for testing
type MockMetricsCollector struct{}

func (m *MockMetricsCollector) RecordRequest(action Action, tier Tier, allowed bool)                    {}
func (m *MockMetricsCollector) RecordBlock(action Action, tier Tier, duration time.Duration)           {}
func (m *MockMetricsCollector) RecordUnblock(action Action, tier Tier)                                 {}
func (m *MockMetricsCollector) UpdateCurrentLimits(action Action, tier Tier, limit int, usage int)    {}
func (m *MockMetricsCollector) RecordLatency(action Action, tier Tier, duration time.Duration)        {}

func createTestRedisClient() *redis.Client {
	return redis.NewClient(&redis.Options{
		Addr: "localhost:6379",
		DB:   1, // Use different DB for tests
	})
}

func TestTokenBucketLimiter_Allow(t *testing.T) {
	client := createTestRedisClient()
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	metrics := &MockMetricsCollector{}

	config := Config{
		Enabled:     true,
		Algorithm:   AlgorithmTokenBucket,
		Limit:       10,
		Burst:       10,
		Window:      time.Minute,
		RefillRate:  time.Second,
	}

	limiter := NewTokenBucketLimiter(
		client,
		config,
		ActionAPI,
		TierBasic,
		"test",
		logger,
		metrics,
	)

	ctx := context.Background()
	userID := uuid.New()

	t.Run("Allow single request", func(t *testing.T) {
		// Clean up first
		limiter.Reset(ctx, userID, ActionAPI)

		result, err := limiter.Allow(ctx, userID, ActionAPI)
		require.NoError(t, err)
		assert.True(t, result.Allowed)
		assert.Equal(t, 10, result.Limit)
		assert.Equal(t, 9, result.Remaining)
	})

	t.Run("Allow multiple requests within limit", func(t *testing.T) {
		// Clean up first
		limiter.Reset(ctx, userID, ActionAPI)

		for i := 0; i < 5; i++ {
			result, err := limiter.Allow(ctx, userID, ActionAPI)
			require.NoError(t, err)
			assert.True(t, result.Allowed)
			assert.Equal(t, 10, result.Limit)
			assert.Equal(t, 10-i-1, result.Remaining)
		}
	})

	t.Run("Deny request when limit exceeded", func(t *testing.T) {
		// Clean up first
		limiter.Reset(ctx, userID, ActionAPI)

		// Use up all tokens
		for i := 0; i < 10; i++ {
			result, err := limiter.Allow(ctx, userID, ActionAPI)
			require.NoError(t, err)
			assert.True(t, result.Allowed)
		}

		// Next request should be denied
		result, err := limiter.Allow(ctx, userID, ActionAPI)
		require.NoError(t, err)
		assert.False(t, result.Allowed)
		assert.Equal(t, 0, result.Remaining)
	})

	t.Run("Disabled rate limiting allows all requests", func(t *testing.T) {
		disabledConfig := config
		disabledConfig.Enabled = false

		disabledLimiter := NewTokenBucketLimiter(
			client,
			disabledConfig,
			ActionAPI,
			TierBasic,
			"test",
			logger,
			metrics,
		)

		result, err := disabledLimiter.Allow(ctx, userID, ActionAPI)
		require.NoError(t, err)
		assert.True(t, result.Allowed)
	})
}

func TestTokenBucketLimiter_AllowN(t *testing.T) {
	client := createTestRedisClient()
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	metrics := &MockMetricsCollector{}

	config := Config{
		Enabled:     true,
		Algorithm:   AlgorithmTokenBucket,
		Limit:       10,
		Burst:       10,
		Window:      time.Minute,
		RefillRate:  time.Second,
	}

	limiter := NewTokenBucketLimiter(
		client,
		config,
		ActionAPI,
		TierBasic,
		"test",
		logger,
		metrics,
	)

	ctx := context.Background()
	userID := uuid.New()

	t.Run("Allow N requests within limit", func(t *testing.T) {
		// Clean up first
		limiter.Reset(ctx, userID, ActionAPI)

		result, err := limiter.AllowN(ctx, userID, ActionAPI, 5)
		require.NoError(t, err)
		assert.True(t, result.Allowed)
		assert.Equal(t, 10, result.Limit)
		assert.Equal(t, 5, result.Remaining)
	})

	t.Run("Deny N requests when exceeding limit", func(t *testing.T) {
		// Clean up first
		limiter.Reset(ctx, userID, ActionAPI)

		// Use up 8 tokens
		result, err := limiter.AllowN(ctx, userID, ActionAPI, 8)
		require.NoError(t, err)
		assert.True(t, result.Allowed)
		assert.Equal(t, 2, result.Remaining)

		// Try to use 5 more tokens (should fail)
		result, err = limiter.AllowN(ctx, userID, ActionAPI, 5)
		require.NoError(t, err)
		assert.False(t, result.Allowed)
		assert.Equal(t, 2, result.Remaining) // Should remain unchanged
	})
}

func TestTokenBucketLimiter_Block(t *testing.T) {
	client := createTestRedisClient()
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	metrics := &MockMetricsCollector{}

	config := Config{
		Enabled:       true,
		Algorithm:     AlgorithmTokenBucket,
		Limit:         10,
		Burst:         10,
		Window:        time.Minute,
		RefillRate:    time.Second,
		BlockDuration: time.Minute,
	}

	limiter := NewTokenBucketLimiter(
		client,
		config,
		ActionAPI,
		TierBasic,
		"test",
		logger,
		metrics,
	)

	ctx := context.Background()
	userID := uuid.New()

	t.Run("Block user manually", func(t *testing.T) {
		// Clean up first
		limiter.Reset(ctx, userID, ActionAPI)

		// Block user
		err := limiter.Block(ctx, userID, ActionAPI, time.Minute)
		require.NoError(t, err)

		// Check if blocked
		blocked, expiry, err := limiter.IsBlocked(ctx, userID, ActionAPI)
		require.NoError(t, err)
		assert.True(t, blocked)
		assert.True(t, expiry.After(time.Now()))

		// Request should be denied due to block
		result, err := limiter.Allow(ctx, userID, ActionAPI)
		require.NoError(t, err)
		assert.False(t, result.Allowed)
		assert.True(t, result.Blocked)
	})

	t.Run("Unblock user manually", func(t *testing.T) {
		// Block user first
		err := limiter.Block(ctx, userID, ActionAPI, time.Minute)
		require.NoError(t, err)

		// Unblock user
		err = limiter.Unblock(ctx, userID, ActionAPI)
		require.NoError(t, err)

		// Check if unblocked
		blocked, _, err := limiter.IsBlocked(ctx, userID, ActionAPI)
		require.NoError(t, err)
		assert.False(t, blocked)

		// Request should be allowed now
		result, err := limiter.Allow(ctx, userID, ActionAPI)
		require.NoError(t, err)
		assert.True(t, result.Allowed)
		assert.False(t, result.Blocked)
	})
}

func TestTokenBucketLimiter_GetStats(t *testing.T) {
	client := createTestRedisClient()
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	metrics := &MockMetricsCollector{}

	config := Config{
		Enabled:     true,
		Algorithm:   AlgorithmTokenBucket,
		Limit:       10,
		Burst:       10,
		Window:      time.Minute,
		RefillRate:  time.Second,
	}

	limiter := NewTokenBucketLimiter(
		client,
		config,
		ActionAPI,
		TierBasic,
		"test",
		logger,
		metrics,
	)

	ctx := context.Background()
	userID := uuid.New()

	t.Run("Get stats after requests", func(t *testing.T) {
		// Clean up first
		limiter.Reset(ctx, userID, ActionAPI)

		// Make some requests
		limiter.AllowN(ctx, userID, ActionAPI, 3)

		stats, err := limiter.GetStats(ctx, userID, ActionAPI)
		require.NoError(t, err)
		assert.Equal(t, ActionAPI, stats.Action)
		assert.Equal(t, userID, stats.UserID)
		assert.Equal(t, 10, stats.Limit)
		assert.Equal(t, 3, stats.Usage)
		assert.Equal(t, 7, stats.Remaining)
		assert.False(t, stats.IsBlocked)
	})

	t.Run("Get stats when blocked", func(t *testing.T) {
		// Block user
		limiter.Block(ctx, userID, ActionAPI, time.Minute)

		stats, err := limiter.GetStats(ctx, userID, ActionAPI)
		require.NoError(t, err)
		assert.True(t, stats.IsBlocked)
		assert.True(t, stats.BlockExpiry.After(time.Now()))
	})
}

func TestTokenBucketLimiter_Reset(t *testing.T) {
	client := createTestRedisClient()
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	metrics := &MockMetricsCollector{}

	config := Config{
		Enabled:     true,
		Algorithm:   AlgorithmTokenBucket,
		Limit:       10,
		Burst:       10,
		Window:      time.Minute,
		RefillRate:  time.Second,
	}

	limiter := NewTokenBucketLimiter(
		client,
		config,
		ActionAPI,
		TierBasic,
		"test",
		logger,
		metrics,
	)

	ctx := context.Background()
	userID := uuid.New()

	t.Run("Reset after using tokens", func(t *testing.T) {
		// Use some tokens
		limiter.AllowN(ctx, userID, ActionAPI, 8)

		// Check remaining
		stats, err := limiter.GetStats(ctx, userID, ActionAPI)
		require.NoError(t, err)
		assert.Equal(t, 2, stats.Remaining)

		// Reset
		err = limiter.Reset(ctx, userID, ActionAPI)
		require.NoError(t, err)

		// Check that tokens are restored
		result, err := limiter.Allow(ctx, userID, ActionAPI)
		require.NoError(t, err)
		assert.True(t, result.Allowed)
		assert.Equal(t, 9, result.Remaining) // Should have full capacity minus 1
	})

	t.Run("Reset when blocked", func(t *testing.T) {
		// Block user
		limiter.Block(ctx, userID, ActionAPI, time.Minute)

		// Verify blocked
		blocked, _, err := limiter.IsBlocked(ctx, userID, ActionAPI)
		require.NoError(t, err)
		assert.True(t, blocked)

		// Reset
		err = limiter.Reset(ctx, userID, ActionAPI)
		require.NoError(t, err)

		// Verify unblocked
		blocked, _, err = limiter.IsBlocked(ctx, userID, ActionAPI)
		require.NoError(t, err)
		assert.False(t, blocked)
	})
}

func BenchmarkTokenBucketLimiter_Allow(b *testing.B) {
	client := createTestRedisClient()
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)
	metrics := &MockMetricsCollector{}

	config := Config{
		Enabled:     true,
		Algorithm:   AlgorithmTokenBucket,
		Limit:       1000,
		Burst:       1000,
		Window:      time.Minute,
		RefillRate:  time.Millisecond,
	}

	limiter := NewTokenBucketLimiter(
		client,
		config,
		ActionAPI,
		TierBasic,
		"bench",
		logger,
		metrics,
	)

	ctx := context.Background()
	userID := uuid.New()

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			limiter.Allow(ctx, userID, ActionAPI)
		}
	})
}
