/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:12:13
Modified: 2025-07-11 10:12:13
*/

package redis

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"

	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"

	"cina.club/services/live-im-service/internal/application/port"
	"cina.club/services/live-im-service/internal/domain/model"
)

const (
	// Redis channel pattern for live IM rooms
	LiveIMChannelPattern = "live_im_room:*"
	LiveIMChannelPrefix  = "live_im_room:"
)

// Broadcaster implements the port.Broadcaster interface using Redis Pub/Sub.
type Broadcaster struct {
	client  *redis.Client
	pubsub  *redis.PubSub
	logger  *logrus.Logger
	handler port.BroadcastHandler
	
	// Control
	ctx    context.Context
	cancel context.CancelFunc
	wg     sync.WaitGroup
	mu     sync.RWMutex
	
	// State
	subscribed bool
}

// NewBroadcaster creates a new Redis broadcaster.
func NewBroadcaster(client *redis.Client, logger *logrus.Logger) *Broadcaster {
	return &Broadcaster{
		client: client,
		logger: logger.WithField("component", "redis_broadcaster"),
	}
}

// Broadcast sends a message to all subscribers of a room.
func (b *Broadcaster) Broadcast(ctx context.Context, roomID string, message model.Message) error {
	// Serialize message to JSON
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	// Publish to Redis channel
	channel := LiveIMChannelPrefix + roomID
	if err := b.client.Publish(ctx, channel, data).Err(); err != nil {
		return fmt.Errorf("failed to publish message to channel %s: %w", channel, err)
	}

	b.logger.WithFields(logrus.Fields{
		"room_id":      roomID,
		"message_type": message.Type,
		"message_id":   message.MessageID,
		"channel":      channel,
	}).Debug("Message broadcasted")

	return nil
}

// Subscribe starts listening for broadcast messages.
func (b *Broadcaster) Subscribe(ctx context.Context, handler port.BroadcastHandler) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	if b.subscribed {
		return fmt.Errorf("already subscribed")
	}

	b.ctx, b.cancel = context.WithCancel(ctx)
	b.handler = handler

	// Subscribe to pattern
	b.pubsub = b.client.PSubscribe(b.ctx, LiveIMChannelPattern)

	// Start message processing goroutine
	b.wg.Add(1)
	go b.processMessages()

	b.subscribed = true
	b.logger.WithField("pattern", LiveIMChannelPattern).Info("Subscribed to Redis Pub/Sub")

	return nil
}

// Unsubscribe stops listening for broadcast messages.
func (b *Broadcaster) Unsubscribe(ctx context.Context) error {
	b.mu.Lock()
	defer b.mu.Unlock()

	if !b.subscribed {
		return nil
	}

	// Cancel context
	if b.cancel != nil {
		b.cancel()
	}

	// Close pubsub
	if b.pubsub != nil {
		if err := b.pubsub.Close(); err != nil {
			b.logger.WithError(err).Error("Failed to close pubsub")
		}
	}

	// Wait for goroutines to finish
	b.wg.Wait()

	b.subscribed = false
	b.logger.Info("Unsubscribed from Redis Pub/Sub")

	return nil
}

// Close closes the broadcaster.
func (b *Broadcaster) Close() error {
	return b.Unsubscribe(context.Background())
}

// processMessages processes incoming messages from Redis Pub/Sub.
func (b *Broadcaster) processMessages() {
	defer b.wg.Done()

	ch := b.pubsub.Channel()

	for {
		select {
		case <-b.ctx.Done():
			b.logger.Info("Message processing stopped")
			return

		case msg, ok := <-ch:
			if !ok {
				b.logger.Warn("Redis Pub/Sub channel closed")
				return
			}

			if err := b.handleMessage(msg); err != nil {
				b.logger.WithError(err).Error("Failed to handle message")
			}
		}
	}
}

// handleMessage handles a single message from Redis Pub/Sub.
func (b *Broadcaster) handleMessage(msg *redis.Message) error {
	// Extract room ID from channel name
	roomID := extractRoomIDFromChannel(msg.Channel)
	if roomID == "" {
		return fmt.Errorf("invalid channel name: %s", msg.Channel)
	}

	// Deserialize message
	var message model.Message
	if err := json.Unmarshal([]byte(msg.Payload), &message); err != nil {
		return fmt.Errorf("failed to unmarshal message: %w", err)
	}

	// Call handler
	b.mu.RLock()
	handler := b.handler
	b.mu.RUnlock()

	if handler != nil {
		if err := handler(roomID, message); err != nil {
			return fmt.Errorf("handler failed: %w", err)
		}
	}

	b.logger.WithFields(logrus.Fields{
		"room_id":      roomID,
		"message_type": message.Type,
		"message_id":   message.MessageID,
		"channel":      msg.Channel,
	}).Debug("Message processed")

	return nil
}

// extractRoomIDFromChannel extracts room ID from channel name.
func extractRoomIDFromChannel(channel string) string {
	if len(channel) <= len(LiveIMChannelPrefix) {
		return ""
	}
	return channel[len(LiveIMChannelPrefix):]
}
