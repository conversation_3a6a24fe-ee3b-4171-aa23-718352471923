/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00

This file demonstrates how to integrate health checks and monitoring into the Live IM Service.
*/

package main

import (
	"context"
	"log"
	"net/http"
	"runtime"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"

	"cina.club/services/live-im-service/internal/application/service"
	"cina.club/services/live-im-service/pkg/circuitbreaker"
	"cina.club/services/live-im-service/pkg/health"
	"cina.club/services/live-im-service/pkg/metrics"
)

// Application represents the main application with health checks and monitoring
type Application struct {
	healthService   *health.Service
	metricsManager  *metrics.MetricsManager
	cbManager       *circuitbreaker.Manager
	hubService      *service.Hub
	redisClient     *redis.Client
	startTime       time.Time
	logger          *logrus.Logger
}

// NewApplication creates a new application with health checks and monitoring
func NewApplication() *Application {
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	logger.SetFormatter(&logrus.JSONFormatter{})

	startTime := time.Now()

	// Create health service
	healthConfig := health.Config{
		Version:     "1.0.0",
		Environment: "development",
		Timeout:     30 * time.Second,
	}
	healthService := health.NewService(healthConfig, logger)

	// Create metrics manager
	metricsManager := metrics.NewMetricsManager(logger, startTime)

	// Create circuit breaker manager
	cbConfig := circuitbreaker.ManagerConfig{
		DefaultConfig: circuitbreaker.Config{
			MaxRequests: 10,
			Interval:    60 * time.Second,
			Timeout:     30 * time.Second,
		},
	}
	cbManager := circuitbreaker.NewManager(cbConfig, logger)

	// Create Redis client
	redisClient := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	})

	return &Application{
		healthService:  healthService,
		metricsManager: metricsManager,
		cbManager:      cbManager,
		redisClient:    redisClient,
		startTime:      startTime,
		logger:         logger,
	}
}

// setupHealthCheckers registers all health checkers
func (app *Application) setupHealthCheckers() {
	// Redis health checker (critical)
	redisChecker := health.NewRedisHealthChecker(
		app.redisClient,
		5*time.Second,
		true, // critical
		app.logger,
	)
	app.healthService.RegisterChecker(redisChecker)

	// Hub health checker (critical) - would be set up when hub is available
	if app.hubService != nil {
		hubChecker := health.NewHubHealthChecker(
			app.hubService,
			3*time.Second,
			true, // critical
			app.logger,
		)
		app.healthService.RegisterChecker(hubChecker)
	}

	// Application health checker (non-critical)
	appChecker := health.NewApplicationHealthChecker(
		app.startTime,
		"1.0.0",
		1*time.Second,
		false, // not critical
		app.logger,
	)
	app.healthService.RegisterChecker(appChecker)

	// External service health checkers
	liveAPIChecker := health.NewHTTPServiceHealthChecker(
		"live_api",
		"https://httpbin.org/status/200", // Mock endpoint for demo
		10*time.Second,
		false, // not critical for demo
		app.logger,
	)
	app.healthService.RegisterChecker(liveAPIChecker)

	// Database health checker (if database is used)
	dbChecker := health.NewDatabaseHealthChecker(
		"database",
		func(ctx context.Context) error {
			// Mock database ping
			return nil
		},
		5*time.Second,
		true, // critical
		app.logger,
	)
	app.healthService.RegisterChecker(dbChecker)

	app.logger.Info("Health checkers registered successfully")
}

// setupRoutes sets up HTTP routes with health checks and monitoring
func (app *Application) setupRoutes() *gin.Engine {
	router := gin.New()

	// Add metrics middleware
	router.Use(app.metricsManager.GetHTTPMiddleware().Middleware())

	// Add health check middleware
	healthHandler := health.NewHTTPHandler(app.healthService, app.logger)
	router.Use(healthHandler.Middleware())

	// Add circuit breaker middleware
	cbHandler := circuitbreaker.NewHTTPHandler(app.cbManager, app.logger)
	router.Use(cbHandler.Middleware())

	// Recovery middleware
	router.Use(gin.Recovery())

	// API routes
	api := router.Group("/api/v1")
	{
		// Health check endpoints
		healthHandler.RegisterRoutes(api)

		// Circuit breaker endpoints
		cbHandler.RegisterRoutes(api)

		// Metrics endpoint
		api.GET("/metrics", app.metricsManager.GetHTTPMiddleware().PrometheusHandler())

		// Health dashboard
		api.GET("/dashboard", healthHandler.HealthDashboard)

		// Example business endpoints
		app.setupBusinessRoutes(api)
	}

	return router
}

// setupBusinessRoutes sets up example business endpoints with monitoring
func (app *Application) setupBusinessRoutes(api *gin.RouterGroup) {
	// Example endpoint that uses Redis with circuit breaker protection
	api.GET("/data/:key", func(c *gin.Context) {
		key := c.Param("key")
		
		// Use circuit breaker for Redis operations
		var value string
		err := app.cbManager.ExecuteWithContext(c.Request.Context(), "redis", func(ctx context.Context) error {
			result := app.redisClient.Get(ctx, key)
			if result.Err() != nil {
				return result.Err()
			}
			value = result.Val()
			return nil
		})

		if err != nil {
			if err == circuitbreaker.ErrOpenState {
				c.JSON(http.StatusServiceUnavailable, gin.H{
					"error": "Redis service is currently unavailable",
					"key":   key,
				})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": err.Error(),
				"key":   key,
			})
			return
		}

		c.JSON(http.StatusOK, gin.H{
			"key":   key,
			"value": value,
		})
	})

	// Example endpoint that sets data
	api.POST("/data/:key", func(c *gin.Context) {
		key := c.Param("key")
		var request struct {
			Value string `json:"value"`
		}

		if err := c.ShouldBindJSON(&request); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		err := app.cbManager.ExecuteWithContext(c.Request.Context(), "redis", func(ctx context.Context) error {
			return app.redisClient.Set(ctx, key, request.Value, time.Hour).Err()
		})

		if err != nil {
			if err == circuitbreaker.ErrOpenState {
				c.JSON(http.StatusServiceUnavailable, gin.H{
					"error": "Redis service is currently unavailable",
				})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": err.Error(),
			})
			return
		}

		c.JSON(http.StatusCreated, gin.H{
			"key":   key,
			"value": request.Value,
		})
	})

	// Example endpoint that simulates load for testing
	api.POST("/simulate/load", func(c *gin.Context) {
		var request struct {
			Duration string `json:"duration"`
			Type     string `json:"type"`
		}

		if err := c.ShouldBindJSON(&request); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		duration, err := time.ParseDuration(request.Duration)
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid duration format"})
			return
		}

		switch request.Type {
		case "cpu":
			// Simulate CPU load
			go func() {
				end := time.Now().Add(duration)
				for time.Now().Before(end) {
					// CPU intensive operation
					for i := 0; i < 1000000; i++ {
						_ = i * i
					}
				}
			}()
		case "memory":
			// Simulate memory allocation
			go func() {
				data := make([][]byte, 0)
				for i := 0; i < 100; i++ {
					data = append(data, make([]byte, 1024*1024)) // 1MB
					time.Sleep(duration / 100)
				}
			}()
		case "redis":
			// Simulate Redis load
			go func() {
				end := time.Now().Add(duration)
				for time.Now().Before(end) {
					app.redisClient.Set(context.Background(), "load-test", "value", time.Minute)
					time.Sleep(10 * time.Millisecond)
				}
			}()
		}

		c.JSON(http.StatusOK, gin.H{
			"message":  "Load simulation started",
			"duration": request.Duration,
			"type":     request.Type,
		})
	})
}

// startSystemMetricsCollection starts collecting system metrics
func (app *Application) startSystemMetricsCollection() {
	ticker := time.NewTicker(10 * time.Second)
	go func() {
		for range ticker.C {
			// Update system metrics
			systemMetrics := app.metricsManager.GetSystemMetrics()
			systemMetrics.UpdateUptime()
			systemMetrics.UpdateGoroutines(runtime.NumGoroutine())

			// Get memory stats
			var memStats runtime.MemStats
			runtime.ReadMemStats(&memStats)
			systemMetrics.UpdateMemoryUsage(int64(memStats.Alloc))

			// Update hub metrics if available
			if app.hubService != nil {
				stats := app.hubService.GetStats()
				if stats != nil {
					hubMetrics := app.metricsManager.GetHubMetrics()
					hubMetrics.UpdateStats(
						stats.ConnectedClients,
						stats.ActiveRooms,
						stats.MessagesPerSecond,
						stats.BroadcastsPerSecond,
					)
				}
			}
		}
	}()

	app.logger.Info("System metrics collection started")
}

// Run starts the application
func (app *Application) Run() error {
	// Setup health checkers
	app.setupHealthCheckers()

	// Start system metrics collection
	app.startSystemMetricsCollection()

	// Setup routes
	router := app.setupRoutes()

	// Log startup information
	app.logger.WithFields(logrus.Fields{
		"version":     "1.0.0",
		"environment": "development",
		"start_time":  app.startTime.Format(time.RFC3339),
	}).Info("Live IM Service starting up")

	// Print available endpoints
	app.logger.Info("Available endpoints:")
	app.logger.Info("  Health Check: http://localhost:8080/api/v1/health")
	app.logger.Info("  Liveness: http://localhost:8080/api/v1/health/live")
	app.logger.Info("  Readiness: http://localhost:8080/api/v1/health/ready")
	app.logger.Info("  Metrics: http://localhost:8080/api/v1/metrics")
	app.logger.Info("  Dashboard: http://localhost:8080/api/v1/dashboard")
	app.logger.Info("  Circuit Breakers: http://localhost:8080/api/v1/circuit-breakers")

	// Start HTTP server
	server := &http.Server{
		Addr:    ":8080",
		Handler: router,
	}

	app.logger.Info("Server starting on :8080")
	return server.ListenAndServe()
}

// Shutdown gracefully shuts down the application
func (app *Application) Shutdown(ctx context.Context) error {
	app.logger.Info("Shutting down application")

	// Close Redis client
	if err := app.redisClient.Close(); err != nil {
		app.logger.WithError(err).Error("Failed to close Redis client")
	}

	// Close circuit breaker manager
	if err := app.cbManager.Close(); err != nil {
		app.logger.WithError(err).Error("Failed to close circuit breaker manager")
	}

	app.logger.Info("Application shutdown complete")
	return nil
}

func main() {
	app := NewApplication()

	// Handle graceful shutdown
	go func() {
		if err := app.Run(); err != nil && err != http.ErrServerClosed {
			log.Fatal("Failed to start server:", err)
		}
	}()

	// Wait for interrupt signal
	// In a real application, you would set up signal handling here
	select {}
}
