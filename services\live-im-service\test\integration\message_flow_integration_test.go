/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package integration

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"

	"cina.club/services/live-im-service/internal/domain/model"
)

// TestMessageFlowIntegration tests end-to-end message flow
func TestMessageFlowIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	logger := logrus.New()
	logger.SetLevel(logrus.WarnLevel)

	t.Run("Message Creation and Validation", func(t *testing.T) {
		testMessageCreationAndValidation(t)
	})

	t.Run("User and Room Interaction", func(t *testing.T) {
		testUserAndRoomInteraction(t)
	})

	t.Run("Message Processing Flow", func(t *testing.T) {
		testMessageProcessingFlow(t)
	})

	t.Run("Multi-User Room Scenario", func(t *testing.T) {
		testMultiUserRoomScenario(t)
	})
}

func testMessageCreationAndValidation(t *testing.T) {
	// Test creating different types of messages
	userID := uuid.New()
	roomID := "test-room-" + uuid.New().String()

	user := &model.User{
		UserID:   userID,
		Username: "testuser",
		Avatar:   "avatar.jpg",
		Role:     model.RoleViewer,
		Level:    5,
		VIPLevel: 2,
		Badges:   []string{"newbie"},
	}

	// Test barrage message
	barrageMsg := model.NewBarrageMessage("Hello World!", roomID, user)
	assert.Equal(t, model.MessageTypeBarrage, barrageMsg.Type)
	assert.Equal(t, roomID, barrageMsg.RoomID)
	assert.Equal(t, "Hello World!", barrageMsg.Content)
	assert.Equal(t, user, barrageMsg.FromUser)
	assert.NotEmpty(t, barrageMsg.MessageID)
	assert.False(t, barrageMsg.Timestamp.IsZero())

	// Test like message
	likeMsg := model.NewLikeMessage(5, roomID, user)
	assert.Equal(t, model.MessageTypeLike, likeMsg.Type)
	assert.Equal(t, roomID, likeMsg.RoomID)
	assert.Equal(t, 5, likeMsg.Count)
	assert.Equal(t, user, likeMsg.FromUser)

	// Test gift message - need to create a recipient user
	recipientUser := &model.User{
		UserID:   uuid.New(),
		Username: "recipient",
		Role:     model.RoleViewer,
	}
	giftMsg := model.NewGiftMessage("rose", 3, roomID, user, recipientUser)
	assert.Equal(t, model.MessageTypeGift, giftMsg.Type)
	assert.Equal(t, roomID, giftMsg.RoomID)
	assert.Equal(t, "rose", giftMsg.GiftID)
	assert.Equal(t, 3, giftMsg.Count)
	assert.Equal(t, user, giftMsg.FromUser)

	// Test error message
	errorMsg := model.NewErrorMessage("Test error")
	assert.Equal(t, model.MessageTypeError, errorMsg.Type)
	assert.Equal(t, "Test error", errorMsg.ErrorMsg)
	assert.False(t, errorMsg.Success)
}

func testUserAndRoomInteraction(t *testing.T) {
	// Test user creation and validation
	userID := uuid.New()
	user := &model.User{
		UserID:   userID,
		Username: "testuser",
		Avatar:   "avatar.jpg",
		Role:     model.RoleViewer,
		Level:    1,
		VIPLevel: 0,
		Badges:   []string{},
	}

	// Test user methods
	assert.False(t, user.HasBadge("vip"))
	user.AddBadge("vip")
	assert.True(t, user.HasBadge("vip"))
	assert.Len(t, user.Badges, 1)

	// Test adding duplicate badge
	user.AddBadge("vip")
	assert.Len(t, user.Badges, 1) // Should not duplicate

	// Test user role validation
	assert.Equal(t, model.RoleViewer, user.Role)
	assert.NotEqual(t, model.RoleModerator, user.Role)
	assert.NotEqual(t, model.RoleAdmin, user.Role)

	// Test room creation
	roomID := "test-room-" + uuid.New().String()
	streamerID := uuid.New()
	streamer := &model.User{
		UserID:   streamerID,
		Username: "streamer",
		Role:     model.RoleStreamer,
	}
	room := model.NewRoom(roomID, "Test Room", streamerID, streamer)
	assert.Equal(t, roomID, room.ID)
	assert.Equal(t, "Test Room", room.Title)
	assert.Equal(t, model.RoomStatusIdle, room.Status) // Default status is idle
	assert.Equal(t, 0, room.OnlineCount)

	// Test client creation would require WebSocket connection and HTTP request
	// For integration testing, we'll test the basic client structure
	assert.Equal(t, userID, user.UserID)
	assert.Equal(t, "testuser", user.Username)
	assert.Equal(t, model.RoleViewer, user.Role)
}

func testMessageProcessingFlow(t *testing.T) {
	// Test message validation and processing
	userID := uuid.New()
	roomID := "test-room-" + uuid.New().String()

	user := &model.User{
		UserID:   userID,
		Username: "testuser",
		Role:     model.RoleViewer,
	}

	// Test message validation
	validMsg := model.NewBarrageMessage("Valid message", roomID, user)
	assert.NotEmpty(t, validMsg.MessageID)
	assert.Equal(t, model.MessageTypeBarrage, validMsg.Type)
	assert.Equal(t, "Valid message", validMsg.Content)

	// Test message cloning
	clonedMsg := validMsg.Clone()
	assert.Equal(t, validMsg.MessageID, clonedMsg.MessageID)
	assert.Equal(t, validMsg.Type, clonedMsg.Type)
	assert.Equal(t, validMsg.Content, clonedMsg.Content)
	assert.Equal(t, validMsg.RoomID, clonedMsg.RoomID)

	// Verify it's a deep copy
	clonedMsg.Content = "Modified content"
	assert.NotEqual(t, validMsg.Content, clonedMsg.Content)
}

func testMultiUserRoomScenario(t *testing.T) {
	// Simulate a multi-user room scenario
	roomID := "test-room-" + uuid.New().String()

	// Create multiple users
	users := make([]*model.User, 3)
	for i := 0; i < 3; i++ {
		users[i] = &model.User{
			UserID:   uuid.New(),
			Username: "user" + string(rune('1'+i)),
			Role:     model.RoleViewer,
			Level:    i + 1,
		}
	}

	// Create room with first user as streamer
	streamerID := users[0].UserID
	room := model.NewRoom(roomID, "Multi-User Test Room", streamerID, users[0])

	// Test message exchange
	messages := []model.Message{
		model.NewBarrageMessage("Hello everyone!", roomID, users[0]),
		model.NewLikeMessage(1, roomID, users[1]),
		model.NewBarrageMessage("Nice to meet you!", roomID, users[2]),
		model.NewLikeMessage(5, roomID, users[0]),
	}

	// Verify all messages are valid
	for i, msg := range messages {
		assert.NotEmpty(t, msg.MessageID, "Message %d should have ID", i)
		assert.Equal(t, roomID, msg.RoomID)
		assert.NotNil(t, msg.FromUser)
		assert.False(t, msg.Timestamp.IsZero())
	}

	// Test room basic properties
	assert.Equal(t, roomID, room.ID)
	assert.Equal(t, "Multi-User Test Room", room.Title)
	assert.Equal(t, streamerID, room.StreamerID)
	assert.Equal(t, model.RoomStatusIdle, room.Status) // Default status

	// Test user properties
	for i, user := range users {
		assert.NotEqual(t, uuid.Nil, user.UserID)
		assert.Equal(t, "user"+string(rune('1'+i)), user.Username)
		assert.Equal(t, model.RoleViewer, user.Role)
		assert.Equal(t, i+1, user.Level)
	}
}

// TestConcurrentMessageProcessing tests concurrent message handling
func TestConcurrentMessageProcessing(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	const numGoroutines = 10
	const messagesPerGoroutine = 100

	roomID := "test-room-concurrent"
	user := &model.User{
		UserID:   uuid.New(),
		Username: "concurrent-user",
		Role:     model.RoleViewer,
	}

	// Channel to collect all messages
	messagesChan := make(chan model.Message, numGoroutines*messagesPerGoroutine)

	// Start multiple goroutines creating messages
	for i := 0; i < numGoroutines; i++ {
		go func(goroutineID int) {
			for j := 0; j < messagesPerGoroutine; j++ {
				msg := model.NewBarrageMessage(
					"Message from goroutine "+string(rune('0'+goroutineID)),
					roomID,
					user,
				)
				messagesChan <- msg
			}
		}(i)
	}

	// Collect all messages
	var messages []model.Message
	timeout := time.After(5 * time.Second)
	expectedCount := numGoroutines * messagesPerGoroutine

	for len(messages) < expectedCount {
		select {
		case msg := <-messagesChan:
			messages = append(messages, msg)
		case <-timeout:
			t.Fatalf("Timeout waiting for messages. Got %d, expected %d", len(messages), expectedCount)
		}
	}

	// Verify all messages are valid and unique
	messageIDs := make(map[string]bool)
	for _, msg := range messages {
		assert.NotEmpty(t, msg.MessageID)
		assert.Equal(t, roomID, msg.RoomID)
		assert.Equal(t, user, msg.FromUser)
		assert.False(t, msg.Timestamp.IsZero())

		// Check for unique message IDs
		assert.False(t, messageIDs[msg.MessageID], "Duplicate message ID: %s", msg.MessageID)
		messageIDs[msg.MessageID] = true
	}

	assert.Equal(t, expectedCount, len(messages))
	assert.Equal(t, expectedCount, len(messageIDs))
}
