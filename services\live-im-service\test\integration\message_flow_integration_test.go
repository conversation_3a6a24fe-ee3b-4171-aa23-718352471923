/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package integration

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"cina.club/services/live-im-service/internal/domain/model"
)

// TestMessageFlowIntegration tests end-to-end message flow
func TestMessageFlowIntegration(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	logger := logrus.New()
	logger.SetLevel(logrus.WarnLevel)

	t.Run("Message Creation and Validation", func(t *testing.T) {
		testMessageCreationAndValidation(t)
	})

	t.Run("User and Room Interaction", func(t *testing.T) {
		testUserAndRoomInteraction(t)
	})

	t.Run("Message Processing Flow", func(t *testing.T) {
		testMessageProcessingFlow(t)
	})

	t.Run("Multi-User Room Scenario", func(t *testing.T) {
		testMultiUserRoomScenario(t)
	})
}

func testMessageCreationAndValidation(t *testing.T) {
	// Test creating different types of messages
	userID := uuid.New()
	roomID := "test-room-" + uuid.New().String()

	user := &model.User{
		UserID:   userID,
		Username: "testuser",
		Avatar:   "avatar.jpg",
		Role:     model.RoleViewer,
		Level:    5,
		VIPLevel: 2,
		Badges:   []string{"newbie"},
	}

	// Test barrage message
	barrageMsg := model.NewBarrageMessage(roomID, "Hello World!", user)
	assert.Equal(t, model.MessageTypeBarrage, barrageMsg.Type)
	assert.Equal(t, roomID, barrageMsg.RoomID)
	assert.Equal(t, "Hello World!", barrageMsg.Content)
	assert.Equal(t, user, barrageMsg.FromUser)
	assert.NotEmpty(t, barrageMsg.MessageID)
	assert.False(t, barrageMsg.Timestamp.IsZero())

	// Test like message
	likeMsg := model.NewLikeMessage(roomID, 5, user)
	assert.Equal(t, model.MessageTypeLike, likeMsg.Type)
	assert.Equal(t, roomID, likeMsg.RoomID)
	assert.Equal(t, 5, likeMsg.Count)
	assert.Equal(t, user, likeMsg.FromUser)

	// Test gift message
	giftMsg := model.NewGiftMessage(roomID, "rose", "recipient-123", 3, user)
	assert.Equal(t, model.MessageTypeGift, giftMsg.Type)
	assert.Equal(t, roomID, giftMsg.RoomID)
	assert.Equal(t, "rose", giftMsg.GiftID)
	assert.Equal(t, "recipient-123", giftMsg.ToUserID)
	assert.Equal(t, 3, giftMsg.Count)
	assert.Equal(t, user, giftMsg.FromUser)

	// Test error message
	errorMsg := model.NewErrorMessage("Test error")
	assert.Equal(t, model.MessageTypeError, errorMsg.Type)
	assert.Equal(t, "Test error", errorMsg.ErrorMsg)
	assert.False(t, errorMsg.Success)
}

func testUserAndRoomInteraction(t *testing.T) {
	// Test user creation and validation
	userID := uuid.New()
	user := &model.User{
		UserID:   userID,
		Username: "testuser",
		Avatar:   "avatar.jpg",
		Role:     model.RoleViewer,
		Level:    1,
		VIPLevel: 0,
		Badges:   []string{},
	}

	// Test user methods
	assert.False(t, user.HasBadge("vip"))
	user.AddBadge("vip")
	assert.True(t, user.HasBadge("vip"))
	assert.Len(t, user.Badges, 1)

	// Test adding duplicate badge
	user.AddBadge("vip")
	assert.Len(t, user.Badges, 1) // Should not duplicate

	// Test user role validation
	assert.True(t, user.IsViewer())
	assert.False(t, user.IsModerator())
	assert.False(t, user.IsAdmin())

	// Test room creation
	roomID := "test-room-" + uuid.New().String()
	room := model.NewRoom(roomID, "Test Room", "Test Description")
	assert.Equal(t, roomID, room.RoomID)
	assert.Equal(t, "Test Room", room.Title)
	assert.Equal(t, "Test Description", room.Description)
	assert.Equal(t, model.RoomStatusActive, room.Status)
	assert.Equal(t, 0, room.OnlineCount)

	// Test client creation
	client := model.NewClient(userID, "test-connection")
	assert.Equal(t, userID, client.UserID)
	assert.Equal(t, "test-connection", client.ConnectionID)
	assert.NotEmpty(t, client.ID)
	assert.False(t, client.ConnectedAt.IsZero())
	assert.True(t, client.IsActive())
}

func testMessageProcessingFlow(t *testing.T) {
	// Test message validation and processing
	userID := uuid.New()
	roomID := "test-room-" + uuid.New().String()

	user := &model.User{
		UserID:   userID,
		Username: "testuser",
		Role:     model.RoleViewer,
	}

	// Test message validation
	validMsg := model.NewBarrageMessage(roomID, "Valid message", user)
	assert.True(t, validMsg.IsValid())

	// Test invalid message (empty content)
	invalidMsg := model.Message{
		Type:      model.MessageTypeBarrage,
		MessageID: uuid.New().String(),
		RoomID:    roomID,
		Content:   "", // Empty content
		FromUser:  user,
		Timestamp: time.Now(),
	}
	assert.False(t, invalidMsg.IsValid())

	// Test message cloning
	clonedMsg := validMsg.Clone()
	assert.Equal(t, validMsg.MessageID, clonedMsg.MessageID)
	assert.Equal(t, validMsg.Type, clonedMsg.Type)
	assert.Equal(t, validMsg.Content, clonedMsg.Content)
	assert.Equal(t, validMsg.RoomID, clonedMsg.RoomID)

	// Verify it's a deep copy
	clonedMsg.Content = "Modified content"
	assert.NotEqual(t, validMsg.Content, clonedMsg.Content)
}

func testMultiUserRoomScenario(t *testing.T) {
	// Simulate a multi-user room scenario
	roomID := "test-room-" + uuid.New().String()
	
	// Create multiple users
	users := make([]*model.User, 3)
	for i := 0; i < 3; i++ {
		users[i] = &model.User{
			UserID:   uuid.New(),
			Username: "user" + string(rune('1'+i)),
			Role:     model.RoleViewer,
			Level:    i + 1,
		}
	}

	// Create room
	room := model.NewRoom(roomID, "Multi-User Test Room", "Testing multiple users")

	// Simulate users joining room
	for _, user := range users {
		client := model.NewClient(user.UserID, "conn-"+user.Username)
		room.AddClient(client)
	}

	assert.Equal(t, 3, room.OnlineCount)

	// Test message exchange
	messages := []model.Message{
		model.NewBarrageMessage(roomID, "Hello everyone!", users[0]),
		model.NewLikeMessage(roomID, 1, users[1]),
		model.NewBarrageMessage(roomID, "Nice to meet you!", users[2]),
		model.NewLikeMessage(roomID, 5, users[0]),
	}

	// Verify all messages are valid
	for i, msg := range messages {
		assert.True(t, msg.IsValid(), "Message %d should be valid", i)
		assert.Equal(t, roomID, msg.RoomID)
		assert.NotNil(t, msg.FromUser)
	}

	// Test room statistics
	assert.Equal(t, 3, room.OnlineCount)
	assert.Equal(t, model.RoomStatusActive, room.Status)

	// Test user leaving
	firstClient := room.GetClients()[0]
	room.RemoveClient(firstClient.ID)
	assert.Equal(t, 2, room.OnlineCount)

	// Test room cleanup when empty
	for _, client := range room.GetClients() {
		room.RemoveClient(client.ID)
	}
	assert.Equal(t, 0, room.OnlineCount)
}

// TestConcurrentMessageProcessing tests concurrent message handling
func TestConcurrentMessageProcessing(t *testing.T) {
	if testing.Short() {
		t.Skip("Skipping integration test in short mode")
	}

	const numGoroutines = 10
	const messagesPerGoroutine = 100

	roomID := "test-room-concurrent"
	user := &model.User{
		UserID:   uuid.New(),
		Username: "concurrent-user",
		Role:     model.RoleViewer,
	}

	// Channel to collect all messages
	messagesChan := make(chan model.Message, numGoroutines*messagesPerGoroutine)

	// Start multiple goroutines creating messages
	for i := 0; i < numGoroutines; i++ {
		go func(goroutineID int) {
			for j := 0; j < messagesPerGoroutine; j++ {
				msg := model.NewBarrageMessage(
					roomID,
					"Message from goroutine "+string(rune('0'+goroutineID)),
					user,
				)
				messagesChan <- msg
			}
		}(i)
	}

	// Collect all messages
	var messages []model.Message
	timeout := time.After(5 * time.Second)
	expectedCount := numGoroutines * messagesPerGoroutine

	for len(messages) < expectedCount {
		select {
		case msg := <-messagesChan:
			messages = append(messages, msg)
		case <-timeout:
			t.Fatalf("Timeout waiting for messages. Got %d, expected %d", len(messages), expectedCount)
		}
	}

	// Verify all messages are valid and unique
	messageIDs := make(map[string]bool)
	for _, msg := range messages {
		assert.True(t, msg.IsValid())
		assert.Equal(t, roomID, msg.RoomID)
		assert.Equal(t, user, msg.FromUser)
		
		// Check for unique message IDs
		assert.False(t, messageIDs[msg.MessageID], "Duplicate message ID: %s", msg.MessageID)
		messageIDs[msg.MessageID] = true
	}

	assert.Equal(t, expectedCount, len(messages))
	assert.Equal(t, expectedCount, len(messageIDs))
}
