/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:12:13
Modified: 2025-07-11 10:12:13
*/

package cache

import (
	"context"
	"fmt"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"cina.club/services/live-im-service/internal/application/port"
)

const (
	// Redis key patterns for rate limiting
	RateLimitKeyPrefix = "live:im:rate_limit:" // live:im:rate_limit:{userId}:{action}
)

// RateLimitConfig defines rate limiting configuration.
type RateLimitConfig struct {
	Enabled       bool          `mapstructure:"enabled"`
	BarrageRPS    int           `mapstructure:"barrage_rps"`
	LikeRPS       int           `mapstructure:"like_rps"`
	GiftRPS       int           `mapstructure:"gift_rps"`
	ConnectionRPS int           `mapstructure:"connection_rps"`
	WindowSize    time.Duration `mapstructure:"window_size"`
	MaxBurst      int           `mapstructure:"max_burst"`
}

// RedisRateLimiter implements the port.RateLimiter interface using Redis.
type RedisRateLimiter struct {
	client *redis.Client
	config RateLimitConfig
	logger *logrus.Entry
}

// NewRedisRateLimiter creates a new Redis rate limiter.
func NewRedisRateLimiter(client *redis.Client, config RateLimitConfig, logger *logrus.Logger) port.RateLimiter {
	return &RedisRateLimiter{
		client: client,
		config: config,
		logger: logger.WithField("component", "redis_rate_limiter"),
	}
}

// Allow checks if an action is allowed for a user.
func (r *RedisRateLimiter) Allow(ctx context.Context, userID uuid.UUID, action string) (bool, error) {
	return r.AllowN(ctx, userID, action, 1)
}

// AllowN checks if N actions are allowed for a user.
func (r *RedisRateLimiter) AllowN(ctx context.Context, userID uuid.UUID, action string, n int) (bool, error) {
	if !r.config.Enabled {
		return true, nil
	}

	// Get rate limit for the action
	limit := r.getRateLimit(action)
	if limit <= 0 {
		return true, nil // No limit configured
	}

	key := r.getRateLimitKey(userID, action)

	// Use sliding window rate limiting with Redis
	now := time.Now()
	windowStart := now.Add(-r.config.WindowSize)

	// Lua script for atomic sliding window rate limiting
	luaScript := `
		local key = KEYS[1]
		local window_start = tonumber(ARGV[1])
		local now = tonumber(ARGV[2])
		local limit = tonumber(ARGV[3])
		local count = tonumber(ARGV[4])
		local ttl = tonumber(ARGV[5])
		
		-- Remove expired entries
		redis.call('ZREMRANGEBYSCORE', key, 0, window_start)
		
		-- Get current count
		local current = redis.call('ZCARD', key)
		
		-- Check if adding count would exceed limit
		if current + count > limit then
			return 0
		end
		
		-- Add new entries
		for i = 1, count do
			redis.call('ZADD', key, now + i, now + i)
		end
		
		-- Set expiration
		redis.call('EXPIRE', key, ttl)
		
		return 1
	`

	// Execute Lua script
	result, err := r.client.Eval(ctx, luaScript, []string{key},
		windowStart.UnixNano(),
		now.UnixNano(),
		limit,
		n,
		int(r.config.WindowSize.Seconds())+1,
	).Result()

	if err != nil {
		return false, fmt.Errorf("failed to check rate limit: %w", err)
	}

	allowed := result.(int64) == 1

	r.logger.WithFields(logrus.Fields{
		"user_id": userID.String(),
		"action":  action,
		"count":   n,
		"limit":   limit,
		"allowed": allowed,
	}).Debug("Rate limit check")

	return allowed, nil
}

// Reset resets the rate limit for a user and action.
func (r *RedisRateLimiter) Reset(ctx context.Context, userID uuid.UUID, action string) error {
	key := r.getRateLimitKey(userID, action)

	if err := r.client.Del(ctx, key).Err(); err != nil {
		return fmt.Errorf("failed to reset rate limit: %w", err)
	}

	r.logger.WithFields(logrus.Fields{
		"user_id": userID.String(),
		"action":  action,
	}).Debug("Rate limit reset")

	return nil
}

// GetRemaining gets the remaining quota for a user and action.
func (r *RedisRateLimiter) GetRemaining(ctx context.Context, userID uuid.UUID, action string) (int, error) {
	if !r.config.Enabled {
		return 1000, nil // Return a large number if rate limiting is disabled
	}

	limit := r.getRateLimit(action)
	if limit <= 0 {
		return 1000, nil // No limit configured
	}

	key := r.getRateLimitKey(userID, action)
	windowStart := time.Now().Add(-r.config.WindowSize)

	// Remove expired entries and get current count
	luaScript := `
		local key = KEYS[1]
		local window_start = tonumber(ARGV[1])
		
		-- Remove expired entries
		redis.call('ZREMRANGEBYSCORE', key, 0, window_start)
		
		-- Get current count
		return redis.call('ZCARD', key)
	`

	result, err := r.client.Eval(ctx, luaScript, []string{key}, windowStart.UnixNano()).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to get remaining quota: %w", err)
	}

	current := int(result.(int64))
	remaining := limit - current
	if remaining < 0 {
		remaining = 0
	}

	return remaining, nil
}

// getRateLimit gets the rate limit for a specific action.
func (r *RedisRateLimiter) getRateLimit(action string) int {
	switch action {
	case "barrage":
		return r.config.BarrageRPS
	case "like":
		return r.config.LikeRPS
	case "gift":
		return r.config.GiftRPS
	case "connection":
		return r.config.ConnectionRPS
	default:
		return 0 // No limit for unknown actions
	}
}

// getRateLimitKey generates a Redis key for rate limiting.
func (r *RedisRateLimiter) getRateLimitKey(userID uuid.UUID, action string) string {
	return fmt.Sprintf("%s%s:%s", RateLimitKeyPrefix, userID.String(), action)
}

// GetStats gets rate limiting statistics (optional method for monitoring).
func (r *RedisRateLimiter) GetStats(ctx context.Context, userID uuid.UUID) (map[string]int, error) {
	stats := make(map[string]int)
	actions := []string{"barrage", "like", "gift", "connection"}

	for _, action := range actions {
		remaining, err := r.GetRemaining(ctx, userID, action)
		if err != nil {
			r.logger.WithError(err).WithFields(logrus.Fields{
				"user_id": userID.String(),
				"action":  action,
			}).Warn("Failed to get remaining quota")
			continue
		}
		stats[action] = remaining
	}

	return stats, nil
}

// CleanupExpiredEntries removes expired rate limit entries (maintenance method).
func (r *RedisRateLimiter) CleanupExpiredEntries(ctx context.Context) error {
	// Get all rate limit keys
	pattern := RateLimitKeyPrefix + "*"
	keys, err := r.client.Keys(ctx, pattern).Result()
	if err != nil {
		return fmt.Errorf("failed to get rate limit keys: %w", err)
	}

	windowStart := time.Now().Add(-r.config.WindowSize)

	// Clean up each key
	for _, key := range keys {
		if err := r.client.ZRemRangeByScore(ctx, key, "0", fmt.Sprintf("%d", windowStart.UnixNano())).Err(); err != nil {
			r.logger.WithError(err).WithField("key", key).Warn("Failed to cleanup expired entries")
		}
	}

	r.logger.WithField("keys_cleaned", len(keys)).Debug("Cleaned up expired rate limit entries")
	return nil
}
