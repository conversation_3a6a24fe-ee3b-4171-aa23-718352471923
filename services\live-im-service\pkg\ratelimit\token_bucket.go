/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package ratelimit

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// TokenBucketLimiter implements rate limiting using the token bucket algorithm
type TokenBucketLimiter struct {
	client           *redis.Client
	config           Config
	action           Action
	tier             Tier
	keyPrefix        string
	logger           *logrus.Logger
	metricsCollector MetricsCollector
}

// NewTokenBucketLimiter creates a new token bucket rate limiter
func NewTokenBucketLimiter(
	client *redis.Client,
	config Config,
	action Action,
	tier Tier,
	keyPrefix string,
	logger *logrus.Logger,
	metricsCollector MetricsCollector,
) *TokenBucketLimiter {
	return &TokenBucketLimiter{
		client:           client,
		config:           config,
		action:           action,
		tier:             tier,
		keyPrefix:        keyPrefix,
		logger:           logger,
		metricsCollector: metricsCollector,
	}
}

// Allow checks if a single request is allowed
func (t *TokenBucketLimiter) Allow(ctx context.Context, userID uuid.UUID, action Action) (*Result, error) {
	return t.AllowN(ctx, userID, action, 1)
}

// AllowN checks if N requests are allowed using token bucket algorithm
func (t *TokenBucketLimiter) AllowN(ctx context.Context, userID uuid.UUID, action Action, n int) (*Result, error) {
	start := time.Now()
	defer func() {
		if t.metricsCollector != nil {
			t.metricsCollector.RecordLatency(t.action, t.tier, time.Since(start))
		}
	}()

	if !t.config.Enabled {
		return &Result{
			Allowed:   true,
			Limit:     t.config.Limit,
			Remaining: t.config.Limit,
			ResetTime: time.Now().Add(t.config.Window),
		}, nil
	}

	// Check if user is blocked
	blocked, blockedUntil, err := t.IsBlocked(ctx, userID, action)
	if err != nil {
		return nil, fmt.Errorf("failed to check block status: %w", err)
	}

	if blocked {
		result := &Result{
			Allowed:      false,
			Limit:        t.config.Limit,
			Remaining:    0,
			Blocked:      true,
			BlockedUntil: blockedUntil,
			RetryAfter:   time.Until(blockedUntil),
		}

		if t.metricsCollector != nil {
			t.metricsCollector.RecordRequest(t.action, t.tier, false)
		}

		return result, nil
	}

	// Execute token bucket algorithm using Lua script for atomicity
	bucketKey := t.getBucketKey(userID)
	now := time.Now()

	luaScript := `
		local bucket_key = KEYS[1]
		local capacity = tonumber(ARGV[1])
		local tokens_requested = tonumber(ARGV[2])
		local refill_rate = tonumber(ARGV[3])
		local now = tonumber(ARGV[4])
		local window_seconds = tonumber(ARGV[5])
		
		-- Get current bucket state
		local bucket_data = redis.call('HMGET', bucket_key, 'tokens', 'last_refill')
		local current_tokens = tonumber(bucket_data[1]) or capacity
		local last_refill = tonumber(bucket_data[2]) or now
		
		-- Calculate tokens to add based on time elapsed
		local time_elapsed = now - last_refill
		local tokens_to_add = math.floor(time_elapsed / refill_rate)
		
		-- Refill tokens (up to capacity)
		current_tokens = math.min(capacity, current_tokens + tokens_to_add)
		
		-- Check if we have enough tokens
		local allowed = current_tokens >= tokens_requested
		local remaining = current_tokens
		
		if allowed then
			-- Consume tokens
			remaining = current_tokens - tokens_requested
		end
		
		-- Update bucket state
		redis.call('HMSET', bucket_key, 
			'tokens', remaining,
			'last_refill', now,
			'last_request', now
		)
		redis.call('EXPIRE', bucket_key, window_seconds * 2)
		
		return {
			allowed and 1 or 0,
			remaining,
			capacity,
			now + window_seconds
		}
	`

	refillRateMs := t.config.RefillRate.Milliseconds()
	if refillRateMs == 0 {
		refillRateMs = 1000 // Default to 1 second
	}

	result, err := t.client.Eval(ctx, luaScript, []string{bucketKey},
		t.config.Burst,                 // capacity
		n,                              // tokens_requested
		refillRateMs,                   // refill_rate in milliseconds
		now.UnixMilli(),                // now
		int(t.config.Window.Seconds()), // window_seconds
	).Result()

	if err != nil {
		return nil, fmt.Errorf("failed to execute token bucket script: %w", err)
	}

	resultSlice := result.([]interface{})
	allowed := resultSlice[0].(int64) == 1
	remaining := int(resultSlice[1].(int64))
	limit := int(resultSlice[2].(int64))
	resetTime := time.UnixMilli(resultSlice[3].(int64))

	// If request is denied and we should block the user
	if !allowed && t.config.BlockDuration > 0 {
		// Check if we should block based on consecutive failures
		consecutiveFailures, err := t.getConsecutiveFailures(ctx, userID)
		if err != nil {
			t.logger.WithError(err).Warn("Failed to get consecutive failures")
		} else if consecutiveFailures >= 5 { // Block after 5 consecutive failures
			blockErr := t.Block(ctx, userID, action, t.config.BlockDuration)
			if blockErr != nil {
				t.logger.WithError(blockErr).Warn("Failed to block user")
			} else {
				if t.metricsCollector != nil {
					t.metricsCollector.RecordBlock(t.action, t.tier, t.config.BlockDuration)
				}
			}
		}
	}

	// Record metrics
	if t.metricsCollector != nil {
		t.metricsCollector.RecordRequest(t.action, t.tier, allowed)
		t.metricsCollector.UpdateCurrentLimits(t.action, t.tier, limit, limit-remaining)
	}

	// Update consecutive failures counter
	if allowed {
		t.resetConsecutiveFailures(ctx, userID)
	} else {
		t.incrementConsecutiveFailures(ctx, userID)
	}

	return &Result{
		Allowed:   allowed,
		Limit:     limit,
		Remaining: remaining,
		ResetTime: resetTime,
	}, nil
}

// Reserve reserves N tokens for future use
func (t *TokenBucketLimiter) Reserve(ctx context.Context, userID uuid.UUID, action Action, n int) (*Result, error) {
	// For token bucket, reserve is the same as AllowN
	return t.AllowN(ctx, userID, action, n)
}

// Reset resets the rate limit for a user and action
func (t *TokenBucketLimiter) Reset(ctx context.Context, userID uuid.UUID, action Action) error {
	bucketKey := t.getBucketKey(userID)
	failuresKey := t.getFailuresKey(userID)
	blockKey := t.getBlockKey(userID)

	pipe := t.client.Pipeline()
	pipe.Del(ctx, bucketKey)
	pipe.Del(ctx, failuresKey)
	pipe.Del(ctx, blockKey)

	_, err := pipe.Exec(ctx)
	if err != nil {
		return fmt.Errorf("failed to reset rate limit: %w", err)
	}

	t.logger.WithFields(logrus.Fields{
		"user_id": userID.String(),
		"action":  string(action),
		"tier":    string(t.tier),
	}).Info("Rate limit reset")

	return nil
}

// GetStats returns current statistics
func (t *TokenBucketLimiter) GetStats(ctx context.Context, userID uuid.UUID, action Action) (*Stats, error) {
	bucketKey := t.getBucketKey(userID)
	blockKey := t.getBlockKey(userID)

	// Get bucket data
	bucketData, err := t.client.HMGet(ctx, bucketKey, "tokens", "last_refill", "last_request").Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get bucket data: %w", err)
	}

	// Get block data
	blockData, err := t.client.Get(ctx, blockKey).Result()
	isBlocked := err == nil && blockData != ""

	var blockExpiry time.Time
	if isBlocked {
		if blockTime, parseErr := strconv.ParseInt(blockData, 10, 64); parseErr == nil {
			blockExpiry = time.UnixMilli(blockTime)
		}
	}

	// Parse bucket data
	var tokens, lastRequest int64
	if bucketData[0] != nil {
		tokens, _ = strconv.ParseInt(bucketData[0].(string), 10, 64)
	} else {
		tokens = int64(t.config.Burst) // Default to full capacity
	}

	if bucketData[2] != nil {
		lastRequest, _ = strconv.ParseInt(bucketData[2].(string), 10, 64)
	}

	now := time.Now()
	windowStart := now.Truncate(t.config.Window)
	windowEnd := windowStart.Add(t.config.Window)

	return &Stats{
		Action:          action,
		UserID:          userID,
		Limit:           t.config.Burst,
		Usage:           t.config.Burst - int(tokens),
		Remaining:       int(tokens),
		WindowStart:     windowStart,
		WindowEnd:       windowEnd,
		TotalRequests:   0, // Would need additional tracking
		BlockedRequests: 0, // Would need additional tracking
		LastRequest:     time.UnixMilli(lastRequest),
		IsBlocked:       isBlocked,
		BlockExpiry:     blockExpiry,
	}, nil
}

// Block manually blocks a user for a specific action
func (t *TokenBucketLimiter) Block(ctx context.Context, userID uuid.UUID, action Action, duration time.Duration) error {
	blockKey := t.getBlockKey(userID)
	expiry := time.Now().Add(duration)

	err := t.client.Set(ctx, blockKey, expiry.UnixMilli(), duration).Err()
	if err != nil {
		return fmt.Errorf("failed to block user: %w", err)
	}

	t.logger.WithFields(logrus.Fields{
		"user_id":  userID.String(),
		"action":   string(action),
		"tier":     string(t.tier),
		"duration": duration.String(),
		"expiry":   expiry.Format(time.RFC3339),
	}).Warn("User blocked")

	return nil
}

// Unblock manually unblocks a user for a specific action
func (t *TokenBucketLimiter) Unblock(ctx context.Context, userID uuid.UUID, action Action) error {
	blockKey := t.getBlockKey(userID)

	err := t.client.Del(ctx, blockKey).Err()
	if err != nil {
		return fmt.Errorf("failed to unblock user: %w", err)
	}

	if t.metricsCollector != nil {
		t.metricsCollector.RecordUnblock(t.action, t.tier)
	}

	t.logger.WithFields(logrus.Fields{
		"user_id": userID.String(),
		"action":  string(action),
		"tier":    string(t.tier),
	}).Info("User unblocked")

	return nil
}

// IsBlocked checks if a user is currently blocked
func (t *TokenBucketLimiter) IsBlocked(ctx context.Context, userID uuid.UUID, action Action) (bool, time.Time, error) {
	blockKey := t.getBlockKey(userID)

	result, err := t.client.Get(ctx, blockKey).Result()
	if err == redis.Nil {
		return false, time.Time{}, nil
	}
	if err != nil {
		return false, time.Time{}, fmt.Errorf("failed to check block status: %w", err)
	}

	blockTime, err := strconv.ParseInt(result, 10, 64)
	if err != nil {
		return false, time.Time{}, fmt.Errorf("failed to parse block time: %w", err)
	}

	expiry := time.UnixMilli(blockTime)
	if time.Now().After(expiry) {
		// Block has expired, clean it up
		t.client.Del(ctx, blockKey)
		return false, time.Time{}, nil
	}

	return true, expiry, nil
}

// Helper methods

func (t *TokenBucketLimiter) getBucketKey(userID uuid.UUID) string {
	return fmt.Sprintf("%s:bucket:%s:%s:%s", t.keyPrefix, string(t.action), string(t.tier), userID.String())
}

func (t *TokenBucketLimiter) getBlockKey(userID uuid.UUID) string {
	return fmt.Sprintf("%s:block:%s:%s:%s", t.keyPrefix, string(t.action), string(t.tier), userID.String())
}

func (t *TokenBucketLimiter) getFailuresKey(userID uuid.UUID) string {
	return fmt.Sprintf("%s:failures:%s:%s:%s", t.keyPrefix, string(t.action), string(t.tier), userID.String())
}

func (t *TokenBucketLimiter) getConsecutiveFailures(ctx context.Context, userID uuid.UUID) (int, error) {
	failuresKey := t.getFailuresKey(userID)
	result, err := t.client.Get(ctx, failuresKey).Result()
	if err == redis.Nil {
		return 0, nil
	}
	if err != nil {
		return 0, err
	}

	failures, err := strconv.Atoi(result)
	if err != nil {
		return 0, err
	}

	return failures, nil
}

func (t *TokenBucketLimiter) incrementConsecutiveFailures(ctx context.Context, userID uuid.UUID) {
	failuresKey := t.getFailuresKey(userID)
	pipe := t.client.Pipeline()
	pipe.Incr(ctx, failuresKey)
	pipe.Expire(ctx, failuresKey, t.config.Window*2) // Expire after 2 windows
	pipe.Exec(ctx)
}

func (t *TokenBucketLimiter) resetConsecutiveFailures(ctx context.Context, userID uuid.UUID) {
	failuresKey := t.getFailuresKey(userID)
	t.client.Del(ctx, failuresKey)
}
