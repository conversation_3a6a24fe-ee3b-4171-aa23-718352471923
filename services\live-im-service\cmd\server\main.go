/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-27 13:00:00
Modified: 2025-06-27 13:00:00
*/

package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"

	// Internal packages
	"cina.club/services/live-im-service/internal/adapter/cache"
	"cina.club/services/live-im-service/internal/adapter/client"
	redisAdapter "cina.club/services/live-im-service/internal/adapter/redis"
	"cina.club/services/live-im-service/internal/adapter/transport/websocket"
	"cina.club/services/live-im-service/internal/application/service"
	// Shared packages
	// "cina.club/pkg/middleware" // TODO: Fix module import
)

const (
	serviceName = "live-im-service"
	version     = "1.0.0"
)

// Config defines the service configuration.
type Config struct {
	Server     ServerConfig     `mapstructure:"server"`
	Redis      RedisConfig      `mapstructure:"redis"`
	Clients    ClientsConfig    `mapstructure:"clients"`
	Hub        HubConfig        `mapstructure:"hub"`
	WebSocket  WebSocketConfig  `mapstructure:"websocket"`
	RateLimit  RateLimitConfig  `mapstructure:"rate_limit"`
	Logging    LoggingConfig    `mapstructure:"logging"`
	Tracing    TracingConfig    `mapstructure:"tracing"`
	Security   SecurityConfig   `mapstructure:"security"`
	Monitoring MonitoringConfig `mapstructure:"monitoring"`
}

// ServerConfig defines the server configurations.
type ServerConfig struct {
	HTTPAddr       string        `mapstructure:"http_addr"`
	HealthAddr     string        `mapstructure:"health_addr"`
	MetricsAddr    string        `mapstructure:"metrics_addr"`
	ReadTimeout    time.Duration `mapstructure:"read_timeout"`
	WriteTimeout   time.Duration `mapstructure:"write_timeout"`
	IdleTimeout    time.Duration `mapstructure:"idle_timeout"`
	MaxHeaderBytes int           `mapstructure:"max_header_bytes"`
	TLSCertFile    string        `mapstructure:"tls_cert_file"`
	TLSKeyFile     string        `mapstructure:"tls_key_file"`
	EnableTLS      bool          `mapstructure:"enable_tls"`
}

// RedisConfig defines the Redis configurations.
type RedisConfig struct {
	Host         string        `mapstructure:"host"`
	Port         int           `mapstructure:"port"`
	Password     string        `mapstructure:"password"`
	Database     int           `mapstructure:"database"`
	PoolSize     int           `mapstructure:"pool_size"`
	MinIdleConns int           `mapstructure:"min_idle_conns"`
	DialTimeout  time.Duration `mapstructure:"dial_timeout"`
	ReadTimeout  time.Duration `mapstructure:"read_timeout"`
	WriteTimeout time.Duration `mapstructure:"write_timeout"`
}

// ClientsConfig defines the client configurations.
type ClientsConfig struct {
	LiveAPI  GRPCClientConfig `mapstructure:"live_api"`
	UserCore GRPCClientConfig `mapstructure:"user_core"`
	Billing  GRPCClientConfig `mapstructure:"billing"`
	CinaCoin GRPCClientConfig `mapstructure:"cina_coin"`
}

// GRPCClientConfig defines the gRPC client configurations.
type GRPCClientConfig struct {
	Endpoint   string        `mapstructure:"endpoint"`
	Timeout    time.Duration `mapstructure:"timeout"`
	MaxRetries int           `mapstructure:"max_retries"`
	EnableTLS  bool          `mapstructure:"enable_tls"`
	EnableAuth bool          `mapstructure:"enable_auth"`
}

// HubConfig defines the Hub configurations.
type HubConfig struct {
	MaxClients            int           `mapstructure:"max_clients"`
	MaxRooms              int           `mapstructure:"max_rooms"`
	HeartbeatInterval     time.Duration `mapstructure:"heartbeat_interval"`
	CleanupInterval       time.Duration `mapstructure:"cleanup_interval"`
	StatsInterval         time.Duration `mapstructure:"stats_interval"`
	MessageBufferSize     int           `mapstructure:"message_buffer_size"`
	BroadcastBufferSize   int           `mapstructure:"broadcast_buffer_size"`
	LikeAggregateInterval time.Duration `mapstructure:"like_aggregate_interval"`
	MaxMessageSize        int           `mapstructure:"max_message_size"`
	EnableMetrics         bool          `mapstructure:"enable_metrics"`
	EnableRoomAnalytics   bool          `mapstructure:"enable_room_analytics"`
}

// WebSocketConfig defines the WebSocket configurations.
type WebSocketConfig struct {
	WriteWait         time.Duration `mapstructure:"write_wait"`
	PongWait          time.Duration `mapstructure:"pong_wait"`
	PingPeriod        time.Duration `mapstructure:"ping_period"`
	MaxMessageSize    int64         `mapstructure:"max_message_size"`
	ReadBufferSize    int           `mapstructure:"read_buffer_size"`
	WriteBufferSize   int           `mapstructure:"write_buffer_size"`
	CheckOrigin       bool          `mapstructure:"check_origin"`
	EnableCompression bool          `mapstructure:"enable_compression"`
}

// RateLimitConfig defines the rate limiting configurations.
type RateLimitConfig struct {
	Enabled       bool          `mapstructure:"enabled"`
	BarrageRPS    int           `mapstructure:"barrage_rps"`
	LikeRPS       int           `mapstructure:"like_rps"`
	GiftRPS       int           `mapstructure:"gift_rps"`
	ConnectionRPS int           `mapstructure:"connection_rps"`
	WindowSize    time.Duration `mapstructure:"window_size"`
	MaxBurst      int           `mapstructure:"max_burst"`
}

// LoggingConfig defines the logging configurations.
type LoggingConfig struct {
	Level            string `mapstructure:"level"`
	Format           string `mapstructure:"format"`
	Output           string `mapstructure:"output"`
	EnableStructured bool   `mapstructure:"enable_structured"`
}

// TracingConfig defines the tracing configurations.
type TracingConfig struct {
	Enabled     bool    `mapstructure:"enabled"`
	ServiceName string  `mapstructure:"service_name"`
	Endpoint    string  `mapstructure:"endpoint"`
	SampleRate  float64 `mapstructure:"sample_rate"`
}

// SecurityConfig defines the security configurations.
type SecurityConfig struct {
	JWTSecret           string   `mapstructure:"jwt_secret"`
	AllowedOrigins      []string `mapstructure:"allowed_origins"`
	EnableIPWhitelist   bool     `mapstructure:"enable_ip_whitelist"`
	IPWhitelist         []string `mapstructure:"ip_whitelist"`
	MaxConnectionsPerIP int      `mapstructure:"max_connections_per_ip"`
	EnableCORS          bool     `mapstructure:"enable_cors"`
}

// MonitoringConfig defines the monitoring configurations.
type MonitoringConfig struct {
	EnableMetrics bool   `mapstructure:"enable_metrics"`
	MetricsPath   string `mapstructure:"metrics_path"`
	EnablePprof   bool   `mapstructure:"enable_pprof"`
	PprofPath     string `mapstructure:"pprof_path"`
	EnableHealthz bool   `mapstructure:"enable_healthz"`
	HealthzPath   string `mapstructure:"healthz_path"`
}

func main() {
	// Setup logger
	log := logrus.New()
	log.SetFormatter(&logrus.JSONFormatter{})

	// Load configuration
	cfg, err := loadConfig()
	if err != nil {
		log.WithError(err).Fatal("Failed to load configuration")
	}

	// Setup structured logging
	if err := setupLogging(cfg.Logging, log); err != nil {
		log.WithError(err).Fatal("Failed to setup logging")
	}

	log.WithFields(logrus.Fields{
		"service": serviceName,
		"version": version,
	}).Info("Starting Live IM Service")

	// Initialize tracing
	if cfg.Tracing.Enabled {
		cleanup, err := setupTracing(cfg.Tracing)
		if err != nil {
			log.WithError(err).Fatal("Failed to setup tracing")
		}
		defer cleanup()
	}

	// Initialize Redis client
	redisClient, err := setupRedis(cfg.Redis, log)
	if err != nil {
		log.WithError(err).Fatal("Failed to setup Redis")
	}
	defer redisClient.Close()

	// Initialize external service clients
	liveAPIClient, err := setupLiveAPIClient(cfg.Clients.LiveAPI, log)
	if err != nil {
		log.WithError(err).Fatal("Failed to setup Live API client")
	}

	userCoreClient, err := setupUserCoreClient(cfg.Clients.UserCore, log)
	if err != nil {
		log.WithError(err).Fatal("Failed to setup User Core client")
	}

	billingClient, err := setupBillingClient(cfg.Clients.Billing, log)
	if err != nil {
		log.WithError(err).Fatal("Failed to setup Billing client")
	}

	cinaCoinClient, err := setupCinaCoinClient(cfg.Clients.CinaCoin, log)
	if err != nil {
		log.WithError(err).Fatal("Failed to setup CinaCoin client")
	}

	// Initialize adapters
	redisBroadcaster := redisAdapter.NewBroadcaster(redisClient, log)
	roomStore := redisAdapter.NewRoomStore(redisClient, log)

	// Convert config to cache config
	cacheConfig := cache.RateLimitConfig{
		Enabled:       cfg.RateLimit.Enabled,
		BarrageRPS:    cfg.RateLimit.BarrageRPS,
		LikeRPS:       cfg.RateLimit.LikeRPS,
		GiftRPS:       cfg.RateLimit.GiftRPS,
		ConnectionRPS: cfg.RateLimit.ConnectionRPS,
		WindowSize:    cfg.RateLimit.WindowSize,
		MaxBurst:      cfg.RateLimit.MaxBurst,
	}
	rateLimiter := cache.NewRedisRateLimiter(redisClient, cacheConfig, log)

	// Initialize auth service
	authService := client.NewAuthService(userCoreClient, cfg.Security.JWTSecret, log)

	// Initialize message processor
	messageProcessor := service.NewMessageProcessor(
		liveAPIClient,
		billingClient,
		cinaCoinClient,
		log,
	)

	// Initialize Hub
	hubConfig := &service.HubConfig{
		MaxClients:            cfg.Hub.MaxClients,
		MaxRooms:              cfg.Hub.MaxRooms,
		HeartbeatInterval:     cfg.Hub.HeartbeatInterval,
		CleanupInterval:       cfg.Hub.CleanupInterval,
		StatsInterval:         cfg.Hub.StatsInterval,
		MessageBufferSize:     cfg.Hub.MessageBufferSize,
		BroadcastBufferSize:   cfg.Hub.BroadcastBufferSize,
		LikeAggregateInterval: cfg.Hub.LikeAggregateInterval,
		MaxMessageSize:        cfg.Hub.MaxMessageSize,
		EnableMetrics:         cfg.Hub.EnableMetrics,
		EnableRoomAnalytics:   cfg.Hub.EnableRoomAnalytics,
	}

	hub := service.NewHub(
		hubConfig,
		redisBroadcaster,
		roomStore,
		messageProcessor,
		rateLimiter,
		authService,
		log,
	)

	// Start Hub
	if err := hub.Start(); err != nil {
		log.WithError(err).Fatal("Failed to start Hub")
	}
	defer hub.Stop()

	// Initialize WebSocket handler
	wsConfig := websocket.WebSocketConfig{
		WriteWait:         cfg.WebSocket.WriteWait,
		PongWait:          cfg.WebSocket.PongWait,
		PingPeriod:        cfg.WebSocket.PingPeriod,
		MaxMessageSize:    cfg.WebSocket.MaxMessageSize,
		ReadBufferSize:    cfg.WebSocket.ReadBufferSize,
		WriteBufferSize:   cfg.WebSocket.WriteBufferSize,
		CheckOrigin:       cfg.WebSocket.CheckOrigin,
		EnableCompression: cfg.WebSocket.EnableCompression,
	}

	securityConfig := websocket.SecurityConfig{
		JWTSecret:           cfg.Security.JWTSecret,
		AllowedOrigins:      cfg.Security.AllowedOrigins,
		EnableIPWhitelist:   cfg.Security.EnableIPWhitelist,
		IPWhitelist:         cfg.Security.IPWhitelist,
		MaxConnectionsPerIP: cfg.Security.MaxConnectionsPerIP,
		EnableCORS:          cfg.Security.EnableCORS,
	}

	wsHandler := websocket.NewHandler(
		hub,
		wsConfig,
		securityConfig,
		log,
	)

	// Setup HTTP server
	httpServer := setupHTTPServer(cfg.Server, wsHandler, hub, log)

	// Start HTTP server
	go func() {
		log.WithField("addr", cfg.Server.HTTPAddr).Info("Starting HTTP server")
		if cfg.Server.EnableTLS {
			if err := httpServer.ListenAndServeTLS(cfg.Server.TLSCertFile, cfg.Server.TLSKeyFile); err != nil && err != http.ErrServerClosed {
				log.WithError(err).Fatal("HTTP server failed")
			}
		} else {
			if err := httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
				log.WithError(err).Fatal("HTTP server failed")
			}
		}
	}()

	// Start health check server
	if cfg.Server.HealthAddr != "" {
		go func() {
			startHealthServer(cfg.Server.HealthAddr, hub, log)
		}()
	}

	// Start metrics server
	if cfg.Server.MetricsAddr != "" {
		go func() {
			startMetricsServer(cfg.Server.MetricsAddr, log)
		}()
	}

	// Wait for interrupt signal
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	<-sigChan
	log.Info("Received shutdown signal")

	// Graceful shutdown
	shutdownCtx, shutdownCancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer shutdownCancel()

	log.Info("Shutting down HTTP server...")
	if err := httpServer.Shutdown(shutdownCtx); err != nil {
		log.WithError(err).Error("Failed to shutdown HTTP server")
	}

	log.Info("Live IM Service stopped")
}

// loadConfig loads the configuration.
func loadConfig() (*Config, error) {
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath("/etc/live-im")
	viper.AddConfigPath(".")

	// Set environment variable prefix
	viper.SetEnvPrefix("LIVE_IM")
	viper.AutomaticEnv()

	// Set default values
	setDefaults()

	if err := viper.ReadInConfig(); err != nil {
		return nil, fmt.Errorf("failed to read config file: %w", err)
	}

	var cfg Config
	if err := viper.Unmarshal(&cfg); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	return &cfg, nil
}

// setDefaults sets the default configuration values.
func setDefaults() {
	// Server default configuration
	viper.SetDefault("server.http_addr", ":8080")
	viper.SetDefault("server.health_addr", ":8081")
	viper.SetDefault("server.metrics_addr", ":9090")
	viper.SetDefault("server.read_timeout", "30s")
	viper.SetDefault("server.write_timeout", "30s")
	viper.SetDefault("server.idle_timeout", "120s")
	viper.SetDefault("server.max_header_bytes", 1048576)
	viper.SetDefault("server.enable_tls", false)

	// Redis default configuration
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.database", 3)
	viper.SetDefault("redis.pool_size", 20)
	viper.SetDefault("redis.min_idle_conns", 5)
	viper.SetDefault("redis.dial_timeout", "5s")
	viper.SetDefault("redis.read_timeout", "3s")
	viper.SetDefault("redis.write_timeout", "3s")

	// Client default configuration
	viper.SetDefault("clients.live_api.endpoint", "live-api-service:50051")
	viper.SetDefault("clients.live_api.timeout", "10s")
	viper.SetDefault("clients.live_api.max_retries", 3)

	viper.SetDefault("clients.user_core.endpoint", "user-core-service:50051")
	viper.SetDefault("clients.user_core.timeout", "10s")
	viper.SetDefault("clients.user_core.max_retries", 3)

	viper.SetDefault("clients.billing.endpoint", "billing-service:50051")
	viper.SetDefault("clients.billing.timeout", "10s")
	viper.SetDefault("clients.billing.max_retries", 3)

	viper.SetDefault("clients.cina_coin.endpoint", "cina-coin-ledger-service:50051")
	viper.SetDefault("clients.cina_coin.timeout", "10s")
	viper.SetDefault("clients.cina_coin.max_retries", 3)

	// Hub default configuration
	viper.SetDefault("hub.max_clients", 50000)
	viper.SetDefault("hub.max_rooms", 1000)
	viper.SetDefault("hub.heartbeat_interval", "30s")
	viper.SetDefault("hub.cleanup_interval", "5m")
	viper.SetDefault("hub.stats_interval", "1m")
	viper.SetDefault("hub.message_buffer_size", 1000)
	viper.SetDefault("hub.broadcast_buffer_size", 10000)
	viper.SetDefault("hub.like_aggregate_interval", "1s")
	viper.SetDefault("hub.max_message_size", 512)
	viper.SetDefault("hub.enable_metrics", true)
	viper.SetDefault("hub.enable_room_analytics", true)

	// WebSocket default configuration
	viper.SetDefault("websocket.write_wait", "10s")
	viper.SetDefault("websocket.pong_wait", "60s")
	viper.SetDefault("websocket.ping_period", "54s")
	viper.SetDefault("websocket.max_message_size", 512)
	viper.SetDefault("websocket.read_buffer_size", 1024)
	viper.SetDefault("websocket.write_buffer_size", 1024)
	viper.SetDefault("websocket.check_origin", false)
	viper.SetDefault("websocket.enable_compression", true)

	// Rate limit default configuration
	viper.SetDefault("rate_limit.enabled", true)
	viper.SetDefault("rate_limit.barrage_rps", 1)
	viper.SetDefault("rate_limit.like_rps", 10)
	viper.SetDefault("rate_limit.gift_rps", 1)
	viper.SetDefault("rate_limit.connection_rps", 10)
	viper.SetDefault("rate_limit.window_size", "1s")
	viper.SetDefault("rate_limit.max_burst", 5)

	// Logging default configuration
	viper.SetDefault("logging.level", "info")
	viper.SetDefault("logging.format", "json")
	viper.SetDefault("logging.output", "stdout")
	viper.SetDefault("logging.enable_structured", true)

	// Tracing default configuration
	viper.SetDefault("tracing.enabled", false)
	viper.SetDefault("tracing.service_name", serviceName)
	viper.SetDefault("tracing.sample_rate", 0.1)

	// Security default configuration
	viper.SetDefault("security.enable_ip_whitelist", false)
	viper.SetDefault("security.max_connections_per_ip", 100)
	viper.SetDefault("security.enable_cors", true)

	// Monitoring default configuration
	viper.SetDefault("monitoring.enable_metrics", true)
	viper.SetDefault("monitoring.metrics_path", "/metrics")
	viper.SetDefault("monitoring.enable_pprof", false)
	viper.SetDefault("monitoring.pprof_path", "/debug/pprof")
	viper.SetDefault("monitoring.enable_healthz", true)
	viper.SetDefault("monitoring.healthz_path", "/health")
}

// setupLogging sets up the logger.
func setupLogging(cfg LoggingConfig, log *logrus.Logger) error {
	// Set log level
	level, err := logrus.ParseLevel(cfg.Level)
	if err != nil {
		return fmt.Errorf("invalid log level: %w", err)
	}
	log.SetLevel(level)

	// Set log format
	if cfg.Format == "json" {
		log.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339,
		})
	} else {
		log.SetFormatter(&logrus.TextFormatter{
			FullTimestamp: true,
		})
	}

	return nil
}

// setupTracing sets up tracing.
func setupTracing(cfg TracingConfig) (func(), error) {
	// TODO: Implement Jaeger/OpenTelemetry tracing
	return func() {}, nil
}

// setupRedis sets up the Redis client.
func setupRedis(cfg RedisConfig, log *logrus.Logger) (*redis.Client, error) {
	client := redis.NewClient(&redis.Options{
		Addr:         fmt.Sprintf("%s:%d", cfg.Host, cfg.Port),
		Password:     cfg.Password,
		DB:           cfg.Database,
		PoolSize:     cfg.PoolSize,
		MinIdleConns: cfg.MinIdleConns,
		DialTimeout:  cfg.DialTimeout,
		ReadTimeout:  cfg.ReadTimeout,
		WriteTimeout: cfg.WriteTimeout,
	})

	// Test connection
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := client.Ping(ctx).Err(); err != nil {
		return nil, fmt.Errorf("failed to connect to Redis: %w", err)
	}

	log.Info("Redis connection established")
	return client, nil
}

// setupHTTPServer sets up the HTTP server.
func setupHTTPServer(cfg ServerConfig, wsHandler *websocket.Handler, hub *service.Hub, log *logrus.Logger) *http.Server {
	// Create Gin engine
	gin.SetMode(gin.ReleaseMode)
	engine := gin.New()

	// Add middleware
	engine.Use(gin.Recovery())
	// TODO: Add custom middleware when pkg/middleware import is fixed
	// engine.Use(middleware.RequestLogger(log))
	// engine.Use(middleware.CORS())

	// Register WebSocket route
	engine.GET("/ws", wsHandler.HandleWebSocket)

	// Register management API
	api := engine.Group("/api/v1")
	{
		api.GET("/stats", func(c *gin.Context) {
			stats := hub.GetStats()
			c.JSON(http.StatusOK, stats)
		})

		api.GET("/rooms/:room_id", func(c *gin.Context) {
			roomID := c.Param("room_id")
			if room, exists := hub.GetRoomInfo(roomID); exists {
				c.JSON(http.StatusOK, room.GetInfo())
			} else {
				c.JSON(http.StatusNotFound, gin.H{"error": "Room not found"})
			}
		})
	}

	return &http.Server{
		Addr:           cfg.HTTPAddr,
		Handler:        engine,
		ReadTimeout:    cfg.ReadTimeout,
		WriteTimeout:   cfg.WriteTimeout,
		IdleTimeout:    cfg.IdleTimeout,
		MaxHeaderBytes: cfg.MaxHeaderBytes,
	}
}

// startHealthServer starts the health check server.
func startHealthServer(addr string, hub *service.Hub, log *logrus.Logger) {
	mux := http.NewServeMux()

	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		stats := hub.GetStats()

		health := map[string]interface{}{
			"status":            "healthy",
			"timestamp":         time.Now(),
			"uptime":            time.Since(stats.StartTime),
			"connected_clients": stats.ConnectedClients,
			"active_rooms":      stats.ActiveRooms,
		}

		w.Header().Set("Content-Type", "application/json")
		json.NewEncoder(w).Encode(health)
	})

	server := &http.Server{
		Addr:    addr,
		Handler: mux,
	}

	log.WithField("addr", addr).Info("Starting health check server")
	if err := server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
		log.WithError(err).Error("Health check server failed")
	}
}

// startMetricsServer starts the metrics server.
func startMetricsServer(addr string, log *logrus.Logger) {
	// TODO: Implement Prometheus metrics server
	log.WithField("addr", addr).Info("Starting metrics server")
}

// Client setup functions
func setupLiveAPIClient(cfg GRPCClientConfig, log *logrus.Logger) (interface{}, error) {
	// TODO: Implement gRPC client
	log.Info("Live API client setup completed")
	return nil, nil
}

func setupUserCoreClient(cfg GRPCClientConfig, log *logrus.Logger) (interface{}, error) {
	// TODO: Implement gRPC client
	log.Info("User Core client setup completed")
	return nil, nil
}

func setupBillingClient(cfg GRPCClientConfig, log *logrus.Logger) (interface{}, error) {
	// TODO: Implement gRPC client
	log.Info("Billing client setup completed")
	return nil, nil
}

func setupCinaCoinClient(cfg GRPCClientConfig, log *logrus.Logger) (interface{}, error) {
	// TODO: Implement gRPC client
	log.Info("CinaCoin client setup completed")
	return nil, nil
}
