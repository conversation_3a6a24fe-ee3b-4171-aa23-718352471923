# Circuit Breaker Pattern Implementation

This document describes the comprehensive circuit breaker pattern implementation for the Live IM Service, providing resilience and fault tolerance for external service dependencies.

## Overview

The circuit breaker pattern prevents cascading failures by monitoring external service calls and temporarily blocking requests when a service is detected as failing. This implementation provides:

- **Automatic Failure Detection**: Monitors request success/failure rates
- **State Management**: Closed, Open, and Half-Open states with automatic transitions
- **Service Protection**: Prevents overwhelming failing services
- **Fast Failure**: Immediate rejection of requests when service is down
- **Automatic Recovery**: Gradual testing of service recovery
- **Comprehensive Monitoring**: Detailed metrics and health checks

## Architecture

### Core Components

1. **CircuitBreaker**: Individual circuit breaker for a specific service
2. **Manager**: Centralized management of multiple circuit breakers
3. **Wrappers**: Service-specific wrappers for Redis, HTTP clients, etc.
4. **HTTP Handler**: REST API for monitoring and management
5. **Metrics Collector**: Pluggable metrics collection interface

### Circuit Breaker States

```
┌─────────────┐    Failure Threshold    ┌─────────────┐
│   CLOSED    │ ────────────────────────▶│    OPEN     │
│             │                         │             │
│ Requests    │                         │ Requests    │
│ Pass Through│                         │ Rejected    │
└─────────────┘                         └─────────────┘
       ▲                                        │
       │                                        │
       │ Success Threshold                      │ Timeout
       │                                        │
       │                ┌─────────────┐         │
       └────────────────│ HALF-OPEN   │◀────────┘
                        │             │
                        │ Limited     │
                        │ Requests    │
                        └─────────────┘
```

## Configuration

### Basic Circuit Breaker Configuration

```go
config := circuitbreaker.Config{
    Name:        "redis",
    MaxRequests: 10,                    // Max requests in half-open state
    Interval:    60 * time.Second,      // Reset interval for closed state
    Timeout:     30 * time.Second,      // Timeout before half-open transition
    ReadyToTrip: func(counts Counts) bool {
        // Open circuit if failure rate >= 60% and min 10 requests
        failureRate := float64(counts.TotalFailures) / float64(counts.Requests)
        return counts.Requests >= 10 && failureRate >= 0.6
    },
    IsSuccessful: func(err error) bool {
        // Consider request successful if no error
        return err == nil
    },
    OnStateChange: func(name string, from State, to State) {
        log.Printf("Circuit breaker %s: %s -> %s", name, from, to)
    },
}
```

### Manager Configuration

```go
managerConfig := circuitbreaker.ManagerConfig{
    DefaultConfig: circuitbreaker.Config{
        MaxRequests: 10,
        Interval:    60 * time.Second,
        Timeout:     30 * time.Second,
    },
    ServiceConfigs: map[string]circuitbreaker.Config{
        "redis": {
            MaxRequests: 5,
            Timeout:     15 * time.Second,
            ReadyToTrip: func(counts Counts) bool {
                return counts.ConsecutiveFailures >= 3
            },
        },
        "live_api": {
            MaxRequests: 8,
            Timeout:     45 * time.Second,
            ReadyToTrip: func(counts Counts) bool {
                return counts.ConsecutiveFailures >= 5
            },
        },
    },
    MetricsCollector: circuitbreaker.NewDefaultMetricsCollector(logger),
}
```

## Usage Examples

### Basic Usage

```go
// Create circuit breaker manager
manager := circuitbreaker.NewManager(managerConfig, logger)
defer manager.Close()

// Execute operation with circuit breaker protection
err := manager.Execute("redis", func() error {
    return redisClient.Set(ctx, "key", "value", time.Hour).Err()
})

if err != nil {
    if err == circuitbreaker.ErrOpenState {
        // Circuit is open, service is down
        log.Warn("Redis service is currently unavailable")
        return handleRedisUnavailable()
    }
    return err
}
```

### With Context

```go
ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
defer cancel()

err := manager.ExecuteWithContext(ctx, "live_api", func(ctx context.Context) error {
    return liveAPIClient.GetRoomInfo(ctx, roomID)
})
```

### Service Wrappers

```go
// Redis wrapper with circuit breaker
redisWrapper := circuitbreaker.NewRedisClientWrapper(redisClient, manager, logger)

// Use wrapper instead of direct client
result := redisWrapper.Get(ctx, "user:123")
if result.Err() != nil {
    // Handle error (including circuit breaker errors)
}

// LiveAPI wrapper
liveAPIWrapper := circuitbreaker.NewLiveAPIClientWrapper(liveAPIClient, manager, logger)

roomInfo, err := liveAPIWrapper.GetRoomInfo(ctx, "room-456")
if err != nil {
    // Automatically handles circuit breaker protection
}
```

## Integration with Live IM Service

### Service Dependencies Protected

1. **Redis**: Caching, pub/sub, rate limiting
2. **Live API Service**: Room information and access control
3. **Billing Service**: Payment processing
4. **CinaCoin Service**: Virtual currency transactions
5. **Auth Service**: Authentication and authorization

### Example Integration

```go
// In main.go or service initialization
func setupCircuitBreakers(logger *logrus.Logger) *circuitbreaker.Manager {
    config := circuitbreaker.ManagerConfig{
        DefaultConfig: circuitbreaker.Config{
            MaxRequests: 10,
            Interval:    60 * time.Second,
            Timeout:     30 * time.Second,
        },
        ServiceConfigs: map[string]circuitbreaker.Config{
            "redis": {
                MaxRequests: 5,
                Timeout:     15 * time.Second,
                ReadyToTrip: func(counts circuitbreaker.Counts) bool {
                    return counts.ConsecutiveFailures >= 3
                },
            },
            "live_api": {
                MaxRequests: 8,
                Timeout:     45 * time.Second,
            },
            "billing": {
                MaxRequests: 3,
                Timeout:     60 * time.Second,
                ReadyToTrip: func(counts circuitbreaker.Counts) bool {
                    // More conservative for billing
                    return counts.ConsecutiveFailures >= 2
                },
            },
        },
    }
    
    return circuitbreaker.NewManager(config, logger)
}

// In service layer
func (s *HubService) JoinRoom(ctx context.Context, userID uuid.UUID, roomID string) error {
    // Check room access with circuit breaker protection
    err := s.circuitBreakerManager.ExecuteWithContext(ctx, "live_api", func(ctx context.Context) error {
        hasAccess, err := s.liveAPIClient.CheckRoomAccess(ctx, userID, roomID)
        if err != nil {
            return err
        }
        if !hasAccess {
            return errors.New("access denied")
        }
        return nil
    })
    
    if err != nil {
        if err == circuitbreaker.ErrOpenState {
            // Fallback: allow join but log for monitoring
            s.logger.Warn("Live API unavailable, allowing room join with degraded validation")
            return s.joinRoomWithDegradedValidation(ctx, userID, roomID)
        }
        return err
    }
    
    return s.joinRoomNormally(ctx, userID, roomID)
}
```

## HTTP API

### Endpoints

- `GET /circuit-breakers/` - Get all circuit breaker statistics
- `GET /circuit-breakers/{name}` - Get specific circuit breaker statistics
- `POST /circuit-breakers/{name}/reset` - Reset a circuit breaker
- `GET /circuit-breakers/health` - Health check endpoint

### Example Responses

#### Get All Statistics

```json
{
  "circuit_breakers": {
    "redis": {
      "name": "redis",
      "state": "CLOSED",
      "requests": 1000,
      "total_successes": 950,
      "total_failures": 50,
      "consecutive_successes": 10,
      "consecutive_failures": 0,
      "success_rate": 0.95
    },
    "live_api": {
      "name": "live_api",
      "state": "OPEN",
      "requests": 100,
      "total_successes": 40,
      "total_failures": 60,
      "consecutive_successes": 0,
      "consecutive_failures": 15,
      "success_rate": 0.4
    }
  },
  "summary": {
    "total": 2,
    "closed": 1,
    "open": 1,
    "half_open": 0,
    "healthy": 1,
    "unhealthy": 1
  },
  "timestamp": "2025-07-11T10:00:00Z"
}
```

#### Health Check

```json
{
  "status": "unhealthy",
  "healthy": false,
  "summary": {
    "total": 5,
    "closed": 3,
    "open": 2,
    "half_open": 0,
    "healthy": 3,
    "unhealthy": 2
  },
  "timestamp": "2025-07-11T10:00:00Z"
}
```

## Monitoring and Metrics

### Built-in Metrics

- Request count per service
- Success/failure rates
- Circuit breaker state changes
- Response times
- Consecutive failure counts

### Prometheus Integration

```go
// Expose Prometheus metrics
prometheusMetrics := circuitbreaker.NewPrometheusMetrics(manager)
http.HandleFunc("/metrics", func(w http.ResponseWriter, r *http.Request) {
    w.Header().Set("Content-Type", "text/plain")
    w.Write([]byte(prometheusMetrics.GetMetrics()))
})
```

### Custom Metrics Collector

```go
type CustomMetricsCollector struct {
    // Your metrics implementation
}

func (c *CustomMetricsCollector) RecordRequest(name string, state circuitbreaker.State) {
    // Record request metric
}

func (c *CustomMetricsCollector) RecordSuccess(name string, duration time.Duration) {
    // Record success metric
}

func (c *CustomMetricsCollector) RecordFailure(name string, duration time.Duration, err error) {
    // Record failure metric
}

func (c *CustomMetricsCollector) RecordStateChange(name string, from, to circuitbreaker.State) {
    // Record state change metric
}
```

## Best Practices

### 1. Service-Specific Configuration

Configure circuit breakers based on service characteristics:

```go
// Fast, high-volume service (Redis)
"redis": {
    MaxRequests: 5,
    Timeout:     15 * time.Second,
    ReadyToTrip: func(counts Counts) bool {
        return counts.ConsecutiveFailures >= 3
    },
}

// Critical, slower service (Billing)
"billing": {
    MaxRequests: 3,
    Timeout:     60 * time.Second,
    ReadyToTrip: func(counts Counts) bool {
        return counts.ConsecutiveFailures >= 2
    },
}
```

### 2. Graceful Degradation

Always provide fallback mechanisms:

```go
err := manager.Execute("external_service", func() error {
    return externalService.Call()
})

if err == circuitbreaker.ErrOpenState {
    // Provide degraded functionality
    return provideFallbackResponse()
}
```

### 3. Monitoring and Alerting

Set up alerts for circuit breaker state changes:

```go
config.OnStateChange = func(name string, from, to circuitbreaker.State) {
    if to == circuitbreaker.StateOpen {
        alerting.SendAlert(fmt.Sprintf("Circuit breaker %s opened", name))
    }
}
```

### 4. Testing

Test circuit breaker behavior:

```go
func TestCircuitBreakerIntegration(t *testing.T) {
    manager := setupTestManager()
    
    // Simulate failures
    for i := 0; i < 5; i++ {
        manager.Execute("test_service", func() error {
            return errors.New("simulated failure")
        })
    }
    
    // Verify circuit is open
    err := manager.Execute("test_service", func() error {
        return nil
    })
    assert.Equal(t, circuitbreaker.ErrOpenState, err)
}
```

## Performance Considerations

- Circuit breaker operations are thread-safe with minimal overhead
- State checks use read locks for high concurrency
- Metrics collection is optional and pluggable
- Memory usage scales with number of circuit breakers (typically < 1KB each)

## Troubleshooting

### Common Issues

1. **Circuit opens too frequently**: Adjust `ReadyToTrip` threshold
2. **Circuit doesn't open**: Check failure detection logic
3. **Slow recovery**: Adjust `Timeout` and `MaxRequests` values
4. **Memory leaks**: Ensure `manager.Close()` is called

### Debug Mode

Enable detailed logging:

```go
logger.SetLevel(logrus.DebugLevel)
```

### Manual Reset

Reset circuit breakers via HTTP API:

```bash
curl -X POST http://localhost:8080/circuit-breakers/redis/reset
```
