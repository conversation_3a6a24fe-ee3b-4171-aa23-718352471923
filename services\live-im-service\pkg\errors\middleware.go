/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package errors

import (
	"context"
	"encoding/json"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

// HTTPErrorResponse represents an HTTP error response
type HTTPErrorResponse struct {
	Success   bool                   `json:"success"`
	Error     *ErrorDetails          `json:"error"`
	Timestamp string                 `json:"timestamp"`
	RequestID string                 `json:"request_id,omitempty"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// ErrorDetails contains error information for API responses
type ErrorDetails struct {
	Code        ErrorCode `json:"code"`
	Message     string    `json:"message"`
	UserMessage string    `json:"user_message"`
	Field       string    `json:"field,omitempty"`
	Retryable   bool      `json:"retryable"`
}

// WebSocketErrorMessage represents a WebSocket error message
type WebSocketErrorMessage struct {
	Type        string                 `json:"type"`
	MessageID   string                 `json:"message_id"`
	Error       *ErrorDetails          `json:"error"`
	Timestamp   string                 `json:"timestamp"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
}

// HTTPErrorMiddleware provides HTTP error handling middleware
type HTTPErrorMiddleware struct {
	handler ErrorHandler
	logger  *logrus.Logger
}

// NewHTTPErrorMiddleware creates a new HTTP error middleware
func NewHTTPErrorMiddleware(handler ErrorHandler, logger *logrus.Logger) *HTTPErrorMiddleware {
	return &HTTPErrorMiddleware{
		handler: handler,
		logger:  logger,
	}
}

// GinErrorHandler returns a Gin middleware for error handling
func (m *HTTPErrorMiddleware) GinErrorHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			if err := m.handler.RecoverFromPanic(c.Request.Context(), "http_request"); err != nil {
				m.writeErrorResponse(c, err)
				c.Abort()
			}
		}()

		c.Next()

		// Handle errors that were set during request processing
		if len(c.Errors) > 0 {
			err := c.Errors.Last().Err
			liveErr := m.handler.HandleError(c.Request.Context(), err, "http_request")
			m.writeErrorResponse(c, liveErr)
		}
	}
}

// HandleHTTPError handles an error and writes an appropriate HTTP response
func (m *HTTPErrorMiddleware) HandleHTTPError(c *gin.Context, err error, operation string) {
	if err == nil {
		return
	}

	liveErr := m.handler.HandleError(c.Request.Context(), err, operation)
	m.writeErrorResponse(c, liveErr)
}

// writeErrorResponse writes an error response to the HTTP client
func (m *HTTPErrorMiddleware) writeErrorResponse(c *gin.Context, err *LiveIMError) {
	if err == nil {
		return
	}

	response := HTTPErrorResponse{
		Success:   false,
		Timestamp: time.Now().Format(time.RFC3339),
		Error: &ErrorDetails{
			Code:        err.Code,
			Message:     err.Message,
			UserMessage: err.GetUserMessage(),
			Field:       err.Field,
			Retryable:   err.Retryable,
		},
	}

	// Add request ID if available
	if requestID := c.GetString("request_id"); requestID != "" {
		response.RequestID = requestID
	}

	// Add safe metadata (exclude sensitive information)
	if err.Metadata != nil {
		response.Metadata = m.sanitizeMetadata(err.Metadata)
	}

	c.JSON(err.GetHTTPStatus(), response)
}

// WebSocketErrorHandler provides WebSocket error handling
type WebSocketErrorHandler struct {
	handler ErrorHandler
	logger  *logrus.Logger
}

// NewWebSocketErrorHandler creates a new WebSocket error handler
func NewWebSocketErrorHandler(handler ErrorHandler, logger *logrus.Logger) *WebSocketErrorHandler {
	return &WebSocketErrorHandler{
		handler: handler,
		logger:  logger,
	}
}

// HandleWebSocketError handles an error and sends an appropriate WebSocket message
func (w *WebSocketErrorHandler) HandleWebSocketError(ctx context.Context, conn *websocket.Conn, err error, operation string, messageID string) {
	if err == nil {
		return
	}

	liveErr := w.handler.HandleError(ctx, err, operation)
	w.sendErrorMessage(ctx, conn, liveErr, messageID)
}

// SendErrorMessage sends an error message via WebSocket
func (w *WebSocketErrorHandler) SendErrorMessage(ctx context.Context, conn *websocket.Conn, err *LiveIMError, messageID string) {
	w.sendErrorMessage(ctx, conn, err, messageID)
}

// sendErrorMessage sends an error message to the WebSocket client
func (w *WebSocketErrorHandler) sendErrorMessage(ctx context.Context, conn *websocket.Conn, err *LiveIMError, messageID string) {
	if err == nil || conn == nil {
		return
	}

	errorMsg := WebSocketErrorMessage{
		Type:      "error",
		MessageID: messageID,
		Timestamp: time.Now().Format(time.RFC3339),
		Error: &ErrorDetails{
			Code:        err.Code,
			Message:     err.Message,
			UserMessage: err.GetUserMessage(),
			Field:       err.Field,
			Retryable:   err.Retryable,
		},
	}

	// Add safe metadata
	if err.Metadata != nil {
		errorMsg.Metadata = w.sanitizeMetadata(err.Metadata)
	}

	// Send error message
	if data, marshalErr := json.Marshal(errorMsg); marshalErr == nil {
		if writeErr := conn.WriteMessage(websocket.TextMessage, data); writeErr != nil {
			w.logger.WithFields(logrus.Fields{
				"error":      writeErr.Error(),
				"message_id": messageID,
				"operation":  "send_websocket_error",
			}).Error("Failed to send WebSocket error message")
		}
	} else {
		w.logger.WithFields(logrus.Fields{
			"error":      marshalErr.Error(),
			"message_id": messageID,
			"operation":  "marshal_websocket_error",
		}).Error("Failed to marshal WebSocket error message")
	}
}

// HandleWithRecovery wraps a WebSocket operation with panic recovery
func (w *WebSocketErrorHandler) HandleWithRecovery(ctx context.Context, conn *websocket.Conn, operation string, messageID string, fn func() error) {
	defer func() {
		if err := w.handler.RecoverFromPanic(ctx, operation); err != nil {
			w.sendErrorMessage(ctx, conn, err, messageID)
		}
	}()

	if err := fn(); err != nil {
		w.HandleWebSocketError(ctx, conn, err, operation, messageID)
	}
}

// sanitizeMetadata removes sensitive information from metadata
func (m *HTTPErrorMiddleware) sanitizeMetadata(metadata map[string]interface{}) map[string]interface{} {
	safe := make(map[string]interface{})
	
	// List of safe metadata keys that can be exposed to clients
	safeKeys := map[string]bool{
		"retry_after_seconds": true,
		"max_capacity":        true,
		"content_length":      true,
		"max_length":          true,
		"limit":               true,
		"current":             true,
		"resource":            true,
		"action":              true,
		"room_id":             true,
		"message_type":        true,
	}

	for key, value := range metadata {
		if safeKeys[key] {
			safe[key] = value
		}
	}

	return safe
}

// sanitizeMetadata removes sensitive information from metadata (WebSocket version)
func (w *WebSocketErrorHandler) sanitizeMetadata(metadata map[string]interface{}) map[string]interface{} {
	safe := make(map[string]interface{})
	
	// List of safe metadata keys that can be exposed to clients
	safeKeys := map[string]bool{
		"retry_after_seconds": true,
		"max_capacity":        true,
		"content_length":      true,
		"max_length":          true,
		"limit":               true,
		"current":             true,
		"resource":            true,
		"action":              true,
		"room_id":             true,
		"message_type":        true,
	}

	for key, value := range metadata {
		if safeKeys[key] {
			safe[key] = value
		}
	}

	return safe
}

// ErrorReporter provides error reporting functionality
type ErrorReporter struct {
	handler ErrorHandler
	logger  *logrus.Logger
}

// NewErrorReporter creates a new error reporter
func NewErrorReporter(handler ErrorHandler, logger *logrus.Logger) *ErrorReporter {
	return &ErrorReporter{
		handler: handler,
		logger:  logger,
	}
}

// ReportError reports an error for monitoring and alerting
func (r *ErrorReporter) ReportError(ctx context.Context, err error, operation string, severity string) {
	if err == nil {
		return
	}

	liveErr := r.handler.HandleError(ctx, err, operation)
	
	// Add severity to metadata
	if liveErr.Metadata == nil {
		liveErr.Metadata = make(map[string]interface{})
	}
	liveErr.Metadata["severity"] = severity
	liveErr.Metadata["reported_at"] = time.Now().Format(time.RFC3339)

	// Log with appropriate level based on severity
	fields := logrus.Fields{
		"operation": operation,
		"severity":  severity,
		"error":     liveErr.Error(),
		"code":      liveErr.Code,
		"retryable": liveErr.Retryable,
	}

	// Add context information
	if requestID := ctx.Value("request_id"); requestID != nil {
		fields["request_id"] = requestID
	}
	if userID := ctx.Value("user_id"); userID != nil {
		fields["user_id"] = userID
	}

	switch severity {
	case "critical":
		r.logger.WithFields(fields).Error("Critical error reported")
	case "high":
		r.logger.WithFields(fields).Error("High severity error reported")
	case "medium":
		r.logger.WithFields(fields).Warn("Medium severity error reported")
	case "low":
		r.logger.WithFields(fields).Info("Low severity error reported")
	default:
		r.logger.WithFields(fields).Error("Error reported")
	}

	// Here you could integrate with external monitoring services like:
	// - Sentry
	// - Datadog
	// - New Relic
	// - Custom alerting systems
}
