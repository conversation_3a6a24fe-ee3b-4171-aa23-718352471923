/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package benchmark

import (
	"testing"
	"time"

	"github.com/google/uuid"

	"cina.club/services/live-im-service/internal/domain/model"
)

// BenchmarkMessageCreation benchmarks message creation performance
func BenchmarkMessageCreation(b *testing.B) {
	user := &model.User{
		UserID:   uuid.New(),
		Username: "benchuser",
		Role:     model.RoleViewer,
		Level:    5,
		VIPLevel: 2,
		Badges:   []string{"active", "supporter"},
	}

	b.<PERSON>("BarrageMessage", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = model.NewBarrageMessage("Benchmark message content", "room-123", user)
		}
	})

	b.Run("LikeMessage", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = model.NewLikeMessage(1, "room-123", user)
		}
	})

	b.Run("GiftMessage", func(b *testing.B) {
		recipientUser := &model.User{
			UserID:   uuid.New(),
			Username: "recipient",
			Role:     model.RoleViewer,
		}
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = model.NewGiftMessage("rose", 3, "room-123", user, recipientUser)
		}
	})

	b.Run("ErrorMessage", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = model.NewErrorMessage("Benchmark error message")
		}
	})

	b.Run("PingMessage", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = model.NewPingMessage()
		}
	})

	b.Run("PongMessage", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = model.NewPongMessage()
		}
	})
}

// BenchmarkMessageCloning benchmarks message cloning performance
func BenchmarkMessageCloning(b *testing.B) {
	user := &model.User{
		UserID:   uuid.New(),
		Username: "benchuser",
		Role:     model.RoleViewer,
		Level:    5,
		VIPLevel: 2,
		Badges:   []string{"active", "supporter"},
	}

	// Create different types of messages to clone
	barrageMsg := model.NewBarrageMessage("Test message for cloning", "room-123", user)
	likeMsg := model.NewLikeMessage(5, "room-123", user)

	recipientUser := &model.User{
		UserID:   uuid.New(),
		Username: "recipient",
		Role:     model.RoleViewer,
	}
	giftMsg := model.NewGiftMessage("diamond", 10, "room-123", user, recipientUser)

	b.Run("BarrageMessageClone", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = barrageMsg.Clone()
		}
	})

	b.Run("LikeMessageClone", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = likeMsg.Clone()
		}
	})

	b.Run("GiftMessageClone", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = giftMsg.Clone()
		}
	})
}

// BenchmarkUserOperations benchmarks user-related operations
func BenchmarkUserOperations(b *testing.B) {
	user := &model.User{
		UserID:   uuid.New(),
		Username: "benchuser",
		Role:     model.RoleViewer,
		Level:    5,
		VIPLevel: 2,
		Badges:   []string{"active", "supporter"},
	}

	b.Run("AddBadge", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			// Create a copy to avoid modifying the original
			testUser := *user
			testUser.Badges = make([]string, len(user.Badges))
			copy(testUser.Badges, user.Badges)
			testUser.AddBadge("newbadge")
		}
	})

	b.Run("HasBadge", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = user.HasBadge("supporter")
		}
	})

	b.Run("HasBadgeNotFound", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = user.HasBadge("nonexistent")
		}
	})
}

// BenchmarkRoomOperations benchmarks room-related operations
func BenchmarkRoomOperations(b *testing.B) {
	streamerID := uuid.New()
	streamer := &model.User{
		UserID:   streamerID,
		Username: "streamer",
		Role:     model.RoleStreamer,
	}

	b.Run("RoomCreation", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			roomID := "bench-room-" + uuid.New().String()
			_ = model.NewRoom(roomID, "Benchmark Room", streamerID, streamer)
		}
	})

	b.Run("RoomLikeOperations", func(b *testing.B) {
		room := model.NewRoom("bench-room", "Benchmark Room", streamerID, streamer)
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			room.AddLike(1)
		}
	})

	b.Run("RoomGetInfo", func(b *testing.B) {
		room := model.NewRoom("bench-room", "Benchmark Room", streamerID, streamer)
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = room.GetInfo()
		}
	})
}

// BenchmarkClientOperations benchmarks client-related operations
func BenchmarkClientOperations(b *testing.B) {
	userID := uuid.New()

	b.Run("ClientCreation", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			// Simulate client creation
			_ = &model.Client{
				ID:          uuid.New().String(),
				UserID:      userID,
				ConnectedAt: time.Now(),
			}
		}
	})
}

// BenchmarkConcurrentMessageCreation benchmarks concurrent message creation
func BenchmarkConcurrentMessageCreation(b *testing.B) {
	user := &model.User{
		UserID:   uuid.New(),
		Username: "benchuser",
		Role:     model.RoleViewer,
		Level:    5,
		VIPLevel: 2,
		Badges:   []string{"active", "supporter"},
	}

	b.Run("ConcurrentBarrageMessages", func(b *testing.B) {
		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				_ = model.NewBarrageMessage("Concurrent benchmark message", "room-123", user)
			}
		})
	})

	b.Run("ConcurrentLikeMessages", func(b *testing.B) {
		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				_ = model.NewLikeMessage(1, "room-123", user)
			}
		})
	})
}

// BenchmarkMessageValidation benchmarks message validation
func BenchmarkMessageValidation(b *testing.B) {
	user := &model.User{
		UserID:   uuid.New(),
		Username: "benchuser",
		Role:     model.RoleViewer,
	}

	validMsg := model.NewBarrageMessage("Valid message", "room-123", user)
	invalidMsg := model.Message{
		Type:      model.MessageTypeBarrage,
		MessageID: "",
		Content:   "",
		Timestamp: time.Time{},
	}

	b.Run("ValidMessageValidation", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			// Simple validation check
			_ = validMsg.MessageID != "" && validMsg.Content != "" && !validMsg.Timestamp.IsZero()
		}
	})

	b.Run("InvalidMessageValidation", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			// Simple validation check
			_ = invalidMsg.MessageID != "" && invalidMsg.Content != "" && !invalidMsg.Timestamp.IsZero()
		}
	})
}

// BenchmarkUUIDGeneration benchmarks UUID generation performance
func BenchmarkUUIDGeneration(b *testing.B) {
	b.Run("UUIDNew", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = uuid.New()
		}
	})

	b.Run("UUIDString", func(b *testing.B) {
		id := uuid.New()
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = id.String()
		}
	})
}
