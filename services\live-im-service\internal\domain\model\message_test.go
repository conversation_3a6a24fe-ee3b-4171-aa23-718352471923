/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:12:13
Modified: 2025-07-11 10:12:13
*/

package model

import (
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewAuthMessage(t *testing.T) {
	token := "test-token"
	roomID := "test-room"

	msg := NewAuthMessage(token, roomID)

	assert.NotEmpty(t, msg.MessageID)
	assert.Equal(t, MessageTypeAuth, msg.Type)
	assert.Equal(t, token, msg.Token)
	assert.Equal(t, roomID, msg.RoomID)
	assert.WithinDuration(t, time.Now(), msg.Timestamp, time.Second)
}

func TestNewAuthResultMessage(t *testing.T) {
	t.Run("Success", func(t *testing.T) {
		roomInfo := &RoomInfo{
			RoomID: "test-room",
			Title:  "Test Room",
		}
		users := []*User{
			{Username: "user1"},
			{Username: "user2"},
		}

		msg := NewAuthResultMessage(true, roomInfo, users, "")

		assert.NotEmpty(t, msg.MessageID)
		assert.Equal(t, MessageTypeAuthResult, msg.Type)
		assert.True(t, msg.Success)
		assert.Equal(t, roomInfo, msg.RoomInfo)
		assert.Equal(t, users, msg.OnlineList)
		assert.Empty(t, msg.ErrorMsg)
	})

	t.Run("Failure", func(t *testing.T) {
		errorMsg := "Authentication failed"

		msg := NewAuthResultMessage(false, nil, nil, errorMsg)

		assert.NotEmpty(t, msg.MessageID)
		assert.Equal(t, MessageTypeAuthResult, msg.Type)
		assert.False(t, msg.Success)
		assert.Nil(t, msg.RoomInfo)
		assert.Nil(t, msg.OnlineList)
		assert.Equal(t, errorMsg, msg.ErrorMsg)
	})
}

func TestNewBarrageMessage(t *testing.T) {
	content := "Hello, world!"
	roomID := "test-room"
	user := &User{
		UserID:   uuid.New(),
		Username: "testuser",
		Avatar:   "avatar.jpg",
		Role:     RoleViewer,
	}

	msg := NewBarrageMessage(content, roomID, user)

	assert.NotEmpty(t, msg.MessageID)
	assert.Equal(t, MessageTypeBarrage, msg.Type)
	assert.Equal(t, content, msg.Content)
	assert.Equal(t, roomID, msg.RoomID)
	assert.Equal(t, user, msg.FromUser)
	assert.WithinDuration(t, time.Now(), msg.Timestamp, time.Second)
}

func TestNewNewBarrageMessage(t *testing.T) {
	content := "Hello, world!"
	roomID := "test-room"
	user := &User{
		Username: "testuser",
		Avatar:   "avatar.jpg",
		Role:     RoleViewer,
	}

	msg := NewNewBarrageMessage(content, roomID, user)

	assert.NotEmpty(t, msg.MessageID)
	assert.Equal(t, MessageTypeNewBarrage, msg.Type)
	assert.Equal(t, content, msg.Content)
	assert.Equal(t, roomID, msg.RoomID)
	assert.WithinDuration(t, time.Now(), msg.Timestamp, time.Second)
}

func TestNewLikeMessage(t *testing.T) {
	count := 5
	roomID := "test-room"
	user := &User{
		UserID:   uuid.New(),
		Username: "testuser",
		Avatar:   "avatar.jpg",
		Role:     RoleViewer,
	}

	msg := NewLikeMessage(count, roomID, user)

	assert.NotEmpty(t, msg.MessageID)
	assert.Equal(t, MessageTypeLike, msg.Type)
	assert.Equal(t, count, msg.Count)
	assert.Equal(t, roomID, msg.RoomID)
	assert.Equal(t, user, msg.FromUser)
	assert.WithinDuration(t, time.Now(), msg.Timestamp, time.Second)
}

func TestNewLikeBurstMessage(t *testing.T) {
	count := 100
	roomID := "test-room"

	msg := NewLikeBurstMessage(count, roomID)

	assert.NotEmpty(t, msg.MessageID)
	assert.Equal(t, MessageTypeLikeBurst, msg.Type)
	assert.Equal(t, count, msg.Count)
	assert.Equal(t, roomID, msg.RoomID)
	assert.WithinDuration(t, time.Now(), msg.Timestamp, time.Second)
}

func TestNewGiftMessage(t *testing.T) {
	giftID := "gift-001"
	count := 3
	roomID := "test-room"
	fromUser := &User{
		UserID:   uuid.New(),
		Username: "sender",
		Avatar:   "sender.jpg",
		Role:     RoleViewer,
	}
	toUser := &User{
		UserID:   uuid.New(),
		Username: "receiver",
		Avatar:   "receiver.jpg",
		Role:     RoleViewer,
	}

	msg := NewGiftMessage(giftID, count, roomID, fromUser, toUser)

	assert.NotEmpty(t, msg.MessageID)
	assert.Equal(t, MessageTypeGift, msg.Type)
	assert.Equal(t, giftID, msg.GiftID)
	assert.Equal(t, count, msg.Count)
	assert.Equal(t, roomID, msg.RoomID)
	assert.Equal(t, fromUser, msg.FromUser)
	assert.Equal(t, toUser, msg.ToUser)
	assert.WithinDuration(t, time.Now(), msg.Timestamp, time.Second)
}

func TestNewPingMessage(t *testing.T) {
	msg := NewPingMessage()

	assert.NotEmpty(t, msg.MessageID)
	assert.Equal(t, MessageTypePing, msg.Type)
	assert.WithinDuration(t, time.Now(), msg.Timestamp, time.Second)
}

func TestNewPongMessage(t *testing.T) {
	msg := NewPongMessage()

	assert.NotEmpty(t, msg.MessageID)
	assert.Equal(t, MessageTypePong, msg.Type)
	assert.WithinDuration(t, time.Now(), msg.Timestamp, time.Second)
}

func TestNewErrorMessage(t *testing.T) {
	errorMsg := "Something went wrong"

	msg := NewErrorMessage(errorMsg)

	assert.NotEmpty(t, msg.MessageID)
	assert.Equal(t, MessageTypeError, msg.Type)
	assert.Equal(t, errorMsg, msg.ErrorMsg)
	assert.WithinDuration(t, time.Now(), msg.Timestamp, time.Second)
}

func TestNewRateLimitedMessage(t *testing.T) {
	msg := NewRateLimitedMessage()

	assert.NotEmpty(t, msg.MessageID)
	assert.Equal(t, MessageTypeRateLimited, msg.Type)
	assert.WithinDuration(t, time.Now(), msg.Timestamp, time.Second)
}

func TestNewAuthFailedMessage(t *testing.T) {
	reason := "Invalid token"

	msg := NewAuthFailedMessage(reason)

	assert.NotEmpty(t, msg.MessageID)
	assert.Equal(t, MessageTypeAuthResult, msg.Type)
	assert.False(t, msg.Success)
	assert.Equal(t, reason, msg.ErrorMessage)
	assert.WithinDuration(t, time.Now(), msg.Timestamp, time.Second)
}

func TestMessageValidation(t *testing.T) {
	t.Run("Valid message", func(t *testing.T) {
		msg := Message{
			MessageID: "test-id",
			Type:      MessageTypeBarrage,
			Content:   "Hello",
			RoomID:    "room-1",
			Timestamp: time.Now(),
		}

		assert.NotEmpty(t, msg.MessageID)
		assert.NotEmpty(t, msg.Type)
		assert.NotEmpty(t, msg.Content)
		assert.NotEmpty(t, msg.RoomID)
	})

	t.Run("Empty message ID", func(t *testing.T) {
		msg := Message{
			Type:      MessageTypeBarrage,
			Content:   "Hello",
			RoomID:    "room-1",
			Timestamp: time.Now(),
		}

		assert.Empty(t, msg.MessageID)
	})
}

func TestMessageSerialization(t *testing.T) {
	msg := NewBarrageMessage("Test content", "room-123")

	// Test that all required fields are set
	require.NotEmpty(t, msg.MessageID)
	require.Equal(t, MessageTypeBarrage, msg.Type)
	require.Equal(t, "Test content", msg.Content)
	require.Equal(t, "room-123", msg.RoomID)
	require.False(t, msg.Timestamp.IsZero())
}

func TestMessageTypes(t *testing.T) {
	tests := []struct {
		name        string
		messageType MessageType
		expected    string
	}{
		{"Auth", MessageTypeAuth, "auth"},
		{"AuthResult", MessageTypeAuthResult, "auth_result"},
		{"Barrage", MessageTypeBarrage, "barrage"},
		{"Like", MessageTypeLike, "like"},
		{"Gift", MessageTypeGift, "gift"},
		{"Ping", MessageTypePing, "ping"},
		{"Pong", MessageTypePong, "pong"},
		{"Error", MessageTypeError, "error"},
		{"RateLimited", MessageTypeRateLimited, "rate_limited"},
		{"NewBarrage", MessageTypeNewBarrage, "new_barrage"},
		{"LikeBurst", MessageTypeLikeBurst, "like_burst"},
		{"NewGift", MessageTypeNewGift, "new_gift"},
		{"UserJoin", MessageTypeUserJoin, "user_join"},
		{"UserLeave", MessageTypeUserLeave, "user_leave"},
		{"RoomUpdate", MessageTypeRoomUpdate, "room_update"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, string(tt.messageType))
		})
	}
}
