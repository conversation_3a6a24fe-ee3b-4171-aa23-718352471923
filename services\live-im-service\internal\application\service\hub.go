/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-27 13:00:00
Modified: 2025-06-27 13:00:00
*/

package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"cina.club/services/live-im-service/internal/application/port"
	"cina.club/services/live-im-service/internal/domain/model"
)

// Hub manages local connections - the "local post office".
// It is responsible for managing all WebSocket connections and room subscriptions on this instance.
type Hub struct {
	// Configuration
	config *HubConfig
	logger *logrus.Logger

	// Dependencies
	broadcaster      port.Broadcaster
	roomStore        port.RoomStore
	messageProcessor port.MessageProcessor
	rateLimiter      port.RateLimiter
	authService      port.AuthService

	// Local state
	clients           map[*model.Client]bool            // All client connections
	rooms             map[string]*model.Room            // Local room cache
	roomSubscriptions map[string]map[*model.Client]bool // Room subscription relationship map[roomID] -> clients
	userToClient      map[string]*model.Client          // Mapping from user ID to client

	// Control channels
	register   chan *model.Client      // Client registration
	unregister chan *model.Client      // Client unregistration
	broadcast  chan *BroadcastMessage  // Local broadcast
	message    chan *ClientMessage     // Client message
	roomUpdate chan *RoomUpdateMessage // Room update
	shutdown   chan struct{}           // Shutdown signal

	// Synchronization
	mu sync.RWMutex

	// Context control
	ctx    context.Context
	cancel context.CancelFunc

	// Statistics
	stats *HubStats

	// Background tasks
	ticker *time.Ticker
}

// HubConfig defines the Hub configuration.
type HubConfig struct {
	MaxClients            int           `json:"max_clients"`
	MaxRooms              int           `json:"max_rooms"`
	HeartbeatInterval     time.Duration `json:"heartbeat_interval"`
	CleanupInterval       time.Duration `json:"cleanup_interval"`
	StatsInterval         time.Duration `json:"stats_interval"`
	MessageBufferSize     int           `json:"message_buffer_size"`
	BroadcastBufferSize   int           `json:"broadcast_buffer_size"`
	LikeAggregateInterval time.Duration `json:"like_aggregate_interval"`
	MaxMessageSize        int           `json:"max_message_size"`
	EnableMetrics         bool          `json:"enable_metrics"`
	EnableRoomAnalytics   bool          `json:"enable_room_analytics"`
}

// HubStats defines Hub statistics.
type HubStats struct {
	StartTime           time.Time `json:"start_time"`
	ConnectedClients    int       `json:"connected_clients"`
	ActiveRooms         int       `json:"active_rooms"`
	TotalMessages       int64     `json:"total_messages"`
	TotalBroadcasts     int64     `json:"total_broadcasts"`
	MessagesPerSecond   float64   `json:"messages_per_second"`
	BroadcastsPerSecond float64   `json:"broadcasts_per_second"`
	LastStatsUpdate     time.Time `json:"last_stats_update"`

	// Memory usage
	MemoryUsage struct {
		Clients       int `json:"clients"`
		Rooms         int `json:"rooms"`
		Subscriptions int `json:"subscriptions"`
	} `json:"memory_usage"`
}

// BroadcastMessage represents a broadcast message.
type BroadcastMessage struct {
	RoomID        string
	Message       model.Message
	ExcludeClient *model.Client
}

// ClientMessage represents a client message.
type ClientMessage struct {
	Client  *model.Client
	Message model.Message
}

// RoomUpdateMessage represents a room update message.
type RoomUpdateMessage struct {
	Type   string
	RoomID string
	Data   interface{}
}

// NewHub creates a new Hub.
func NewHub(
	config *HubConfig,
	broadcaster port.Broadcaster,
	roomStore port.RoomStore,
	messageProcessor port.MessageProcessor,
	rateLimiter port.RateLimiter,
	authService port.AuthService,
	logger *logrus.Logger,
) *Hub {
	if config == nil {
		config = getDefaultHubConfig()
	}

	ctx, cancel := context.WithCancel(context.Background())

	hub := &Hub{
		config:           config,
		logger:           logger,
		broadcaster:      broadcaster,
		roomStore:        roomStore,
		messageProcessor: messageProcessor,
		rateLimiter:      rateLimiter,
		authService:      authService,

		clients:           make(map[*model.Client]bool),
		rooms:             make(map[string]*model.Room),
		roomSubscriptions: make(map[string]map[*model.Client]bool),
		userToClient:      make(map[string]*model.Client),

		register:   make(chan *model.Client, config.MessageBufferSize),
		unregister: make(chan *model.Client, config.MessageBufferSize),
		broadcast:  make(chan *BroadcastMessage, config.BroadcastBufferSize),
		message:    make(chan *ClientMessage, config.MessageBufferSize),
		roomUpdate: make(chan *RoomUpdateMessage, config.MessageBufferSize),
		shutdown:   make(chan struct{}),

		ctx:    ctx,
		cancel: cancel,

		stats: &HubStats{
			StartTime:       time.Now(),
			LastStatsUpdate: time.Now(),
		},

		ticker: time.NewTicker(config.HeartbeatInterval),
	}

	return hub
}

// Start starts the Hub.
func (h *Hub) Start() error {
	h.logger.Info("Starting Live IM Hub")

	// Start broadcast listener
	if err := h.broadcaster.Subscribe(h.ctx, h.handleBroadcastMessage); err != nil {
		return fmt.Errorf("failed to start broadcaster: %w", err)
	}

	// Start main event loop
	go h.run()

	// Start background tasks
	go h.runBackgroundTasks()

	h.logger.Info("Live IM Hub started successfully")
	return nil
}

// Stop stops the Hub.
func (h *Hub) Stop() error {
	h.logger.Info("Stopping Live IM Hub")

	// Send shutdown signal
	close(h.shutdown)

	// Cancel context
	h.cancel()

	// Stop ticker
	h.ticker.Stop()

	// Close all client connections
	h.mu.Lock()
	for client := range h.clients {
		client.Close()
	}
	h.mu.Unlock()

	// Stop broadcast listener
	if err := h.broadcaster.Unsubscribe(h.ctx); err != nil {
		h.logger.WithError(err).Error("Failed to stop broadcaster")
	}

	h.logger.Info("Live IM Hub stopped")
	return nil
}

// RegisterClient registers a client.
func (h *Hub) RegisterClient(client *model.Client) {
	select {
	case h.register <- client:
	case <-h.ctx.Done():
		h.logger.Warn("Hub is shutting down, cannot register client")
		client.Close()
	}
}

// UnregisterClient unregisters a client.
func (h *Hub) UnregisterClient(client *model.Client) {
	select {
	case h.unregister <- client:
	case <-h.ctx.Done():
		// Hub is already closed, clean up directly
		h.removeClientDirect(client)
	}
}

// BroadcastToRoom broadcasts a message to a room.
func (h *Hub) BroadcastToRoom(roomID string, message model.Message, excludeClient *model.Client) {
	broadcastMsg := &BroadcastMessage{
		RoomID:        roomID,
		Message:       message,
		ExcludeClient: excludeClient,
	}

	select {
	case h.broadcast <- broadcastMsg:
	case <-h.ctx.Done():
		h.logger.Warn("Hub is shutting down, cannot broadcast message")
	default:
		h.logger.Warn("Broadcast channel is full, dropping message")
	}
}

// SendMessage sends a message to the Hub for processing.
func (h *Hub) SendMessage(client *model.Client, message model.Message) {
	clientMsg := &ClientMessage{
		Client:  client,
		Message: message,
	}

	select {
	case h.message <- clientMsg:
	case <-h.ctx.Done():
		h.logger.Warn("Hub is shutting down, cannot process message")
	default:
		h.logger.Warn("Message channel is full, dropping message")
	}
}

// NotifyUserEvent notifies of a user event.
func (h *Hub) NotifyUserEvent(event model.UserEvent) {
	switch event.Type {
	case model.UserEventTypeJoin:
		h.handleUserJoin(event)
	case model.UserEventTypeLeave:
		h.handleUserLeave(event)
	}
}

// GetStats retrieves Hub statistics.
func (h *Hub) GetStats() *HubStats {
	h.mu.RLock()
	defer h.mu.RUnlock()

	stats := *h.stats
	stats.ConnectedClients = len(h.clients)
	stats.ActiveRooms = len(h.rooms)
	stats.MemoryUsage.Clients = len(h.clients)
	stats.MemoryUsage.Rooms = len(h.rooms)
	stats.MemoryUsage.Subscriptions = len(h.roomSubscriptions)

	return &stats
}

// GetRoomInfo retrieves room information.
func (h *Hub) GetRoomInfo(roomID string) (*model.Room, bool) {
	h.mu.RLock()
	defer h.mu.RUnlock()

	room, exists := h.rooms[roomID]
	return room, exists
}

// run is the main event loop.
func (h *Hub) run() {
	defer func() {
		if r := recover(); r != nil {
			h.logger.WithField("panic", r).Error("Hub event loop panic recovered")
		}
	}()

	for {
		select {
		case <-h.shutdown:
			h.logger.Info("Hub event loop shutting down")
			return

		case client := <-h.register:
			h.handleClientRegister(client)

		case client := <-h.unregister:
			h.handleClientUnregister(client)

		case broadcastMsg := <-h.broadcast:
			h.handleLocalBroadcast(broadcastMsg)

		case clientMsg := <-h.message:
			h.handleClientMessage(clientMsg)

		case roomUpdate := <-h.roomUpdate:
			h.handleRoomUpdate(roomUpdate)

		case <-h.ticker.C:
			h.handleTick()
		}
	}
}

// runBackgroundTasks runs background tasks.
func (h *Hub) runBackgroundTasks() {
	cleanupTicker := time.NewTicker(h.config.CleanupInterval)
	statsTicker := time.NewTicker(h.config.StatsInterval)
	likeAggregateTicker := time.NewTicker(h.config.LikeAggregateInterval)

	defer func() {
		cleanupTicker.Stop()
		statsTicker.Stop()
		likeAggregateTicker.Stop()
	}()

	for {
		select {
		case <-h.ctx.Done():
			return

		case <-cleanupTicker.C:
			h.cleanupInactiveConnections()

		case <-statsTicker.C:
			h.updateStats()

		case <-likeAggregateTicker.C:
			h.aggregateAndFlushLikes()
		}
	}
}

// handleClientRegister handles client registration.
func (h *Hub) handleClientRegister(client *model.Client) {
	h.mu.Lock()
	defer h.mu.Unlock()

	// Check if the maximum number of clients has been reached
	if len(h.clients) >= h.config.MaxClients {
		h.logger.Warn("Maximum clients reached, rejecting new connection")
		client.SendMessage(model.NewErrorMessageWithCode(
			model.ErrorCodeTooManyConnections,
			"Server is at maximum capacity",
		))
		client.Close()
		return
	}

	// Add to client map
	h.clients[client] = true
	h.userToClient[client.UserID.String()] = client

	h.logger.WithFields(logrus.Fields{
		"client_id":       client.ID,
		"user_id":         client.UserID,
		"username":        client.Username,
		"connected_count": len(h.clients),
	}).Info("Client registered")

	// Update statistics
	h.stats.ConnectedClients = len(h.clients)
}

// handleClientUnregister handles client unregistration.
func (h *Hub) handleClientUnregister(client *model.Client) {
	h.removeClientDirect(client)
}

// removeClientDirect removes a client directly.
func (h *Hub) removeClientDirect(client *model.Client) {
	h.mu.Lock()
	defer h.mu.Unlock()

	// Remove from client map
	delete(h.clients, client)
	delete(h.userToClient, client.UserID.String())

	// Remove from room subscriptions
	if client.RoomID != "" {
		if roomClients, exists := h.roomSubscriptions[client.RoomID]; exists {
			delete(roomClients, client)

			// If the room has no clients, remove the room
			if len(roomClients) == 0 {
				delete(h.roomSubscriptions, client.RoomID)
				delete(h.rooms, client.RoomID)
			} else {
				// Update room online count
				if room, exists := h.rooms[client.RoomID]; exists {
					room.RemoveClient(client.ID)
				}
			}
		}

		// Notify other users in the room
		h.broadcastUserLeave(client)
	}

	h.logger.WithFields(logrus.Fields{
		"client_id":       client.ID,
		"user_id":         client.UserID,
		"username":        client.Username,
		"room_id":         client.RoomID,
		"connected_count": len(h.clients),
	}).Info("Client unregistered")

	// Update statistics
	h.stats.ConnectedClients = len(h.clients)
}

// handleLocalBroadcast handles a local broadcast.
func (h *Hub) handleLocalBroadcast(broadcastMsg *BroadcastMessage) {
	h.mu.RLock()
	roomClients, exists := h.roomSubscriptions[broadcastMsg.RoomID]
	h.mu.RUnlock()

	if !exists {
		return
	}

	// Copy client list to avoid concurrent access issues
	clients := make([]*model.Client, 0, len(roomClients))
	h.mu.RLock()
	for client := range roomClients {
		if broadcastMsg.ExcludeClient == nil || client.ID != broadcastMsg.ExcludeClient.ID {
			clients = append(clients, client)
		}
	}
	h.mu.RUnlock()

	// Send message concurrently
	for _, client := range clients {
		go func(c *model.Client) {
			if err := c.SendMessage(broadcastMsg.Message); err != nil {
				h.logger.WithError(err).WithField("client_id", c.ID).Error("Failed to send message to client")
			}
		}(client)
	}

	// Update statistics
	h.stats.TotalBroadcasts++

	h.logger.WithFields(logrus.Fields{
		"room_id":      broadcastMsg.RoomID,
		"message_type": broadcastMsg.Message.Type,
		"client_count": len(clients),
	}).Debug("Message broadcasted locally")
}

// handleClientMessage handles a client message.
func (h *Hub) handleClientMessage(clientMsg *ClientMessage) {
	client := clientMsg.Client
	message := clientMsg.Message

	// Rate limit check
	if h.rateLimiter != nil {
		allowed, err := h.rateLimiter.Allow(h.ctx, client.UserID, "message")
		if err != nil {
			h.logger.WithError(err).Error("Rate limiter error")
		} else if !allowed {
			client.SendMessage(model.NewRateLimitedMessage())
			return
		}
	}

	// Process based on message type
	switch message.Type {
	case model.MessageTypeAuth:
		h.handleAuthMessage(client, message)
	case model.MessageTypeBarrage:
		h.handleBarrageMessage(client, message)
	case model.MessageTypeLike:
		h.handleLikeMessage(client, message)
	case model.MessageTypeGift:
		h.handleGiftMessage(client, message)
	case model.MessageTypePing:
		h.handlePingMessage(client, message)
	case model.MessageTypeJoinRoom:
		h.handleJoinRoomMessage(client, message)
	case model.MessageTypeLeaveRoom:
		h.handleLeaveRoomMessage(client, message)
	default:
		h.logger.WithField("message_type", message.Type).Warn("Unknown message type")
		client.SendMessage(model.NewErrorMessage("Unknown message type"))
	}

	// Update statistics
	h.stats.TotalMessages++
}

// handleAuthMessage handles an authentication message.
func (h *Hub) handleAuthMessage(client *model.Client, message model.Message) {
	// Call auth service to validate token
	userInfo, err := h.authService.ValidateToken(h.ctx, message.Token)
	if err != nil {
		h.logger.WithError(err).Error("Token validation failed")
		client.SendMessage(model.NewAuthFailedMessage("Token validation failed"))
		client.Close()
		return
	}

	// Update client information
	client.UserID = userInfo.UserID
	client.Username = userInfo.Username
	client.Avatar = userInfo.Avatar
	client.Role = userInfo.Role

	// Join room
	if err := h.joinRoom(client, message.RoomID); err != nil {
		client.SendMessage(model.NewErrorMessage(fmt.Sprintf("Failed to join room: %s", err.Error())))
		client.Close()
		return
	}

	// Send successful authentication response
	room := h.rooms[message.RoomID]
	onlineList := h.getOnlineList(message.RoomID)

	authResponse := model.NewAuthResultMessage(true, room.GetInfo(), onlineList, "")
	client.SendMessage(authResponse)

	h.logger.WithFields(logrus.Fields{
		"client_id": client.ID,
		"user_id":   client.UserID,
		"username":  client.Username,
		"room_id":   message.RoomID,
	}).Info("Client authenticated successfully")
}

// handleBarrageMessage handles a barrage message.
func (h *Hub) handleBarrageMessage(client *model.Client, message model.Message) {
	if client.RoomID == "" {
		client.SendMessage(model.NewErrorMessage("Not in any room"))
		return
	}

	// Check if the room allows barrages
	room, exists := h.rooms[client.RoomID]
	if !exists || !room.CanSendMessage() {
		client.SendMessage(model.NewErrorMessage("Cannot send message in this room"))
		return
	}

	// Process message
	if h.messageProcessor != nil {
		err := h.messageProcessor.ProcessBarrage(h.ctx, client, message)
		if err != nil {
			client.SendMessage(model.NewErrorMessage(err.Error()))
			return
		}
	}
}

// handleLikeMessage handles a like message.
func (h *Hub) handleLikeMessage(client *model.Client, message model.Message) {
	if client.RoomID == "" {
		client.SendMessage(model.NewErrorMessage("Not in any room"))
		return
	}

	// Add to room like aggregator
	if room, exists := h.rooms[client.RoomID]; exists {
		room.AddLike(message.Count)
	}
}

// handleGiftMessage handles a gift message.
func (h *Hub) handleGiftMessage(client *model.Client, message model.Message) {
	if client.RoomID == "" {
		client.SendMessage(model.NewErrorMessage("Not in any room"))
		return
	}

	// Check if the room allows gifts
	room, exists := h.rooms[client.RoomID]
	if !exists || !room.CanSendGift() {
		client.SendMessage(model.NewErrorMessage("Cannot send gift in this room"))
		return
	}

	// Process gift message (involves billing)
	if h.messageProcessor != nil {
		err := h.messageProcessor.ProcessGift(h.ctx, client, message)
		if err != nil {
			client.SendMessage(model.NewErrorMessage(err.Error()))
			return
		}
	}
}

// handlePingMessage handles a ping message.
func (h *Hub) handlePingMessage(client *model.Client, message model.Message) {
	pongMsg := model.NewPongMessage()
	client.SendMessage(pongMsg)
}

// handleJoinRoomMessage handles a join room message.
func (h *Hub) handleJoinRoomMessage(client *model.Client, message model.Message) {
	if err := h.joinRoom(client, message.RoomID); err != nil {
		client.SendMessage(model.NewErrorMessage(fmt.Sprintf("Failed to join room: %s", err.Error())))
		return
	}

	// Send room information
	room := h.rooms[message.RoomID]
	roomInfoMsg := model.NewRoomInfoMessage(room.GetInfo())
	client.SendMessage(roomInfoMsg)
}

// handleLeaveRoomMessage handles a leave room message.
func (h *Hub) handleLeaveRoomMessage(client *model.Client, message model.Message) {
	h.leaveRoom(client)
}

// handleRoomUpdate handles a room update.
func (h *Hub) handleRoomUpdate(update *RoomUpdateMessage) {
	// Process based on update type
	switch update.Type {
	case "settings":
		h.updateRoomSettings(update.RoomID, update.Data)
	case "info":
		h.updateRoomInfo(update.RoomID, update.Data)
	}
}

// handleTick handles a tick.
func (h *Hub) handleTick() {
	// Perform periodic maintenance tasks
	h.cleanupExpiredSessions()
	h.updateRoomAnalytics()
}

// handleBroadcastMessage handles a broadcast message from Redis.
func (h *Hub) handleBroadcastMessage(roomID string, message model.Message) error {
	broadcastMsg := &BroadcastMessage{
		RoomID:  roomID,
		Message: message,
	}

	select {
	case h.broadcast <- broadcastMsg:
	default:
		h.logger.Warn("Broadcast channel is full, dropping Redis message")
	}
	return nil
}

// joinRoom joins a room.
func (h *Hub) joinRoom(client *model.Client, roomID string) error {
	h.mu.Lock()
	defer h.mu.Unlock()

	// Leave current room
	if client.RoomID != "" {
		h.leaveRoomLocked(client)
	}

	// Get or create room
	room, exists := h.rooms[roomID]
	if !exists {
		// Create room (TODO: Get from live-api-service)
		streamerID := uuid.New()
		room = model.NewRoom(roomID, "Live Room", streamerID, nil)
		h.rooms[roomID] = room
	}

	// Add to room subscription
	if _, exists := h.roomSubscriptions[roomID]; !exists {
		h.roomSubscriptions[roomID] = make(map[*model.Client]bool)
	}
	h.roomSubscriptions[roomID][client] = true

	// Update client room ID
	client.RoomID = roomID

	// Add to room
	room.AddClient(client)

	// Store user-room relationship in Redis
	if err := h.roomStore.Subscribe(h.ctx, client.UserID, roomID); err != nil {
		h.logger.WithError(err).Error("Failed to store user room relation")
	}

	// Broadcast user enter event
	h.broadcastUserEnter(client)

	return nil
}

// leaveRoom leaves a room.
func (h *Hub) leaveRoom(client *model.Client) {
	h.mu.Lock()
	defer h.mu.Unlock()
	h.leaveRoomLocked(client)
}

// leaveRoomLocked leaves a room (locked version).
func (h *Hub) leaveRoomLocked(client *model.Client) {
	if client.RoomID == "" {
		return
	}

	roomID := client.RoomID

	// Remove from room subscription
	if roomClients, exists := h.roomSubscriptions[roomID]; exists {
		delete(roomClients, client)

		// If the room has no clients, remove the room
		if len(roomClients) == 0 {
			delete(h.roomSubscriptions, roomID)
			delete(h.rooms, roomID)
		} else {
			// Update room
			if room, exists := h.rooms[roomID]; exists {
				room.RemoveClient(client.ID)
			}
		}
	}

	// Remove user-room relationship from Redis
	if err := h.roomStore.Unsubscribe(h.ctx, client.UserID, roomID); err != nil {
		h.logger.WithError(err).Error("Failed to remove user room relation")
	}

	// Broadcast user leave event
	h.broadcastUserLeave(client)

	// Clear client room ID
	client.RoomID = ""
}

// Helper methods

// handleUserJoin handles a user join event.
func (h *Hub) handleUserJoin(event model.UserEvent) {
	userEnterMsg := model.NewUserEnterMessage(event.RoomID, &model.User{
		UserID:   event.UserID,
		Username: event.Username,
		Avatar:   event.Avatar,
	})

	h.BroadcastToRoom(event.RoomID, userEnterMsg, event.Client)
}

// handleUserLeave handles a user leave event.
func (h *Hub) handleUserLeave(event model.UserEvent) {
	userLeaveMsg := model.NewUserLeaveMessage(event.RoomID, &model.User{
		UserID:   event.UserID,
		Username: event.Username,
		Avatar:   event.Avatar,
	})

	h.BroadcastToRoom(event.RoomID, userLeaveMsg, event.Client)
}

// broadcastUserEnter broadcasts a user enter event.
func (h *Hub) broadcastUserEnter(client *model.Client) {
	userEnterMsg := model.NewUserEnterMessage(client.RoomID, &model.User{
		UserID:   client.UserID,
		Username: client.Username,
		Avatar:   client.Avatar,
		Role:     client.Role,
	})

	h.BroadcastToRoom(client.RoomID, userEnterMsg, client)
}

// broadcastUserLeave broadcasts a user leave event.
func (h *Hub) broadcastUserLeave(client *model.Client) {
	if client.RoomID == "" {
		return
	}

	userLeaveMsg := model.NewUserLeaveMessage(client.RoomID, &model.User{
		UserID:   client.UserID,
		Username: client.Username,
		Avatar:   client.Avatar,
		Role:     client.Role,
	})

	h.BroadcastToRoom(client.RoomID, userLeaveMsg, client)
}

// getOnlineList retrieves the online user list.
func (h *Hub) getOnlineList(roomID string) []*model.User {
	roomClients, exists := h.roomSubscriptions[roomID]
	if !exists {
		return []*model.User{}
	}

	users := make([]*model.User, 0, len(roomClients))
	for client := range roomClients {
		users = append(users, &model.User{
			UserID:   client.UserID,
			Username: client.Username,
			Avatar:   client.Avatar,
			Role:     client.Role,
		})
	}

	return users
}

// cleanupInactiveConnections cleans up inactive connections.
func (h *Hub) cleanupInactiveConnections() {
	h.mu.Lock()
	defer h.mu.Unlock()

	now := time.Now()
	inactiveClients := make([]*model.Client, 0)

	for client := range h.clients {
		if !client.IsActive || now.Sub(client.LastSeenAt) > time.Minute*5 {
			inactiveClients = append(inactiveClients, client)
		}
	}

	for _, client := range inactiveClients {
		h.logger.WithField("client_id", client.ID).Info("Cleaning up inactive client")
		client.Close()
	}
}

// updateStats updates statistics.
func (h *Hub) updateStats() {
	h.mu.Lock()
	defer h.mu.Unlock()

	now := time.Now()
	duration := now.Sub(h.stats.LastStatsUpdate).Seconds()

	if duration > 0 {
		h.stats.MessagesPerSecond = float64(h.stats.TotalMessages) / duration
		h.stats.BroadcastsPerSecond = float64(h.stats.TotalBroadcasts) / duration
	}

	h.stats.LastStatsUpdate = now
}

// aggregateAndFlushLikes aggregates and flushes likes.
func (h *Hub) aggregateAndFlushLikes() {
	h.mu.RLock()
	rooms := make([]*model.Room, 0, len(h.rooms))
	for _, room := range h.rooms {
		rooms = append(rooms, room)
	}
	h.mu.RUnlock()

	for _, room := range rooms {
		if likeCount := room.FlushLikes(); likeCount > 0 {
			likeBurstMsg := model.NewLikeBurstMessage(int(likeCount), room.ID)
			h.BroadcastToRoom(room.ID, likeBurstMsg, nil)
		}
	}
}

// cleanupExpiredSessions cleans up expired sessions.
func (h *Hub) cleanupExpiredSessions() {
	// Clean up expired room caches, etc.
}

// updateRoomAnalytics updates room analytics data.
func (h *Hub) updateRoomAnalytics() {
	if !h.config.EnableRoomAnalytics {
		return
	}

	h.mu.RLock()
	defer h.mu.RUnlock()

	for _, room := range h.rooms {
		room.GetAnalytics() // This will trigger an internal update
	}
}

// updateRoomSettings updates room settings.
func (h *Hub) updateRoomSettings(roomID string, data interface{}) {
	// Implement room settings update logic
}

// updateRoomInfo updates room information.
func (h *Hub) updateRoomInfo(roomID string, data interface{}) {
	// Implement room information update logic
}

// getDefaultHubConfig gets the default Hub configuration.
func getDefaultHubConfig() *HubConfig {
	return &HubConfig{
		MaxClients:            50000,
		MaxRooms:              1000,
		HeartbeatInterval:     30 * time.Second,
		CleanupInterval:       5 * time.Minute,
		StatsInterval:         1 * time.Minute,
		MessageBufferSize:     1000,
		BroadcastBufferSize:   10000,
		LikeAggregateInterval: 1 * time.Second,
		MaxMessageSize:        512,
		EnableMetrics:         true,
		EnableRoomAnalytics:   true,
	}
}

// GetConnectedClientsCount gets the number of connected clients.
func (h *Hub) GetConnectedClientsCount() int {
	h.mu.RLock()
	defer h.mu.RUnlock()
	return len(h.clients)
}

// GetActiveRoomsCount gets the number of active rooms.
func (h *Hub) GetActiveRoomsCount() int {
	h.mu.RLock()
	defer h.mu.RUnlock()
	return len(h.rooms)
}

// GetRoomClientCount gets the number of clients in a room.
func (h *Hub) GetRoomClientCount(roomID string) int {
	h.mu.RLock()
	defer h.mu.RUnlock()

	if roomClients, exists := h.roomSubscriptions[roomID]; exists {
		return len(roomClients)
	}
	return 0
}
