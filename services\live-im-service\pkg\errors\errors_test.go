/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package errors

import (
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestLiveIMError_Creation(t *testing.T) {
	t.Run("NewLiveIMError", func(t *testing.T) {
		err := NewLiveIMError(ErrCodeClientNotFound, "Client not found")
		
		assert.Equal(t, ErrCodeClientNotFound, err.Code)
		assert.Equal(t, "Client not found", err.Message)
		assert.NotZero(t, err.Timestamp)
		assert.Equal(t, 404, err.GetHTTPStatus())
		assert.False(t, err.Retryable)
	})

	t.Run("NewLiveIMErrorWithDetails", func(t *testing.T) {
		err := NewLiveIMErrorWithDetails(ErrCodeRoomFull, "Room is full", "Max capacity: 100")
		
		assert.Equal(t, ErrCodeRoomFull, err.Code)
		assert.Equal(t, "Room is full", err.Message)
		assert.Equal(t, "Max capacity: 100", err.Details)
		assert.Equal(t, 409, err.GetHTTPStatus())
	})

	t.Run("NewLiveIMErrorWithCause", func(t *testing.T) {
		cause := errors.New("underlying error")
		err := NewLiveIMErrorWithCause(ErrCodeDatabaseError, "Database failed", cause)
		
		assert.Equal(t, ErrCodeDatabaseError, err.Code)
		assert.Equal(t, "Database failed", err.Message)
		assert.Equal(t, cause, err.Cause)
		assert.True(t, err.Retryable)
		assert.Equal(t, 503, err.GetHTTPStatus())
	})
}

func TestLiveIMError_Methods(t *testing.T) {
	t.Run("Error method", func(t *testing.T) {
		err := NewLiveIMErrorWithDetails(ErrCodeInvalidInput, "Invalid data", "Field 'email' is required")
		expected := "[INVALID_INPUT] Invalid data: Field 'email' is required"
		assert.Equal(t, expected, err.Error())
	})

	t.Run("WithMetadata", func(t *testing.T) {
		err := NewLiveIMError(ErrCodeClientNotFound, "Client not found")
		err.WithMetadata("client_id", "123").WithMetadata("user_id", "456")
		
		assert.Equal(t, "123", err.Metadata["client_id"])
		assert.Equal(t, "456", err.Metadata["user_id"])
	})

	t.Run("WithField", func(t *testing.T) {
		err := NewLiveIMError(ErrCodeValidationFailed, "Validation failed")
		err.WithField("email")
		
		assert.Equal(t, "email", err.Field)
	})

	t.Run("WithUserMessage", func(t *testing.T) {
		err := NewLiveIMError(ErrCodeInternalError, "Internal error")
		err.WithUserMessage("Something went wrong. Please try again.")
		
		assert.Equal(t, "Something went wrong. Please try again.", err.GetUserMessage())
	})
}

func TestLiveIMError_Unwrap(t *testing.T) {
	cause := errors.New("original error")
	err := NewLiveIMErrorWithCause(ErrCodeDatabaseError, "Database error", cause)
	
	assert.Equal(t, cause, err.Unwrap())
}

func TestLiveIMError_Is(t *testing.T) {
	err1 := NewLiveIMError(ErrCodeClientNotFound, "Client not found")
	err2 := NewLiveIMError(ErrCodeClientNotFound, "Different message")
	err3 := NewLiveIMError(ErrCodeRoomNotFound, "Room not found")
	
	assert.True(t, err1.Is(err2))
	assert.False(t, err1.Is(err3))
}

func TestWrapError(t *testing.T) {
	originalErr := errors.New("original error")
	wrappedErr := WrapError(originalErr, ErrCodeNetworkError, "Network operation failed")
	
	assert.Equal(t, ErrCodeNetworkError, wrappedErr.Code)
	assert.Equal(t, "Network operation failed", wrappedErr.Message)
	assert.Equal(t, "original error", wrappedErr.Details)
	assert.Equal(t, originalErr, wrappedErr.Cause)
}

func TestErrorHelpers(t *testing.T) {
	t.Run("IsLiveIMError", func(t *testing.T) {
		liveErr := NewLiveIMError(ErrCodeClientNotFound, "Client not found")
		stdErr := errors.New("standard error")
		
		assert.True(t, IsLiveIMError(liveErr))
		assert.False(t, IsLiveIMError(stdErr))
	})

	t.Run("GetLiveIMError", func(t *testing.T) {
		liveErr := NewLiveIMError(ErrCodeClientNotFound, "Client not found")
		stdErr := errors.New("standard error")
		
		assert.Equal(t, liveErr, GetLiveIMError(liveErr))
		assert.Nil(t, GetLiveIMError(stdErr))
	})

	t.Run("HasCode", func(t *testing.T) {
		err := NewLiveIMError(ErrCodeClientNotFound, "Client not found")
		
		assert.True(t, HasCode(err, ErrCodeClientNotFound))
		assert.False(t, HasCode(err, ErrCodeRoomNotFound))
	})

	t.Run("IsRetryable", func(t *testing.T) {
		retryableErr := NewLiveIMError(ErrCodeDatabaseError, "Database error")
		nonRetryableErr := NewLiveIMError(ErrCodeClientNotFound, "Client not found")
		
		assert.True(t, IsRetryable(retryableErr))
		assert.False(t, IsRetryable(nonRetryableErr))
	})
}

func TestHTTPStatusMapping(t *testing.T) {
	testCases := []struct {
		code           ErrorCode
		expectedStatus int
	}{
		{ErrCodeClientNotFound, 404},
		{ErrCodeUnauthorized, 401},
		{ErrCodePermissionDenied, 403},
		{ErrCodeInvalidInput, 400},
		{ErrCodeClientRateLimited, 429},
		{ErrCodeRoomFull, 409},
		{ErrCodeServiceUnavailable, 503},
		{ErrCodeInternalError, 500},
	}

	for _, tc := range testCases {
		t.Run(string(tc.code), func(t *testing.T) {
			err := NewLiveIMError(tc.code, "Test error")
			assert.Equal(t, tc.expectedStatus, err.GetHTTPStatus())
		})
	}
}

func TestRetryableMapping(t *testing.T) {
	retryableCodes := []ErrorCode{
		ErrCodeServiceUnavailable,
		ErrCodeDatabaseError,
		ErrCodeRedisError,
		ErrCodeNetworkError,
		ErrCodeTimeoutError,
		ErrCodeInternalError,
	}

	nonRetryableCodes := []ErrorCode{
		ErrCodeClientNotFound,
		ErrCodeUnauthorized,
		ErrCodePermissionDenied,
		ErrCodeInvalidInput,
		ErrCodeValidationFailed,
	}

	for _, code := range retryableCodes {
		t.Run("Retryable_"+string(code), func(t *testing.T) {
			err := NewLiveIMError(code, "Test error")
			assert.True(t, err.Retryable, "Expected %s to be retryable", code)
		})
	}

	for _, code := range nonRetryableCodes {
		t.Run("NonRetryable_"+string(code), func(t *testing.T) {
			err := NewLiveIMError(code, "Test error")
			assert.False(t, err.Retryable, "Expected %s to be non-retryable", code)
		})
	}
}

func TestUserMessageMapping(t *testing.T) {
	testCases := []struct {
		code            ErrorCode
		expectedMessage string
	}{
		{ErrCodeClientNotFound, "Connection not found. Please refresh and try again."},
		{ErrCodeUnauthorized, "Authentication required. Please log in."},
		{ErrCodeUserMuted, "You are currently muted and cannot send messages."},
		{ErrCodeRoomFull, "The room is currently full. Please try again later."},
		{ErrCodeContentTooLong, "Your message is too long. Please shorten it and try again."},
		{ErrCodeClientRateLimited, "You're sending messages too quickly. Please slow down."},
	}

	for _, tc := range testCases {
		t.Run(string(tc.code), func(t *testing.T) {
			err := NewLiveIMError(tc.code, "Test error")
			assert.Equal(t, tc.expectedMessage, err.GetUserMessage())
		})
	}
}

func TestCommonErrorConstructors(t *testing.T) {
	t.Run("ErrClientNotFound", func(t *testing.T) {
		err := ErrClientNotFound("client-123")
		
		assert.Equal(t, ErrCodeClientNotFound, err.Code)
		assert.Equal(t, "Client not found", err.Message)
		assert.Contains(t, err.Details, "client-123")
		assert.Equal(t, "client-123", err.Metadata["client_id"])
	})

	t.Run("ErrClientRateLimited", func(t *testing.T) {
		retryAfter := 30 * time.Second
		err := ErrClientRateLimited("client-123", "send_message", retryAfter)
		
		assert.Equal(t, ErrCodeClientRateLimited, err.Code)
		assert.Equal(t, "client-123", err.Metadata["client_id"])
		assert.Equal(t, "send_message", err.Metadata["action"])
		assert.Equal(t, 30, err.Metadata["retry_after_seconds"])
	})

	t.Run("ErrUserMuted", func(t *testing.T) {
		mutedUntil := time.Now().Add(1 * time.Hour)
		err := ErrUserMuted("user-123", &mutedUntil)
		
		assert.Equal(t, ErrCodeUserMuted, err.Code)
		assert.Equal(t, "user-123", err.Metadata["user_id"])
		assert.Contains(t, err.Metadata, "muted_until")
	})

	t.Run("ErrRoomFull", func(t *testing.T) {
		err := ErrRoomFull("room-123", 100)
		
		assert.Equal(t, ErrCodeRoomFull, err.Code)
		assert.Equal(t, "room-123", err.Metadata["room_id"])
		assert.Equal(t, 100, err.Metadata["max_capacity"])
	})

	t.Run("ErrContentTooLong", func(t *testing.T) {
		err := ErrContentTooLong(500, 200)
		
		assert.Equal(t, ErrCodeContentTooLong, err.Code)
		assert.Equal(t, 500, err.Metadata["content_length"])
		assert.Equal(t, 200, err.Metadata["max_length"])
	})

	t.Run("ErrInsufficientFunds", func(t *testing.T) {
		err := ErrInsufficientFunds("user-123", 100.50, 75.25)
		
		assert.Equal(t, ErrCodeInsufficientFunds, err.Code)
		assert.Equal(t, "user-123", err.Metadata["user_id"])
		assert.Equal(t, 100.50, err.Metadata["required_amount"])
		assert.Equal(t, 75.25, err.Metadata["available_amount"])
	})
}

func TestErrorChaining(t *testing.T) {
	// Test error wrapping and unwrapping
	originalErr := errors.New("database connection failed")
	wrappedErr := WrapError(originalErr, ErrCodeDatabaseError, "Failed to save user")
	
	// Test that we can unwrap to get the original error
	assert.Equal(t, originalErr, wrappedErr.Unwrap())
	
	// Test that errors.Is works with wrapped errors
	assert.True(t, errors.Is(wrappedErr, originalErr))
	
	// Test that our custom Is method works
	anotherDBErr := NewLiveIMError(ErrCodeDatabaseError, "Another DB error")
	assert.True(t, wrappedErr.Is(anotherDBErr))
}

func TestErrorSerialization(t *testing.T) {
	err := NewLiveIMErrorWithDetails(ErrCodeClientRateLimited, "Rate limited", "Too many requests")
	err.WithMetadata("client_id", "123").WithField("action")
	
	// Test that the error can be converted to string
	errStr := err.Error()
	assert.Contains(t, errStr, "RATE_LIMITED")
	assert.Contains(t, errStr, "Rate limited")
	assert.Contains(t, errStr, "Too many requests")
	
	// Test that metadata is preserved
	assert.Equal(t, "123", err.Metadata["client_id"])
	assert.Equal(t, "action", err.Field)
}

func BenchmarkErrorCreation(b *testing.B) {
	b.Run("NewLiveIMError", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_ = NewLiveIMError(ErrCodeClientNotFound, "Client not found")
		}
	})

	b.Run("ErrClientNotFound", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			_ = ErrClientNotFound("client-123")
		}
	})

	b.Run("WrapError", func(b *testing.B) {
		originalErr := errors.New("original error")
		for i := 0; i < b.N; i++ {
			_ = WrapError(originalErr, ErrCodeInternalError, "Wrapped error")
		}
	})
}
