/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00

This file demonstrates how to integrate rate limiting into the Live IM Service.
*/

package main

import (
	"context"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"cina.club/services/live-im-service/pkg/ratelimit"
)

// Application represents the main application with rate limiting
type Application struct {
	rateLimitManager ratelimit.Manager
	redisClient      *redis.Client
	logger           *logrus.Logger
}

// MockMetricsCollector implements the MetricsCollector interface
type MockMetricsCollector struct {
	logger *logrus.Logger
}

func (m *MockMetricsCollector) RecordRequest(action ratelimit.Action, tier ratelimit.Tier, allowed bool) {
	m.logger.WithFields(logrus.Fields{
		"action":  string(action),
		"tier":    string(tier),
		"allowed": allowed,
	}).Debug("Rate limit request recorded")
}

func (m *MockMetricsCollector) RecordBlock(action ratelimit.Action, tier ratelimit.Tier, duration time.Duration) {
	m.logger.WithFields(logrus.Fields{
		"action":   string(action),
		"tier":     string(tier),
		"duration": duration.String(),
	}).Warn("User blocked")
}

func (m *MockMetricsCollector) RecordUnblock(action ratelimit.Action, tier ratelimit.Tier) {
	m.logger.WithFields(logrus.Fields{
		"action": string(action),
		"tier":   string(tier),
	}).Info("User unblocked")
}

func (m *MockMetricsCollector) UpdateCurrentLimits(action ratelimit.Action, tier ratelimit.Tier, limit int, usage int) {
	m.logger.WithFields(logrus.Fields{
		"action": string(action),
		"tier":   string(tier),
		"limit":  limit,
		"usage":  usage,
	}).Debug("Rate limit usage updated")
}

func (m *MockMetricsCollector) RecordLatency(action ratelimit.Action, tier ratelimit.Tier, duration time.Duration) {
	if duration > 10*time.Millisecond {
		m.logger.WithFields(logrus.Fields{
			"action":   string(action),
			"tier":     string(tier),
			"duration": duration.String(),
		}).Warn("High rate limiting latency")
	}
}

// MockEventHandler implements the EventHandler interface
type MockEventHandler struct {
	logger *logrus.Logger
}

func (m *MockEventHandler) HandleEvent(ctx context.Context, event *ratelimit.Event) error {
	m.logger.WithFields(logrus.Fields{
		"type":    string(event.Type),
		"user_id": event.UserID.String(),
		"action":  string(event.Action),
		"tier":    string(event.Tier),
	}).Info("Rate limiting event")
	return nil
}

// NewApplication creates a new application with rate limiting
func NewApplication() *Application {
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	logger.SetFormatter(&logrus.JSONFormatter{})

	// Create Redis client
	redisClient := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       0,
	})

	// Create metrics collector and event handler
	metricsCollector := &MockMetricsCollector{logger: logger}
	eventHandler := &MockEventHandler{logger: logger}

	// Configure rate limiting
	config := ratelimit.ManagerConfig{
		Default: ratelimit.Config{
			Algorithm:     ratelimit.AlgorithmSlidingWindow,
			Enabled:       true,
			Limit:         100,
			Window:        time.Minute,
			BlockDuration: 5 * time.Minute,
		},
		Actions: map[ratelimit.Action]ratelimit.ActionConfig{
			ratelimit.ActionBarrage: {
				Base: ratelimit.Config{
					Algorithm:  ratelimit.AlgorithmTokenBucket,
					Enabled:    true,
					Limit:      5,
					Burst:      10,
					Window:     time.Minute,
					RefillRate: 10 * time.Second,
				},
				Tiers: map[ratelimit.Tier]ratelimit.TierConfig{
					ratelimit.TierAnonymous: {Multiplier: 0.5},
					ratelimit.TierBasic:     {Multiplier: 1.0},
					ratelimit.TierPremium:   {Multiplier: 2.0},
					ratelimit.TierVIP:       {Multiplier: 5.0},
					ratelimit.TierAdmin:     {Multiplier: 10.0},
				},
			},
			ratelimit.ActionLike: {
				Base: ratelimit.Config{
					Algorithm: ratelimit.AlgorithmSlidingWindow,
					Enabled:   true,
					Limit:     30,
					Window:    time.Minute,
				},
				Tiers: map[ratelimit.Tier]ratelimit.TierConfig{
					ratelimit.TierAnonymous: {Multiplier: 0.3},
					ratelimit.TierBasic:     {Multiplier: 1.0},
					ratelimit.TierPremium:   {Multiplier: 2.0},
				},
			},
			ratelimit.ActionGift: {
				Base: ratelimit.Config{
					Algorithm: ratelimit.AlgorithmSlidingWindow,
					Enabled:   true,
					Limit:     3,
					Window:    time.Minute,
				},
				Tiers: map[ratelimit.Tier]ratelimit.TierConfig{
					ratelimit.TierBasic:   {Multiplier: 1.0},
					ratelimit.TierPremium: {Multiplier: 2.0},
					ratelimit.TierVIP:     {Multiplier: 5.0},
				},
			},
			ratelimit.ActionAPI: {
				Base: ratelimit.Config{
					Algorithm: ratelimit.AlgorithmSlidingWindow,
					Enabled:   true,
					Limit:     1000,
					Window:    time.Minute,
				},
				Tiers: map[ratelimit.Tier]ratelimit.TierConfig{
					ratelimit.TierAnonymous: {Multiplier: 0.1},
					ratelimit.TierBasic:     {Multiplier: 1.0},
					ratelimit.TierPremium:   {Multiplier: 2.0},
					ratelimit.TierVIP:       {Multiplier: 5.0},
					ratelimit.TierAdmin:     {Multiplier: 10.0},
				},
			},
		},
		EnableMetrics:    true,
		EnableLogging:    true,
		KeyPrefix:        "live_im_ratelimit",
		CleanupInterval:  5 * time.Minute,
		CleanupBatchSize: 1000,
	}

	// Create rate limiting manager
	rateLimitManager := ratelimit.NewRateLimitManager(
		redisClient,
		config,
		logger,
		metricsCollector,
		eventHandler,
	)

	return &Application{
		rateLimitManager: rateLimitManager,
		redisClient:      redisClient,
		logger:           logger,
	}
}

// setupRoutes sets up HTTP routes with rate limiting
func (app *Application) setupRoutes() *gin.Engine {
	router := gin.New()
	router.Use(gin.Recovery())

	// Configure rate limiting middleware
	middlewareConfig := ratelimit.MiddlewareConfig{
		SkipPaths: []string{
			"/health",
			"/metrics",
			"/static",
		},
		SkipMethods:   []string{"OPTIONS"},
		DefaultAction: ratelimit.ActionAPI,
		DefaultTier:   ratelimit.TierBasic,
		Headers: ratelimit.HeaderConfig{
			IncludeHeaders:   true,
			LimitHeader:      "X-RateLimit-Limit",
			RemainingHeader:  "X-RateLimit-Remaining",
			ResetHeader:      "X-RateLimit-Reset",
			RetryAfterHeader: "Retry-After",
		},
	}

	// Create and apply rate limiting middleware
	rateLimitMiddleware := ratelimit.NewHTTPMiddleware(
		app.rateLimitManager,
		app.logger,
		middlewareConfig,
	)

	// Apply global rate limiting
	router.Use(rateLimitMiddleware.Middleware())

	// API routes
	api := router.Group("/api/v1")
	{
		// Barrage/chat endpoints with specific rate limiting
		api.POST("/barrage",
			rateLimitMiddleware.EndpointMiddleware(ratelimit.ActionBarrage, ratelimit.TierBasic),
			app.handleBarrage,
		)

		// Like endpoints
		api.POST("/like",
			rateLimitMiddleware.EndpointMiddleware(ratelimit.ActionLike, ratelimit.TierBasic),
			app.handleLike,
		)

		// Gift endpoints
		api.POST("/gift",
			rateLimitMiddleware.EndpointMiddleware(ratelimit.ActionGift, ratelimit.TierBasic),
			app.handleGift,
		)

		// General API endpoints (use default rate limiting)
		api.GET("/rooms", app.handleGetRooms)
		api.GET("/room/:id", app.handleGetRoom)

		// Rate limiting management endpoints
		api.GET("/ratelimit/stats/:user_id", app.handleGetUserStats)
		api.POST("/ratelimit/reset/:user_id/:action", app.handleResetUserLimit)
		api.POST("/ratelimit/block/:user_id/:action", app.handleBlockUser)
		api.POST("/ratelimit/unblock/:user_id/:action", app.handleUnblockUser)

		// Test endpoints for demonstrating rate limiting
		api.POST("/test/spam", app.handleTestSpam)
		api.GET("/test/load", app.handleTestLoad)
	}

	return router
}

// HTTP handlers

func (app *Application) handleBarrage(c *gin.Context) {
	var request struct {
		Message string `json:"message" binding:"required"`
		RoomID  string `json:"room_id" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Simulate barrage processing
	app.logger.WithFields(logrus.Fields{
		"user_id": c.GetString("user_id"),
		"room_id": request.RoomID,
		"message": request.Message,
	}).Info("Barrage message processed")

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Barrage sent successfully",
	})
}

func (app *Application) handleLike(c *gin.Context) {
	var request struct {
		TargetID   string `json:"target_id" binding:"required"`
		TargetType string `json:"target_type" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":      "success",
		"target_id":   request.TargetID,
		"target_type": request.TargetType,
	})
}

func (app *Application) handleGift(c *gin.Context) {
	var request struct {
		RecipientID string `json:"recipient_id" binding:"required"`
		GiftType    string `json:"gift_type" binding:"required"`
		Amount      int    `json:"amount" binding:"required"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":       "success",
		"recipient_id": request.RecipientID,
		"gift_type":    request.GiftType,
		"amount":       request.Amount,
	})
}

func (app *Application) handleGetRooms(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"rooms": []map[string]interface{}{
			{"id": "room1", "name": "General Chat", "users": 150},
			{"id": "room2", "name": "Gaming", "users": 89},
			{"id": "room3", "name": "Music", "users": 234},
		},
	})
}

func (app *Application) handleGetRoom(c *gin.Context) {
	roomID := c.Param("id")
	c.JSON(http.StatusOK, gin.H{
		"id":          roomID,
		"name":        "Room " + roomID,
		"description": "This is room " + roomID,
		"users":       42,
	})
}

func (app *Application) handleGetUserStats(c *gin.Context) {
	userIDStr := c.Param("user_id")
	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	stats, err := app.rateLimitManager.GetUserStats(c.Request.Context(), userID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"user_id": userID.String(),
		"stats":   stats,
	})
}

func (app *Application) handleResetUserLimit(c *gin.Context) {
	userIDStr := c.Param("user_id")
	actionStr := c.Param("action")

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	action := ratelimit.Action(actionStr)
	limiter := app.rateLimitManager.GetLimiter(action, ratelimit.TierBasic)

	err = limiter.Reset(c.Request.Context(), userID, action)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Rate limit reset successfully",
	})
}

func (app *Application) handleBlockUser(c *gin.Context) {
	userIDStr := c.Param("user_id")
	actionStr := c.Param("action")

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	var request struct {
		Duration string `json:"duration"`
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	duration, err := time.ParseDuration(request.Duration)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid duration format"})
		return
	}

	action := ratelimit.Action(actionStr)
	limiter := app.rateLimitManager.GetLimiter(action, ratelimit.TierBasic)

	err = limiter.Block(c.Request.Context(), userID, action, duration)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":   "success",
		"message":  "User blocked successfully",
		"duration": duration.String(),
	})
}

func (app *Application) handleUnblockUser(c *gin.Context) {
	userIDStr := c.Param("user_id")
	actionStr := c.Param("action")

	userID, err := uuid.Parse(userIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid user ID"})
		return
	}

	action := ratelimit.Action(actionStr)
	limiter := app.rateLimitManager.GetLimiter(action, ratelimit.TierBasic)

	err = limiter.Unblock(c.Request.Context(), userID, action)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "User unblocked successfully",
	})
}

func (app *Application) handleTestSpam(c *gin.Context) {
	userID := uuid.New()

	// Simulate spam by making many requests quickly
	results := make([]map[string]interface{}, 0)
	for i := 0; i < 20; i++ {
		result, err := app.rateLimitManager.Allow(
			c.Request.Context(),
			userID,
			ratelimit.ActionBarrage,
			ratelimit.TierBasic,
		)

		status := "allowed"
		if err != nil {
			status = "error"
		} else if !result.Allowed {
			status = "denied"
		}

		results = append(results, map[string]interface{}{
			"request": i + 1,
			"status":  status,
			"remaining": func() int {
				if result != nil {
					return result.Remaining
				}
				return 0
			}(),
		})

		time.Sleep(100 * time.Millisecond)
	}

	c.JSON(http.StatusOK, gin.H{
		"test":    "spam",
		"user_id": userID.String(),
		"results": results,
	})
}

func (app *Application) handleTestLoad(c *gin.Context) {
	// Simulate load testing
	userCount := 10
	requestsPerUser := 5

	results := make(map[string]interface{})
	for i := 0; i < userCount; i++ {
		userID := uuid.New()
		userResults := make([]string, 0)

		for j := 0; j < requestsPerUser; j++ {
			result, err := app.rateLimitManager.Allow(
				c.Request.Context(),
				userID,
				ratelimit.ActionAPI,
				ratelimit.TierBasic,
			)

			status := "allowed"
			if err != nil {
				status = "error"
			} else if !result.Allowed {
				status = "denied"
			}

			userResults = append(userResults, status)
		}

		results[userID.String()] = userResults
	}

	c.JSON(http.StatusOK, gin.H{
		"test":    "load",
		"users":   userCount,
		"requests_per_user": requestsPerUser,
		"results": results,
	})
}

// Run starts the application
func (app *Application) Run() error {
	router := app.setupRoutes()

	app.logger.Info("Live IM Service with Rate Limiting starting up")
	app.logger.Info("Available endpoints:")
	app.logger.Info("  POST /api/v1/barrage - Send barrage message (rate limited)")
	app.logger.Info("  POST /api/v1/like - Like content (rate limited)")
	app.logger.Info("  POST /api/v1/gift - Send gift (rate limited)")
	app.logger.Info("  GET /api/v1/rooms - Get rooms list")
	app.logger.Info("  GET /api/v1/ratelimit/stats/{user_id} - Get user rate limit stats")
	app.logger.Info("  POST /api/v1/test/spam - Test spam detection")
	app.logger.Info("  GET /api/v1/test/load - Test load handling")

	server := &http.Server{
		Addr:    ":8080",
		Handler: router,
	}

	return server.ListenAndServe()
}

// Shutdown gracefully shuts down the application
func (app *Application) Shutdown(ctx context.Context) error {
	app.logger.Info("Shutting down application")

	if err := app.rateLimitManager.Close(); err != nil {
		app.logger.WithError(err).Error("Failed to close rate limit manager")
	}

	if err := app.redisClient.Close(); err != nil {
		app.logger.WithError(err).Error("Failed to close Redis client")
	}

	app.logger.Info("Application shutdown complete")
	return nil
}

func main() {
	app := NewApplication()

	if err := app.Run(); err != nil && err != http.ErrServerClosed {
		log.Fatal("Failed to start server:", err)
	}
}
