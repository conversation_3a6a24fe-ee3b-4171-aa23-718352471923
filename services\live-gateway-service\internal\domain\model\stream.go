/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-27 13:00:00
Modified: 2025-06-27 13:00:00
*/

package model

import (
	"time"

	"cina.club/services/live-gateway-service/internal/domain/model/validation"
	"github.com/google/uuid"
)

// Stream represents a live stream.
type Stream struct {
	StreamKey  string         `json:"stream_key"`
	RoomID     uuid.UUID      `json:"room_id"`
	UserID     uuid.UUID      `json:"user_id"`
	AuthToken  string         `json:"auth_token"`
	ServerNode string         `json:"server_node"`
	Status     StreamStatus   `json:"status"`
	Protocol   StreamProtocol `json:"protocol"`
	Quality    StreamQuality  `json:"quality"`
	Stats      *StreamStats   `json:"stats,omitempty"`
	StartedAt  time.Time      `json:"started_at"`
	LastSeenAt time.Time      `json:"last_seen_at"`
	CreatedAt  time.Time      `json:"created_at"`
	ExpiresAt  time.Time      `json:"expires_at"`
}

// StreamStatus represents the status of a stream.
type StreamStatus string

const (
	StreamStatusIdle     StreamStatus = "idle"
	StreamStatusLive     StreamStatus = "live"
	StreamStatusError    StreamStatus = "error"
	StreamStatusStopped  StreamStatus = "stopped"
	StreamStatusActive   StreamStatus = "active"
	StreamStatusInactive StreamStatus = "inactive"
	StreamStatusPending  StreamStatus = "pending"
	StreamStatusBlocked  StreamStatus = "blocked"
	StreamStatusBanned   StreamStatus = "banned"
	StreamStatusArchived StreamStatus = "archived"
)

// StreamMapping represents a mapping between stream key and room/user.
type StreamMapping struct {
	StreamKey  string         `json:"stream_key"`
	RoomID     uuid.UUID      `json:"room_id"`
	UserID     uuid.UUID      `json:"user_id"`
	AuthToken  string         `json:"auth_token"`
	ServerNode string         `json:"server_node"`
	Protocol   StreamProtocol `json:"protocol"`
	Status     StreamStatus   `json:"status"`
	CreatedAt  time.Time      `json:"created_at"`
	ExpiresAt  time.Time      `json:"expires_at"`
}

// Validate validates the stream mapping
func (m *StreamMapping) Validate() error {
	var validations []*validation.ValidationError

	// Validate stream key
	validations = append(validations, validation.Required("stream_key", m.StreamKey))
	if m.StreamKey != "" {
		if len(m.StreamKey) > 255 {
			validations = append(validations, &validation.ValidationError{
				Field:   "stream_key",
				Message: "stream_key is too long (max 255 characters)",
			})
		}
	}

	// Validate auth token
	validations = append(validations, validation.Required("auth_token", m.AuthToken))

	// Validate UUIDs
	if m.RoomID == uuid.Nil {
		validations = append(validations, &validation.ValidationError{
			Field:   "room_id",
			Message: "room_id is required and must be a valid UUID",
		})
	}

	if m.UserID == uuid.Nil {
		validations = append(validations, &validation.ValidationError{
			Field:   "user_id",
			Message: "user_id is required and must be a valid UUID",
		})
	}

	// Validate expiration time
	if m.ExpiresAt.IsZero() {
		validations = append(validations, &validation.ValidationError{
			Field:   "expires_at",
			Message: "expires_at is required",
		})
	} else if m.ExpiresAt.Before(time.Now()) {
		validations = append(validations, &validation.ValidationError{
			Field:   "expires_at",
			Message: "expires_at cannot be in the past",
		})
	} else if m.ExpiresAt.After(time.Now().Add(time.Hour * 24 * 30)) { // Max 30 days
		validations = append(validations, &validation.ValidationError{
			Field:   "expires_at",
			Message: "expires_at cannot be more than 30 days in the future",
		})
	}

	return validation.ValidateAll(validations...)
}

// NewStreamKey generates a new stream key.
func NewStreamKey() (string, error) {
	return uuid.New().String(), nil
}

// NewAuthToken generates a new authentication token.
func NewAuthToken() (string, error) {
	return uuid.New().String(), nil
}
