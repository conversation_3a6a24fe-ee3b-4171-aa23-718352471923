# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-01-27 14:00:00

# Live IM Service Makefile

# Project information
SERVICE_NAME := live-im-service
VERSION := 1.0.0
DOCKER_IMAGE := cina-club/$(SERVICE_NAME)
DOCKER_TAG := $(VERSION)

# Go information
GO_VERSION := 1.22
GO_MODULE := github.com/cina-club/$(SERVICE_NAME)

# Build information
BUILD_TIME := $(shell date -u +"%Y-%m-%dT%H:%M:%SZ")
BUILD_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")
BUILD_BRANCH := $(shell git rev-parse --abbrev-ref HEAD 2>/dev/null || echo "unknown")

# Ldflags
LDFLAGS := -w -s \
	-X main.version=$(VERSION) \
	-X main.buildTime=$(BUILD_TIME) \
	-X main.buildCommit=$(BUILD_COMMIT) \
	-X main.buildBranch=$(BUILD_BRANCH)

# Directories
BIN_DIR := bin
CONFIGS_DIR := configs
SCRIPTS_DIR := scripts
DOCS_DIR := docs

# Default target
.DEFAULT_GOAL := help

# Help target
.PHONY: help
help: ## Show this help message
	@echo "Available targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-20s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Clean targets
.PHONY: clean
clean: ## Clean build artifacts
	@echo "Cleaning build artifacts..."
	@rm -rf $(BIN_DIR)
	@rm -f coverage.out
	@go clean -cache -testcache -modcache

.PHONY: clean-docker
clean-docker: ## Clean Docker images
	@echo "Cleaning Docker images..."
	@docker rmi $(DOCKER_IMAGE):$(DOCKER_TAG) 2>/dev/null || true
	@docker rmi $(DOCKER_IMAGE):latest 2>/dev/null || true
	@docker system prune -f

# Build targets
.PHONY: build
build: ## Build the service binary
	@echo "Building $(SERVICE_NAME)..."
	@mkdir -p $(BIN_DIR)
	@CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
		-ldflags="$(LDFLAGS)" \
		-o $(BIN_DIR)/$(SERVICE_NAME) \
		./cmd/server

.PHONY: build-local
build-local: ## Build for local development
	@echo "Building $(SERVICE_NAME) for local development..."
	@mkdir -p $(BIN_DIR)
	@go build \
		-ldflags="$(LDFLAGS)" \
		-o $(BIN_DIR)/$(SERVICE_NAME) \
		./cmd/server

.PHONY: build-race
build-race: ## Build with race detector
	@echo "Building $(SERVICE_NAME) with race detector..."
	@mkdir -p $(BIN_DIR)
	@go build \
		-race \
		-ldflags="$(LDFLAGS)" \
		-o $(BIN_DIR)/$(SERVICE_NAME)-race \
		./cmd/server

# Development targets
.PHONY: run
run: build-local ## Run the service locally
	@echo "Running $(SERVICE_NAME)..."
	@./$(BIN_DIR)/$(SERVICE_NAME)

.PHONY: run-dev
run-dev: ## Run with development configuration
	@echo "Running $(SERVICE_NAME) in development mode..."
	@LIVE_IM_LOGGING_LEVEL=debug \
		LIVE_IM_FEATURES_ENABLE_DEBUG_MODE=true \
		go run ./cmd/server

.PHONY: run-race
run-race: build-race ## Run with race detector
	@echo "Running $(SERVICE_NAME) with race detector..."
	@./$(BIN_DIR)/$(SERVICE_NAME)-race

# Test targets
.PHONY: test
test: ## Run tests
	@echo "Running tests..."
	@go test -v -race -coverprofile=coverage.out ./...

.PHONY: test-unit
test-unit: ## Run unit tests only
	@echo "Running unit tests..."
	@go test -v -race -short ./...

.PHONY: test-adapter
test-adapter: ## Run adapter layer tests
	@echo "Running adapter layer tests..."
	@go test -v -race -coverprofile=adapter_coverage.out ./internal/adapter/cache ./internal/adapter/client ./internal/adapter/redis ./internal/adapter/transport/websocket

.PHONY: test-integration
test-integration: ## Run integration tests
	@echo "Running integration tests..."
	@go test -v -race -tags=integration ./...

.PHONY: test-coverage
test-coverage: test ## Generate test coverage report
	@echo "Generating coverage report..."
	@go tool cover -html=coverage.out -o coverage.html
	@echo "Coverage report generated: coverage.html"

.PHONY: test-adapter-coverage
test-adapter-coverage: test-adapter ## Generate adapter layer coverage report
	@echo "Generating adapter layer coverage report..."
	@go tool cover -html=adapter_coverage.out -o adapter_coverage.html
	@echo "Adapter coverage report generated: adapter_coverage.html"

.PHONY: test-benchmark
test-benchmark: ## Run benchmark tests
	@echo "Running benchmark tests..."
	@go test -v -bench=. -benchmem ./...

.PHONY: test-watch
test-watch: ## Run tests in watch mode (requires air)
	@echo "Running tests in watch mode..."
	@air -c .air.test.toml

# Quality targets
.PHONY: fmt
fmt: ## Format code
	@echo "Formatting code..."
	@gofmt -s -w .
	@goimports -w .

.PHONY: lint
lint: ## Run linter
	@echo "Running linter..."
	@golangci-lint run ./...

.PHONY: vet
vet: ## Run go vet
	@echo "Running go vet..."
	@go vet ./...

.PHONY: mod-tidy
mod-tidy: ## Tidy go modules
	@echo "Tidying go modules..."
	@go mod tidy

.PHONY: mod-verify
mod-verify: ## Verify go modules
	@echo "Verifying go modules..."
	@go mod verify

.PHONY: security-check
security-check: ## Run security checks
	@echo "Running security checks..."
	@gosec -quiet ./...

# Docker targets
.PHONY: docker-build
docker-build: ## Build Docker image
	@echo "Building Docker image..."
	@docker build \
		--build-arg BUILD_TIME=$(BUILD_TIME) \
		--build-arg BUILD_COMMIT=$(BUILD_COMMIT) \
		--build-arg BUILD_BRANCH=$(BUILD_BRANCH) \
		-t $(DOCKER_IMAGE):$(DOCKER_TAG) \
		-t $(DOCKER_IMAGE):latest \
		.

.PHONY: docker-push
docker-push: docker-build ## Push Docker image
	@echo "Pushing Docker image..."
	@docker push $(DOCKER_IMAGE):$(DOCKER_TAG)
	@docker push $(DOCKER_IMAGE):latest

.PHONY: docker-run
docker-run: ## Run Docker container
	@echo "Running Docker container..."
	@docker run -d \
		--name $(SERVICE_NAME) \
		-p 8080:8080 \
		-p 8081:8081 \
		-p 9090:9090 \
		$(DOCKER_IMAGE):$(DOCKER_TAG)

.PHONY: docker-stop
docker-stop: ## Stop Docker container
	@echo "Stopping Docker container..."
	@docker stop $(SERVICE_NAME) 2>/dev/null || true
	@docker rm $(SERVICE_NAME) 2>/dev/null || true

.PHONY: docker-logs
docker-logs: ## Show Docker container logs
	@docker logs -f $(SERVICE_NAME)

# Development environment targets
.PHONY: dev-up
dev-up: ## Start development environment
	@echo "Starting development environment..."
	@docker-compose -f docker-compose.dev.yml up -d

.PHONY: dev-down
dev-down: ## Stop development environment
	@echo "Stopping development environment..."
	@docker-compose -f docker-compose.dev.yml down

.PHONY: dev-logs
dev-logs: ## Show development environment logs
	@docker-compose -f docker-compose.dev.yml logs -f

.PHONY: dev-restart
dev-restart: dev-down dev-up ## Restart development environment

# Database targets
.PHONY: redis-cli
redis-cli: ## Connect to Redis CLI
	@echo "Connecting to Redis..."
	@docker exec -it redis redis-cli

.PHONY: redis-monitor
redis-monitor: ## Monitor Redis commands
	@echo "Monitoring Redis commands..."
	@docker exec -it redis redis-cli monitor

# Load testing targets
.PHONY: load-test
load-test: ## Run load tests
	@echo "Running load tests..."
	@go run ./scripts/loadtest/main.go

.PHONY: stress-test
stress-test: ## Run stress tests
	@echo "Running stress tests..."
	@go run ./scripts/stress/main.go

# Generate targets
.PHONY: generate
generate: ## Generate code
	@echo "Generating code..."
	@go generate ./...

.PHONY: proto-gen
proto-gen: ## Generate protobuf code
	@echo "Generating protobuf code..."
	@protoc --go_out=. --go-grpc_out=. api/proto/*.proto

.PHONY: mock-gen
mock-gen: ## Generate mocks
	@echo "Generating mocks..."
	@mockgen -source=internal/application/port/broadcaster.go -destination=internal/adapter/mock/broadcaster_mock.go
	@mockgen -source=internal/application/port/room_store.go -destination=internal/adapter/mock/room_store_mock.go

# Documentation targets
.PHONY: docs
docs: ## Generate documentation
	@echo "Generating documentation..."
	@godoc -http=:6060

.PHONY: swagger
swagger: ## Generate Swagger documentation
	@echo "Generating Swagger documentation..."
	@swag init -g cmd/server/main.go

# Deployment targets
.PHONY: deploy-dev
deploy-dev: docker-push ## Deploy to development environment
	@echo "Deploying to development..."
	@kubectl apply -f k8s/dev/

.PHONY: deploy-staging
deploy-staging: docker-push ## Deploy to staging environment
	@echo "Deploying to staging..."
	@kubectl apply -f k8s/staging/

.PHONY: deploy-prod
deploy-prod: docker-push ## Deploy to production environment
	@echo "Deploying to production..."
	@kubectl apply -f k8s/prod/

# Monitoring targets
.PHONY: logs
logs: ## Show service logs
	@kubectl logs -f deployment/$(SERVICE_NAME)

.PHONY: port-forward
port-forward: ## Port forward to local
	@kubectl port-forward service/$(SERVICE_NAME) 8080:8080

.PHONY: top
top: ## Show resource usage
	@kubectl top pods -l app=$(SERVICE_NAME)

# Utility targets
.PHONY: version
version: ## Show version information
	@echo "Service: $(SERVICE_NAME)"
	@echo "Version: $(VERSION)"
	@echo "Build Time: $(BUILD_TIME)"
	@echo "Build Commit: $(BUILD_COMMIT)"
	@echo "Build Branch: $(BUILD_BRANCH)"

.PHONY: info
info: ## Show project information
	@echo "Project Information:"
	@echo "  Service Name: $(SERVICE_NAME)"
	@echo "  Version: $(VERSION)"
	@echo "  Go Version: $(GO_VERSION)"
	@echo "  Module: $(GO_MODULE)"
	@echo "  Docker Image: $(DOCKER_IMAGE):$(DOCKER_TAG)"

.PHONY: deps
deps: ## Install dependencies
	@echo "Installing dependencies..."
	@go mod download
	@go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	@go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest
	@go install golang.org/x/tools/cmd/goimports@latest
	@go install github.com/swaggo/swag/cmd/swag@latest
	@go install github.com/golang/mock/mockgen@latest

# All-in-one targets
.PHONY: check
check: fmt lint vet mod-verify security-check test ## Run all checks

.PHONY: ci
ci: mod-verify check test-coverage ## Run CI checks

.PHONY: release
release: clean check build docker-build ## Prepare release

.PHONY: all
all: clean deps check build docker-build ## Build everything 