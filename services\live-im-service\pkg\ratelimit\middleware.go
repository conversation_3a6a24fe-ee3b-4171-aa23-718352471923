/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package ratelimit

import (
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// HTTPMiddleware provides HTTP middleware for rate limiting
type HTTPMiddleware struct {
	manager Manager
	logger  *logrus.Logger
	config  MiddlewareConfig
}

// MiddlewareConfig configures the rate limiting middleware
type MiddlewareConfig struct {
	// SkipPaths are paths that should skip rate limiting
	SkipPaths []string
	
	// SkipMethods are HTTP methods that should skip rate limiting
	SkipMethods []string
	
	// DefaultAction is the default action to use for rate limiting
	DefaultAction Action
	
	// DefaultTier is the default tier to use for rate limiting
	DefaultTier Tier
	
	// UserIDExtractor extracts user ID from the request
	UserIDExtractor func(*gin.Context) (uuid.UUID, error)
	
	// TierExtractor extracts user tier from the request
	TierExtractor func(*gin.Context) Tier
	
	// ActionExtractor extracts action from the request
	ActionExtractor func(*gin.Context) Action
	
	// ErrorHandler handles rate limiting errors
	ErrorHandler func(*gin.Context, *Result, error)
	
	// SuccessHandler handles successful rate limit checks
	SuccessHandler func(*gin.Context, *Result)
	
	// Headers configures which headers to include in responses
	Headers HeaderConfig
}

// HeaderConfig configures rate limiting headers
type HeaderConfig struct {
	// IncludeHeaders indicates whether to include rate limiting headers
	IncludeHeaders bool
	
	// LimitHeader is the header name for the rate limit
	LimitHeader string
	
	// RemainingHeader is the header name for remaining requests
	RemainingHeader string
	
	// ResetHeader is the header name for reset time
	ResetHeader string
	
	// RetryAfterHeader is the header name for retry after
	RetryAfterHeader string
}

// NewHTTPMiddleware creates a new HTTP rate limiting middleware
func NewHTTPMiddleware(manager Manager, logger *logrus.Logger, config MiddlewareConfig) *HTTPMiddleware {
	// Set defaults
	if config.DefaultAction == "" {
		config.DefaultAction = ActionAPI
	}
	if config.DefaultTier == "" {
		config.DefaultTier = TierBasic
	}
	if config.UserIDExtractor == nil {
		config.UserIDExtractor = defaultUserIDExtractor
	}
	if config.TierExtractor == nil {
		config.TierExtractor = defaultTierExtractor
	}
	if config.ActionExtractor == nil {
		config.ActionExtractor = defaultActionExtractor
	}
	if config.ErrorHandler == nil {
		config.ErrorHandler = defaultErrorHandler
	}
	if config.SuccessHandler == nil {
		config.SuccessHandler = defaultSuccessHandler
	}
	
	// Set default headers
	if config.Headers.LimitHeader == "" {
		config.Headers.LimitHeader = "X-RateLimit-Limit"
	}
	if config.Headers.RemainingHeader == "" {
		config.Headers.RemainingHeader = "X-RateLimit-Remaining"
	}
	if config.Headers.ResetHeader == "" {
		config.Headers.ResetHeader = "X-RateLimit-Reset"
	}
	if config.Headers.RetryAfterHeader == "" {
		config.Headers.RetryAfterHeader = "Retry-After"
	}

	return &HTTPMiddleware{
		manager: manager,
		logger:  logger,
		config:  config,
	}
}

// Middleware returns a Gin middleware function
func (m *HTTPMiddleware) Middleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Check if path should be skipped
		if m.shouldSkipPath(c.Request.URL.Path) {
			c.Next()
			return
		}

		// Check if method should be skipped
		if m.shouldSkipMethod(c.Request.Method) {
			c.Next()
			return
		}

		// Extract user ID
		userID, err := m.config.UserIDExtractor(c)
		if err != nil {
			m.logger.WithError(err).Warn("Failed to extract user ID for rate limiting")
			// Continue without rate limiting if we can't identify the user
			c.Next()
			return
		}

		// Extract tier and action
		tier := m.config.TierExtractor(c)
		action := m.config.ActionExtractor(c)

		// Check rate limit
		result, err := m.manager.Allow(c.Request.Context(), userID, action, tier)
		if err != nil {
			m.logger.WithError(err).WithFields(logrus.Fields{
				"user_id": userID.String(),
				"action":  string(action),
				"tier":    string(tier),
				"path":    c.Request.URL.Path,
				"method":  c.Request.Method,
			}).Error("Rate limiting check failed")
			
			m.config.ErrorHandler(c, nil, err)
			return
		}

		// Add rate limiting headers
		if m.config.Headers.IncludeHeaders {
			m.addHeaders(c, result)
		}

		// Check if request is allowed
		if !result.Allowed {
			m.logger.WithFields(logrus.Fields{
				"user_id":   userID.String(),
				"action":    string(action),
				"tier":      string(tier),
				"path":      c.Request.URL.Path,
				"method":    c.Request.Method,
				"limit":     result.Limit,
				"remaining": result.Remaining,
				"blocked":   result.Blocked,
			}).Warn("Request rate limited")

			m.config.ErrorHandler(c, result, nil)
			return
		}

		// Request is allowed, call success handler and continue
		m.config.SuccessHandler(c, result)
		c.Next()
	}
}

// EndpointMiddleware creates middleware for specific endpoints with custom configuration
func (m *HTTPMiddleware) EndpointMiddleware(action Action, tier Tier) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Extract user ID
		userID, err := m.config.UserIDExtractor(c)
		if err != nil {
			m.logger.WithError(err).Warn("Failed to extract user ID for endpoint rate limiting")
			c.Next()
			return
		}

		// Check rate limit with specific action and tier
		result, err := m.manager.Allow(c.Request.Context(), userID, action, tier)
		if err != nil {
			m.logger.WithError(err).WithFields(logrus.Fields{
				"user_id": userID.String(),
				"action":  string(action),
				"tier":    string(tier),
				"path":    c.Request.URL.Path,
				"method":  c.Request.Method,
			}).Error("Endpoint rate limiting check failed")
			
			m.config.ErrorHandler(c, nil, err)
			return
		}

		// Add rate limiting headers
		if m.config.Headers.IncludeHeaders {
			m.addHeaders(c, result)
		}

		// Check if request is allowed
		if !result.Allowed {
			m.config.ErrorHandler(c, result, nil)
			return
		}

		// Request is allowed
		m.config.SuccessHandler(c, result)
		c.Next()
	}
}

// Helper methods

func (m *HTTPMiddleware) shouldSkipPath(path string) bool {
	for _, skipPath := range m.config.SkipPaths {
		if strings.HasPrefix(path, skipPath) {
			return true
		}
	}
	return false
}

func (m *HTTPMiddleware) shouldSkipMethod(method string) bool {
	for _, skipMethod := range m.config.SkipMethods {
		if strings.EqualFold(method, skipMethod) {
			return true
		}
	}
	return false
}

func (m *HTTPMiddleware) addHeaders(c *gin.Context, result *Result) {
	c.Header(m.config.Headers.LimitHeader, strconv.Itoa(result.Limit))
	c.Header(m.config.Headers.RemainingHeader, strconv.Itoa(result.Remaining))
	c.Header(m.config.Headers.ResetHeader, strconv.FormatInt(result.ResetTime.Unix(), 10))
	
	if !result.Allowed && result.RetryAfter > 0 {
		c.Header(m.config.Headers.RetryAfterHeader, strconv.Itoa(int(result.RetryAfter.Seconds())))
	}
}

// Default extractors and handlers

func defaultUserIDExtractor(c *gin.Context) (uuid.UUID, error) {
	// Try to get user ID from context (set by auth middleware)
	if userID, exists := c.Get("user_id"); exists {
		if uid, ok := userID.(uuid.UUID); ok {
			return uid, nil
		}
		if uidStr, ok := userID.(string); ok {
			return uuid.Parse(uidStr)
		}
	}

	// Try to get from header
	if userIDHeader := c.GetHeader("X-User-ID"); userIDHeader != "" {
		return uuid.Parse(userIDHeader)
	}

	// Fall back to IP-based identification
	ip := c.ClientIP()
	return uuid.NewSHA1(uuid.NameSpaceURL, []byte(ip)), nil
}

func defaultTierExtractor(c *gin.Context) Tier {
	// Try to get tier from context (set by auth middleware)
	if tier, exists := c.Get("user_tier"); exists {
		if tierStr, ok := tier.(string); ok {
			return Tier(tierStr)
		}
		if tierEnum, ok := tier.(Tier); ok {
			return tierEnum
		}
	}

	// Try to get from header
	if tierHeader := c.GetHeader("X-User-Tier"); tierHeader != "" {
		return Tier(tierHeader)
	}

	// Check if user is authenticated
	if _, exists := c.Get("user_id"); exists {
		return TierBasic
	}

	// Default to anonymous
	return TierAnonymous
}

func defaultActionExtractor(c *gin.Context) Action {
	// Map HTTP methods and paths to actions
	method := c.Request.Method
	path := c.Request.URL.Path

	// Check for specific endpoints
	if strings.Contains(path, "/upload") {
		return ActionUpload
	}
	if strings.Contains(path, "/download") {
		return ActionDownload
	}
	if strings.Contains(path, "/auth") || strings.Contains(path, "/login") {
		return ActionAuth
	}

	// Map by HTTP method
	switch method {
	case "POST":
		if strings.Contains(path, "/barrage") || strings.Contains(path, "/message") {
			return ActionBarrage
		}
		if strings.Contains(path, "/like") {
			return ActionLike
		}
		if strings.Contains(path, "/gift") {
			return ActionGift
		}
		return ActionAPI
	case "GET":
		return ActionAPI
	default:
		return ActionAPI
	}
}

func defaultErrorHandler(c *gin.Context, result *Result, err error) {
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Rate limiting service unavailable",
			"message": "Please try again later",
		})
		c.Abort()
		return
	}

	if result.Blocked {
		c.JSON(http.StatusTooManyRequests, gin.H{
			"error":        "User temporarily blocked",
			"message":      "Too many failed requests",
			"blocked_until": result.BlockedUntil.Format(time.RFC3339),
			"retry_after":  int(result.RetryAfter.Seconds()),
		})
		c.Abort()
		return
	}

	c.JSON(http.StatusTooManyRequests, gin.H{
		"error":      "Rate limit exceeded",
		"message":    "Too many requests",
		"limit":      result.Limit,
		"remaining":  result.Remaining,
		"reset_time": result.ResetTime.Format(time.RFC3339),
		"retry_after": int(result.RetryAfter.Seconds()),
	})
	c.Abort()
}

func defaultSuccessHandler(c *gin.Context, result *Result) {
	// Add rate limiting info to context for potential use by handlers
	c.Set("rate_limit_result", result)
}

// WebSocketMiddleware provides WebSocket middleware for rate limiting
type WebSocketMiddleware struct {
	manager Manager
	logger  *logrus.Logger
}

// NewWebSocketMiddleware creates a new WebSocket rate limiting middleware
func NewWebSocketMiddleware(manager Manager, logger *logrus.Logger) *WebSocketMiddleware {
	return &WebSocketMiddleware{
		manager: manager,
		logger:  logger,
	}
}

// CheckConnection checks if a WebSocket connection is allowed
func (w *WebSocketMiddleware) CheckConnection(userID uuid.UUID, tier Tier) (*Result, error) {
	return w.manager.Allow(nil, userID, ActionConnection, tier)
}

// CheckMessage checks if a WebSocket message is allowed
func (w *WebSocketMiddleware) CheckMessage(userID uuid.UUID, action Action, tier Tier) (*Result, error) {
	return w.manager.Allow(nil, userID, action, tier)
}
