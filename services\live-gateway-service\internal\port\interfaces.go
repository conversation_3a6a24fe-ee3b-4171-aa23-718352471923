/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-10 14:00:00
Modified: 2025-07-10 14:00:00
*/

package port

import (
	"context"
	"time"

	"cina.club/services/live-gateway-service/internal/domain/model"
)

// GatewayService defines the interface for the gateway service.
type GatewayService interface {
	// Stream management
	RequestPushURL(ctx context.Context, req *CreateStreamRequest) (*model.PushURL, error)
	RequestPlayURLs(ctx context.Context, req *RequestPlayURLsRequest) ([]*model.PlayURL, error)
	KickStream(ctx context.Context, req *KickStreamRequest) error
	GetStreamInfo(ctx context.Context, streamKey string) (*model.Stream, error)
	GetServerStats(ctx context.Context) (*model.MetricsInfo, error)

	// Webhook handling
	HandleWebhook(ctx context.Context, req *WebhookRequest) (*WebhookResponse, error)

	// Node management
	RegisterNode(ctx context.Context, node *model.MediaNode) error
	UpdateNodeStatus(ctx context.Context, nodeID string, status model.NodeStatus) error
	GetNodeByID(ctx context.Context, nodeID string) (*model.MediaNode, error)
	GetAllNodes(ctx context.Context) ([]*model.MediaNode, error)
	UpdateNodeStats(ctx context.Context, nodeID string, stats *model.NodeStats) error
	SelectNode(ctx context.Context, region string, protocol model.StreamProtocol) (*model.MediaNode, error)

	// Event handling
	PublishEvent(ctx context.Context, event *model.Event) error
	ProcessEventBatch(ctx context.Context, events []*model.Event) error
	QueryEvents(ctx context.Context, startTime, endTime time.Time) ([]*model.Event, error)

	// Statistics
	AggregateStreamStats(ctx context.Context, streamKeys []string) (map[string]interface{}, error)

	// Health
	HealthCheck(ctx context.Context) error
}

// CacheRepository defines the interface for cache operations.
type CacheRepository interface {
	// Stream mapping operations
	StoreStreamMapping(ctx context.Context, mapping *model.StreamMapping) error
	GetStreamMapping(ctx context.Context, streamKey string) (*model.StreamMapping, error)
	DeleteStreamMapping(ctx context.Context, streamKey string) error

	// Stream stats operations
	StoreStreamStats(ctx context.Context, streamKey string, stats *model.StreamStats) error
	GetStreamStats(ctx context.Context, streamKey string) (*model.StreamStats, error)
	DeleteStreamStats(ctx context.Context, streamKey string) error

	// Node operations
	StoreNode(ctx context.Context, node *model.MediaNode) error
	GetNode(ctx context.Context, nodeID string) (*model.MediaNode, error)
	GetAllNodes(ctx context.Context) ([]*model.MediaNode, error)
	DeleteNode(ctx context.Context, nodeID string) error
	UpdateNodeStatus(ctx context.Context, nodeID string, status model.NodeStatus) error
	UpdateNodeStats(ctx context.Context, nodeID string, stats *model.NodeStats) error
}

// MediaServerAdapter defines the interface for media server operations.
type MediaServerAdapter interface {
	// URL generation
	GeneratePushURL(ctx context.Context, req *PushURLRequest) (*model.PushURL, error)
	GeneratePlayURLs(ctx context.Context, req *RequestPlayURLsRequest) ([]*model.PlayURL, error)

	// Stream management
	KickStream(ctx context.Context, streamKey string) error
	GetStreamInfo(ctx context.Context, streamKey string) (*model.Stream, error)
	GetServerStats(ctx context.Context, nodeID string) (*model.MetricsInfo, error)

	// Health
	HealthCheck(ctx context.Context) error
}

// LoadBalancer defines the interface for load balancing operations.
type LoadBalancer interface {
	SelectNode(ctx context.Context, region string, protocol model.StreamProtocol) (*model.MediaNode, error)
	UpdateNodeLoad(ctx context.Context, nodeID string, load *model.NodeLoad) error
	GetAvailableNodes(ctx context.Context) ([]*model.MediaNode, error)
}

// LiveAPIClient defines the interface for live API operations.
type LiveAPIClient interface {
	// Authentication
	CheckPushAuth(ctx context.Context, req *AuthRequest) (*AuthResponse, error)

	// Event notifications
	NotifyStreamPublished(ctx context.Context, event *StreamPublishedEvent) error
	NotifyStreamUnpublished(ctx context.Context, event *StreamUnpublishedEvent) error
	NotifyStreamKicked(ctx context.Context, event *StreamKickedEvent) error
	NotifyRecordingCompleted(ctx context.Context, event *RecordingCompletedEvent) error
	SubmitForModeration(ctx context.Context, event *ModerationSubmissionEvent) error
}

// EventPublisher defines the interface for event publishing.
type EventPublisher interface {
	PublishEvent(ctx context.Context, event *model.Event) error
	PublishBatch(ctx context.Context, events []*model.Event) error
}

// EventStore defines the interface for event storage.
type EventStore interface {
	StoreEvent(ctx context.Context, event *model.Event) error
	StoreBatch(ctx context.Context, events []*model.Event) error
	QueryEvents(ctx context.Context, startTime, endTime time.Time) ([]*model.Event, error)
	GetEvent(ctx context.Context, eventID string) (*model.Event, error)
}
