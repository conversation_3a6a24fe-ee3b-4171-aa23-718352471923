/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:12:13
Modified: 2025-07-11 10:12:13
*/

package client

import (
	"context"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"cina.club/services/live-im-service/internal/application/port"
	"cina.club/services/live-im-service/internal/domain/model"
)

// AuthService implements the port.AuthService interface.
type AuthService struct {
	userCoreClient interface{} // TODO: Replace with actual gRPC client
	jwtSecret      string
	logger         *logrus.Logger
}

// NewAuthService creates a new auth service.
func NewAuthService(userCoreClient interface{}, jwtSecret string, logger *logrus.Logger) port.AuthService {
	return &AuthService{
		userCoreClient: userCoreClient,
		jwtSecret:      jwtSecret,
		logger:         logger.WithField("component", "auth_service"),
	}
}

// Claims represents JWT claims.
type Claims struct {
	UserID   string `json:"user_id"`
	Username string `json:"username"`
	Avatar   string `json:"avatar"`
	Role     string `json:"role"`
	Level    int    `json:"level"`
	VIPLevel int    `json:"vip_level"`
	jwt.RegisteredClaims
}

// ValidateToken validates a JWT token and returns user information.
func (a *AuthService) ValidateToken(ctx context.Context, token string) (*port.UserInfo, error) {
	// Parse JWT token
	claims := &Claims{}
	tkn, err := jwt.ParseWithClaims(token, claims, func(token *jwt.Token) (interface{}, error) {
		// Validate signing method
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(a.jwtSecret), nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	if !tkn.Valid {
		return nil, fmt.Errorf("invalid token")
	}

	// Check expiration
	if claims.ExpiresAt != nil && claims.ExpiresAt.Time.Before(time.Now()) {
		return nil, fmt.Errorf("token expired")
	}

	// Parse user ID
	userID, err := uuid.Parse(claims.UserID)
	if err != nil {
		return nil, fmt.Errorf("invalid user ID in token: %w", err)
	}

	// Convert role string to UserRole
	var role model.UserRole
	switch claims.Role {
	case "viewer":
		role = model.RoleViewer
	case "streamer":
		role = model.RoleStreamer
	case "moderator":
		role = model.RoleModerator
	case "admin":
		role = model.RoleAdmin
	case "vip":
		role = model.RoleVIP
	default:
		role = model.RoleViewer // Default to viewer
	}

	userInfo := &port.UserInfo{
		UserID:   userID,
		Username: claims.Username,
		Avatar:   claims.Avatar,
		Role:     role,
		Level:    claims.Level,
		VIPLevel: claims.VIPLevel,
		Badges:   []string{}, // TODO: Extract from token or fetch from user service
	}

	a.logger.WithFields(logrus.Fields{
		"user_id":  userID.String(),
		"username": claims.Username,
		"role":     role,
	}).Debug("Token validated successfully")

	return userInfo, nil
}

// CheckRoomPermission checks if a user has permission to join a room.
func (a *AuthService) CheckRoomPermission(ctx context.Context, userID uuid.UUID, roomID string) (bool, error) {
	// TODO: Implement actual room permission check by calling live-api-service
	// For now, allow all users to join any room
	
	a.logger.WithFields(logrus.Fields{
		"user_id": userID.String(),
		"room_id": roomID,
	}).Debug("Room permission check (allowing all for now)")

	return true, nil
}

// MockAuthService provides a mock implementation for testing.
type MockAuthService struct {
	logger *logrus.Logger
}

// NewMockAuthService creates a new mock auth service.
func NewMockAuthService(logger *logrus.Logger) port.AuthService {
	return &MockAuthService{
		logger: logger.WithField("component", "mock_auth_service"),
	}
}

// ValidateToken validates a mock token.
func (m *MockAuthService) ValidateToken(ctx context.Context, token string) (*port.UserInfo, error) {
	// For testing purposes, accept any non-empty token
	if token == "" {
		return nil, fmt.Errorf("empty token")
	}

	// Generate a mock user
	userID := uuid.New()
	userInfo := &port.UserInfo{
		UserID:   userID,
		Username: "test_user",
		Avatar:   "https://example.com/avatar.jpg",
		Role:     model.RoleViewer,
		Level:    1,
		VIPLevel: 0,
		Badges:   []string{"tester"},
	}

	m.logger.WithFields(logrus.Fields{
		"user_id":  userID.String(),
		"username": userInfo.Username,
		"token":    token,
	}).Debug("Mock token validated")

	return userInfo, nil
}

// CheckRoomPermission checks room permission (mock).
func (m *MockAuthService) CheckRoomPermission(ctx context.Context, userID uuid.UUID, roomID string) (bool, error) {
	// Allow all users to join any room in mock mode
	return true, nil
}

// GenerateTestToken generates a test JWT token for testing purposes.
func GenerateTestToken(userID uuid.UUID, username, secret string) (string, error) {
	claims := &Claims{
		UserID:   userID.String(),
		Username: username,
		Avatar:   "https://example.com/avatar.jpg",
		Role:     "viewer",
		Level:    1,
		VIPLevel: 0,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(24 * time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
			Issuer:    "live-im-service",
			Subject:   userID.String(),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	return token.SignedString([]byte(secret))
}
