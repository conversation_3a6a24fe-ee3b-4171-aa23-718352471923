Starting benchmark suite...
System Information:
OS: Windows_NT
Computer: WIN-TP72ETNS8L5
User: Administrator
Go Version: go version go1.24.4 windows/amd64

=== Message Benchmarks - Message creation, cloning, and validation ===
Timestamp: 07/11/2025 18:29:59

PASS
ok  	cina.club/services/live-im-service/test/benchmark	1.220s

----------------------------------------

=== Serialization Benchmarks - JSON serialization and deserialization ===
Timestamp: 07/11/2025 18:30:02

PASS
ok  	cina.club/services/live-im-service/test/benchmark	1.223s

----------------------------------------

=== Concurrent Benchmarks - Concurrent message processing ===
Timestamp: 07/11/2025 18:30:06

PASS
ok  	cina.club/services/live-im-service/test/benchmark	0.732s

----------------------------------------

=== Memory Benchmarks - Memory allocation patterns ===
Timestamp: 07/11/2025 18:30:09

PASS
ok  	cina.club/services/live-im-service/test/benchmark	0.722s

----------------------------------------

=== Redis Benchmarks - SKIPPED ===
Redis server not available or skipped

=== Performance Summary ===
Generated: 07/11/2025 18:30:12

Performance Analysis:

Recommendations:
1. Monitor message creation performance (target: <1000 ns/op)
2. Optimize large message serialization if >10000 ns/op
3. Consider object pooling for high-allocation operations
4. Run benchmarks regularly to detect performance regressions
