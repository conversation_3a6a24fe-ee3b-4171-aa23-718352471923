/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:12:13
Modified: 2025-07-11 10:12:13
*/

package cache

import (
	"context"
	"testing"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
)

// MockRedisClientForRateLimit implements a mock Redis client for rate limiter testing
type MockRedisClientForRateLimit struct {
	mock.Mock
}

func (m *MockRedisClientForRateLimit) Eval(ctx context.Context, script string, keys []string, args ...interface{}) *redis.Cmd {
	mockArgs := m.Called(ctx, script, keys, args)
	cmd := redis.NewCmd(ctx)
	if mockArgs.Error(0) != nil {
		cmd.SetErr(mockArgs.Error(0))
	} else {
		cmd.SetVal(mockArgs.Get(0))
	}
	return cmd
}

func (m *MockRedisClientForRateLimit) Get(ctx context.Context, key string) *redis.StringCmd {
	args := m.Called(ctx, key)
	cmd := redis.NewStringCmd(ctx)
	if args.Error(0) != nil {
		cmd.SetErr(args.Error(0))
	} else {
		cmd.SetVal(args.String(0))
	}
	return cmd
}

func (m *MockRedisClientForRateLimit) Del(ctx context.Context, keys ...string) *redis.IntCmd {
	args := m.Called(ctx, keys)
	cmd := redis.NewIntCmd(ctx)
	if args.Error(0) != nil {
		cmd.SetErr(args.Error(0))
	} else {
		cmd.SetVal(args.Get(0).(int64))
	}
	return cmd
}

func (m *MockRedisClientForRateLimit) Close() error {
	args := m.Called()
	return args.Error(0)
}

func TestNewRedisRateLimiter(t *testing.T) {
	mockClient := &MockRedisClientForRateLimit{}
	config := RateLimitConfig{
		Enabled:       true,
		BarrageRPS:    10,
		LikeRPS:       20,
		GiftRPS:       5,
		ConnectionRPS: 100,
		WindowSize:    time.Minute,
		MaxBurst:      50,
	}
	logger := logrus.New()

	rateLimiter := NewRedisRateLimiter(mockClient, config, logger)

	assert.NotNil(t, rateLimiter)
}

func TestRedisRateLimiter_Allow(t *testing.T) {
	mockClient := &MockRedisClientForRateLimit{}
	config := RateLimitConfig{
		Enabled:       true,
		BarrageRPS:    10,
		LikeRPS:       20,
		GiftRPS:       5,
		ConnectionRPS: 100,
		WindowSize:    time.Minute,
		MaxBurst:      50,
	}
	logger := logrus.New()
	rateLimiter := NewRedisRateLimiter(mockClient, config, logger).(*RedisRateLimiter)

	ctx := context.Background()
	userID := uuid.New()
	action := "barrage"

	t.Run("Allow when under limit", func(t *testing.T) {
		// Mock Lua script execution returning 1 (allowed)
		mockClient.On("Eval", ctx, mock.AnythingOfType("string"), mock.AnythingOfType("[]string"), mock.Anything).Return(int64(1), nil)

		allowed, err := rateLimiter.Allow(ctx, userID, action)

		assert.NoError(t, err)
		assert.True(t, allowed)
		mockClient.AssertExpectations(t)
	})

	t.Run("Deny when over limit", func(t *testing.T) {
		// Reset mock
		mockClient.ExpectedCalls = nil
		mockClient.Calls = nil

		// Mock Lua script execution returning 0 (denied)
		mockClient.On("Eval", ctx, mock.AnythingOfType("string"), mock.AnythingOfType("[]string"), mock.Anything).Return(int64(0), nil)

		allowed, err := rateLimiter.Allow(ctx, userID, action)

		assert.NoError(t, err)
		assert.False(t, allowed)
		mockClient.AssertExpectations(t)
	})

	t.Run("Error in Redis operation", func(t *testing.T) {
		// Reset mock
		mockClient.ExpectedCalls = nil
		mockClient.Calls = nil

		// Mock Lua script execution with error
		mockClient.On("Eval", ctx, mock.AnythingOfType("string"), mock.AnythingOfType("[]string"), mock.Anything).Return(nil, assert.AnError)

		allowed, err := rateLimiter.Allow(ctx, userID, action)

		assert.Error(t, err)
		assert.False(t, allowed)
		assert.Contains(t, err.Error(), "failed to check rate limit")
		mockClient.AssertExpectations(t)
	})
}

func TestRedisRateLimiter_AllowDisabled(t *testing.T) {
	mockClient := &MockRedisClientForRateLimit{}
	config := RateLimitConfig{
		Enabled: false, // Rate limiting disabled
	}
	logger := logrus.New()
	rateLimiter := NewRedisRateLimiter(mockClient, config, logger).(*RedisRateLimiter)

	ctx := context.Background()
	userID := uuid.New()
	action := "barrage"

	// When rate limiting is disabled, should always allow
	allowed, err := rateLimiter.Allow(ctx, userID, action)

	assert.NoError(t, err)
	assert.True(t, allowed)
	// No Redis calls should be made when disabled
	mockClient.AssertNotCalled(t, "Eval")
}

func TestRedisRateLimiter_AllowN(t *testing.T) {
	mockClient := &MockRedisClientForRateLimit{}
	config := RateLimitConfig{
		Enabled:       true,
		BarrageRPS:    10,
		LikeRPS:       20,
		GiftRPS:       5,
		ConnectionRPS: 100,
		WindowSize:    time.Minute,
		MaxBurst:      50,
	}
	logger := logrus.New()
	rateLimiter := NewRedisRateLimiter(mockClient, config, logger).(*RedisRateLimiter)

	ctx := context.Background()
	userID := uuid.New()
	action := "like"
	n := 5

	t.Run("Allow N when under limit", func(t *testing.T) {
		// Mock Lua script execution returning 1 (allowed)
		mockClient.On("Eval", ctx, mock.AnythingOfType("string"), mock.AnythingOfType("[]string"), mock.Anything).Return(int64(1), nil)

		allowed, err := rateLimiter.AllowN(ctx, userID, action, n)

		assert.NoError(t, err)
		assert.True(t, allowed)
		mockClient.AssertExpectations(t)
	})

	t.Run("Deny N when over limit", func(t *testing.T) {
		// Reset mock
		mockClient.ExpectedCalls = nil
		mockClient.Calls = nil

		// Mock Lua script execution returning 0 (denied)
		mockClient.On("Eval", ctx, mock.AnythingOfType("string"), mock.AnythingOfType("[]string"), mock.Anything).Return(int64(0), nil)

		allowed, err := rateLimiter.AllowN(ctx, userID, action, n)

		assert.NoError(t, err)
		assert.False(t, allowed)
		mockClient.AssertExpectations(t)
	})
}

func TestRedisRateLimiter_Reset(t *testing.T) {
	mockClient := &MockRedisClientForRateLimit{}
	config := RateLimitConfig{
		Enabled:       true,
		BarrageRPS:    10,
		LikeRPS:       20,
		GiftRPS:       5,
		ConnectionRPS: 100,
		WindowSize:    time.Minute,
		MaxBurst:      50,
	}
	logger := logrus.New()
	rateLimiter := NewRedisRateLimiter(mockClient, config, logger).(*RedisRateLimiter)

	ctx := context.Background()
	userID := uuid.New()
	action := "barrage"

	// Mock Del operation
	mockClient.On("Del", ctx, []string{"live:im:rate_limit:" + userID.String() + ":" + action}).Return(int64(1), nil)

	err := rateLimiter.Reset(ctx, userID, action)

	assert.NoError(t, err)
	mockClient.AssertExpectations(t)
}

func TestRedisRateLimiter_ResetError(t *testing.T) {
	mockClient := &MockRedisClientForRateLimit{}
	config := RateLimitConfig{
		Enabled:       true,
		BarrageRPS:    10,
		LikeRPS:       20,
		GiftRPS:       5,
		ConnectionRPS: 100,
		WindowSize:    time.Minute,
		MaxBurst:      50,
	}
	logger := logrus.New()
	rateLimiter := NewRedisRateLimiter(mockClient, config, logger).(*RedisRateLimiter)

	ctx := context.Background()
	userID := uuid.New()
	action := "barrage"

	// Mock Del operation with error
	mockClient.On("Del", ctx, []string{"live:im:rate_limit:" + userID.String() + ":" + action}).Return(int64(0), assert.AnError)

	err := rateLimiter.Reset(ctx, userID, action)

	assert.Error(t, err)
	assert.Contains(t, err.Error(), "failed to reset rate limit")
	mockClient.AssertExpectations(t)
}

func TestRedisRateLimiter_GetRemaining(t *testing.T) {
	mockClient := &MockRedisClientForRateLimit{}
	config := RateLimitConfig{
		Enabled:       true,
		BarrageRPS:    10,
		LikeRPS:       20,
		GiftRPS:       5,
		ConnectionRPS: 100,
		WindowSize:    time.Minute,
		MaxBurst:      50,
	}
	logger := logrus.New()
	rateLimiter := NewRedisRateLimiter(mockClient, config, logger).(*RedisRateLimiter)

	ctx := context.Background()
	userID := uuid.New()
	action := "barrage"

	t.Run("Get remaining tokens", func(t *testing.T) {
		// Mock Lua script execution returning remaining count
		mockClient.On("Eval", ctx, mock.AnythingOfType("string"), mock.AnythingOfType("[]string"), mock.Anything).Return(int64(7), nil)

		remaining, err := rateLimiter.GetRemaining(ctx, userID, action)

		assert.NoError(t, err)
		assert.Equal(t, 7, remaining)
		mockClient.AssertExpectations(t)
	})

	t.Run("Error getting remaining", func(t *testing.T) {
		// Reset mock
		mockClient.ExpectedCalls = nil
		mockClient.Calls = nil

		// Mock Lua script execution with error
		mockClient.On("Eval", ctx, mock.AnythingOfType("string"), mock.AnythingOfType("[]string"), mock.Anything).Return(nil, assert.AnError)

		remaining, err := rateLimiter.GetRemaining(ctx, userID, action)

		assert.Error(t, err)
		assert.Equal(t, 0, remaining)
		assert.Contains(t, err.Error(), "failed to get remaining rate limit")
		mockClient.AssertExpectations(t)
	})
}

func TestRedisRateLimiter_GetActionLimit(t *testing.T) {
	config := RateLimitConfig{
		Enabled:       true,
		BarrageRPS:    10,
		LikeRPS:       20,
		GiftRPS:       5,
		ConnectionRPS: 100,
		WindowSize:    time.Minute,
		MaxBurst:      50,
	}
	logger := logrus.New()
	rateLimiter := NewRedisRateLimiter(nil, config, logger).(*RedisRateLimiter)

	// Test different action limits
	assert.Equal(t, 10, rateLimiter.getActionLimit("barrage"))
	assert.Equal(t, 20, rateLimiter.getActionLimit("like"))
	assert.Equal(t, 5, rateLimiter.getActionLimit("gift"))
	assert.Equal(t, 100, rateLimiter.getActionLimit("connection"))
	assert.Equal(t, 10, rateLimiter.getActionLimit("unknown")) // Default to BarrageRPS
}
