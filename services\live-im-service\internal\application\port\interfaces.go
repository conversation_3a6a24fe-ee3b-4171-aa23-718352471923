/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:12:13
Modified: 2025-07-11 10:12:13
*/

package port

import (
	"context"
	"time"

	"github.com/google/uuid"

	"cina.club/services/live-im-service/internal/domain/model"
)

// Broadcaster defines the interface for message broadcasting.
type Broadcaster interface {
	// Broadcast sends a message to all subscribers of a room
	Broadcast(ctx context.Context, roomID string, message model.Message) error
	
	// Subscribe starts listening for broadcast messages
	Subscribe(ctx context.Context, handler BroadcastHandler) error
	
	// Unsubscribe stops listening for broadcast messages
	Unsubscribe(ctx context.Context) error
	
	// Close closes the broadcaster
	Close() error
}

// BroadcastHandler handles incoming broadcast messages.
type BroadcastHandler func(roomID string, message model.Message) error

// RoomStore defines the interface for room subscription management.
type RoomStore interface {
	// Subscribe adds a user to a room
	Subscribe(ctx context.Context, userID uuid.UUID, roomID string) error
	
	// Unsubscribe removes a user from a room
	Unsubscribe(ctx context.Context, userID uuid.UUID, roomID string) error
	
	// GetRoomMembers gets all members of a room
	GetRoomMembers(ctx context.Context, roomID string) ([]uuid.UUID, error)
	
	// GetUserRooms gets all rooms a user is subscribed to
	GetUserRooms(ctx context.Context, userID uuid.UUID) ([]string, error)
	
	// IsUserInRoom checks if a user is in a room
	IsUserInRoom(ctx context.Context, userID uuid.UUID, roomID string) (bool, error)
	
	// GetRoomCount gets the number of members in a room
	GetRoomCount(ctx context.Context, roomID string) (int, error)
}

// RateLimiter defines the interface for rate limiting.
type RateLimiter interface {
	// Allow checks if an action is allowed for a user
	Allow(ctx context.Context, userID uuid.UUID, action string) (bool, error)
	
	// AllowN checks if N actions are allowed for a user
	AllowN(ctx context.Context, userID uuid.UUID, action string, n int) (bool, error)
	
	// Reset resets the rate limit for a user and action
	Reset(ctx context.Context, userID uuid.UUID, action string) error
	
	// GetRemaining gets the remaining quota for a user and action
	GetRemaining(ctx context.Context, userID uuid.UUID, action string) (int, error)
}

// AuthService defines the interface for authentication.
type AuthService interface {
	// ValidateToken validates a JWT token and returns user information
	ValidateToken(ctx context.Context, token string) (*UserInfo, error)
	
	// CheckRoomPermission checks if a user has permission to join a room
	CheckRoomPermission(ctx context.Context, userID uuid.UUID, roomID string) (bool, error)
}

// UserInfo represents authenticated user information.
type UserInfo struct {
	UserID   uuid.UUID        `json:"user_id"`
	Username string           `json:"username"`
	Avatar   string           `json:"avatar"`
	Role     model.UserRole   `json:"role"`
	Level    int              `json:"level"`
	VIPLevel int              `json:"vip_level"`
	Badges   []string         `json:"badges"`
}

// MessageProcessor defines the interface for processing messages.
type MessageProcessor interface {
	// ProcessMessage processes a message from a client
	ProcessMessage(ctx context.Context, client *model.Client, message model.Message) error
	
	// ProcessGift processes a gift message with payment
	ProcessGift(ctx context.Context, client *model.Client, message model.Message) error
	
	// ProcessBarrage processes a barrage message
	ProcessBarrage(ctx context.Context, client *model.Client, message model.Message) error
	
	// ProcessLike processes a like message
	ProcessLike(ctx context.Context, client *model.Client, message model.Message) error
}

// LiveAPIClient defines the interface for Live API service client.
type LiveAPIClient interface {
	// GetRoomInfo gets room information
	GetRoomInfo(ctx context.Context, roomID string) (*model.RoomInfo, error)
	
	// CheckRoomAccess checks if a user can access a room
	CheckRoomAccess(ctx context.Context, userID uuid.UUID, roomID string) (bool, error)
	
	// NotifyUserJoin notifies that a user joined a room
	NotifyUserJoin(ctx context.Context, userID uuid.UUID, roomID string) error
	
	// NotifyUserLeave notifies that a user left a room
	NotifyUserLeave(ctx context.Context, userID uuid.UUID, roomID string) error
}

// BillingClient defines the interface for billing service client.
type BillingClient interface {
	// CreateInvoice creates an invoice for a purchase
	CreateInvoice(ctx context.Context, userID uuid.UUID, amount int, description string) (*Invoice, error)
	
	// ProcessPayment processes a payment
	ProcessPayment(ctx context.Context, invoiceID string) (*PaymentResult, error)
}

// CinaCoinClient defines the interface for CinaCoin ledger service client.
type CinaCoinClient interface {
	// Debit debits an amount from a user's account
	Debit(ctx context.Context, userID uuid.UUID, amount int, reason string) (*TransactionResult, error)
	
	// Credit credits an amount to a user's account
	Credit(ctx context.Context, userID uuid.UUID, amount int, reason string) (*TransactionResult, error)
	
	// GetBalance gets a user's balance
	GetBalance(ctx context.Context, userID uuid.UUID) (int, error)
}

// Invoice represents a billing invoice.
type Invoice struct {
	ID          string    `json:"id"`
	UserID      uuid.UUID `json:"user_id"`
	Amount      int       `json:"amount"`
	Description string    `json:"description"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
}

// PaymentResult represents a payment result.
type PaymentResult struct {
	Success       bool      `json:"success"`
	TransactionID string    `json:"transaction_id"`
	ErrorMessage  string    `json:"error_message,omitempty"`
	ProcessedAt   time.Time `json:"processed_at"`
}

// TransactionResult represents a transaction result.
type TransactionResult struct {
	Success       bool      `json:"success"`
	TransactionID string    `json:"transaction_id"`
	Balance       int       `json:"balance"`
	ErrorMessage  string    `json:"error_message,omitempty"`
	ProcessedAt   time.Time `json:"processed_at"`
}
