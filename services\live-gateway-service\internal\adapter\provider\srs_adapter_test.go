package provider

import (
	"context"
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"cina.club/services/live-gateway-service/internal/application/port"
	"cina.club/services/live-gateway-service/internal/domain/model"
)

func TestNewSRSAdapter(t *testing.T) {
	config := &SRSConfig{
		Nodes: []*SRSNode{
			{
				ID:       "test-node",
				Host:     "localhost",
				APIPort:  1985,
				RTMPPort: 1935,
				HTTPPort: 8080,
				Region:   "test-region",
				Weight:   100,
				Status:   "active",
			},
		},
		DefaultNode:   "test-node",
		WebhookSecret: "test-secret",
		APITimeout:    time.Second * 5,
		RTMPPort:      1935,
		SRTPort:       10080,
		WebRTCPort:    8000,
		HTTPPort:      8080,
		HLSPath:       "/hls",
		FLVPath:       "/flv",
		DASHPath:      "/dash",
		CDNDomain:     "cdn.example.com",
	}

	adapter, err := NewSRSAdapter(config)
	require.NoError(t, err)
	require.NotNil(t, adapter)

	// Test with invalid config
	_, err = NewSRSAdapter("invalid")
	assert.Error(t, err)
}

func TestSRSAdapter_ParseWebhookRequest(t *testing.T) {
	adapter := createTestAdapter(t)

	tests := []struct {
		name        string
		requestBody string
		expectError bool
		expected    *port.WebhookRequest
	}{
		{
			name: "valid publish request",
			requestBody: `{
				"action": "on_publish",
				"client_id": "test-client",
				"ip": "127.0.0.1",
				"vhost": "test-vhost",
				"app": "live",
				"stream": "test-stream",
				"param": "?token=test-token",
				"server_id": "test-node"
			}`,
			expectError: false,
			expected: &port.WebhookRequest{
				EventType:  model.WebhookEventTypePublish,
				StreamKey:  "test-stream",
				AuthToken:  "test-token",
				ClientIP:   "127.0.0.1",
				ServerNode: "test-node",
				Protocol:   model.StreamProtocolRTMP,
			},
		},
		{
			name: "valid unpublish request",
			requestBody: `{
				"action": "on_unpublish",
				"client_id": "test-client",
				"stream": "test-stream",
				"server_id": "test-node"
			}`,
			expectError: false,
			expected: &port.WebhookRequest{
				EventType:  model.WebhookEventTypeUnpublish,
				StreamKey:  "test-stream",
				ServerNode: "test-node",
				Protocol:   model.StreamProtocolRTMP,
			},
		},
		{
			name:        "invalid json",
			requestBody: "invalid json",
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := &port.WebhookParseRequest{
				Body:      []byte(tt.requestBody),
				UserAgent: "test-agent",
				Timestamp: time.Now(),
			}

			result, err := adapter.ParseWebhookRequest(req)
			if tt.expectError {
				assert.Error(t, err)
				return
			}

			require.NoError(t, err)
			require.NotNil(t, result)
			assert.Equal(t, tt.expected.EventType, result.EventType)
			assert.Equal(t, tt.expected.StreamKey, result.StreamKey)
			assert.Equal(t, tt.expected.ServerNode, result.ServerNode)
			assert.Equal(t, tt.expected.Protocol, result.Protocol)
		})
	}
}

func TestSRSAdapter_ValidateWebhookRequest(t *testing.T) {
	adapter := createTestAdapter(t)

	tests := []struct {
		name        string
		req         *port.WebhookValidateRequest
		expectError bool
	}{
		{
			name: "valid request",
			req: &port.WebhookValidateRequest{
				ClientIP: "127.0.0.1",
				Secret:   "test-secret",
			},
			expectError: false,
		},
		{
			name: "invalid secret",
			req: &port.WebhookValidateRequest{
				ClientIP: "127.0.0.1",
				Secret:   "wrong-secret",
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := adapter.ValidateWebhookRequest(tt.req)
			if tt.expectError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestSRSAdapter_GeneratePushURL(t *testing.T) {
	adapter := createTestAdapter(t)

	tests := []struct {
		name        string
		req         *port.PushURLRequest
		expectError bool
		checkURL    func(t *testing.T, url string)
	}{
		{
			name: "valid RTMP request",
			req: &port.PushURLRequest{
				RoomID:     "test-room",
				StreamKey:  "test-stream",
				AuthToken:  "test-token",
				Protocol:   model.StreamProtocolRTMP,
				ServerNode: "test-node",
				TTL:        time.Hour,
			},
			expectError: false,
			checkURL: func(t *testing.T, url string) {
				assert.Contains(t, url, "rtmp://")
				assert.Contains(t, url, "test-stream")
				assert.Contains(t, url, "token=test-token")
			},
		},
		{
			name: "unsupported protocol",
			req: &port.PushURLRequest{
				Protocol: "unsupported",
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := adapter.GeneratePushURL(context.Background(), tt.req)
			if tt.expectError {
				assert.Error(t, err)
				return
			}

			require.NoError(t, err)
			require.NotNil(t, result)
			tt.checkURL(t, result.URLString)
		})
	}
}

func TestSRSAdapter_GeneratePlayURLs(t *testing.T) {
	adapter := createTestAdapter(t)

	tests := []struct {
		name        string
		req         *port.RequestPlayURLsRequest
		expectError bool
		checkURLs   func(t *testing.T, urls []*model.PlayURL)
	}{
		{
			name: "valid request with multiple protocols",
			req: &port.RequestPlayURLsRequest{
				StreamKey: "test-stream",
				Protocols: []model.PlayProtocol{
					model.PlayProtocolHLS,
					model.PlayProtocolFLV,
				},
				EnableCDN: true,
				Region:    "test-region",
			},
			expectError: false,
			checkURLs: func(t *testing.T, urls []*model.PlayURL) {
				require.Len(t, urls, 2)
				assert.Contains(t, urls[0].URLString, ".m3u8")
				assert.Contains(t, urls[1].URLString, ".flv")
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := adapter.GeneratePlayURLs(context.Background(), tt.req)
			if tt.expectError {
				assert.Error(t, err)
				return
			}

			require.NoError(t, err)
			require.NotNil(t, result)
			tt.checkURLs(t, result)
		})
	}
}

func TestSRSAdapter_GetStreamInfo(t *testing.T) {
	adapter := createTestAdapter(t)
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := SRSAPIResponse{
			Code: 0,
			Data: map[string]interface{}{
				"streams": []interface{}{
					map[string]interface{}{
						"id":     1,
						"name":   "test-stream",
						"active": true,
						"publish": map[string]interface{}{
							"active": true,
						},
					},
				},
			},
		}
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	info, err := adapter.GetStreamInfo(context.Background(), "test-stream")
	require.NoError(t, err)
	require.NotNil(t, info)
	assert.Equal(t, "test-stream", info.StreamKey)
	assert.Equal(t, model.StreamStatusLive, info.Status)
}

func TestSRSAdapter_GetServerStats(t *testing.T) {
	adapter := createTestAdapter(t)
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := SRSAPIResponse{
			Code: 0,
			Data: map[string]interface{}{
				"streams":    10,
				"kbps":       5000,
				"recv_bytes": 1000000,
				"send_bytes": 2000000,
			},
		}
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	stats, err := adapter.GetServerStats(context.Background(), "test-node")
	require.NoError(t, err)
	require.NotNil(t, stats)
	assert.Equal(t, 10, stats.CurrentStreams)
	assert.Equal(t, int64(5000), stats.CurrentBitrate)
}

func TestSRSAdapter_HealthCheck(t *testing.T) {
	adapter := createTestAdapter(t)
	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		response := SRSAPIResponse{
			Code: 0,
			Data: map[string]interface{}{
				"ok": true,
			},
		}
		json.NewEncoder(w).Encode(response)
	}))
	defer server.Close()

	err := adapter.HealthCheck(context.Background())
	assert.NoError(t, err)
}

func TestSRSAdapter_GeneratePushURL_ValidationErrors(t *testing.T) {
	config := &SRSConfig{
		Nodes: []*SRSNode{
			{
				ID:       "test-node",
				Host:     "test-host",
				RTMPPort: 1935,
				APIPort:  1985,
				Status:   "active",
			},
		},
		DefaultNode:   "test-node",
		WebhookSecret: "test-secret",
		APITimeout:    time.Second * 5,
	}

	adapter, err := NewSRSAdapter(config)
	require.NoError(t, err)

	tests := []struct {
		name    string
		req     *port.PushURLRequest
		wantErr bool
		errMsg  string
	}{
		{
			name: "Empty stream key",
			req: &port.PushURLRequest{
				StreamKey: "",
				RoomID:    "test-room",
				Protocol:  model.StreamProtocolRTMP,
				Quality:   model.StreamQualityHigh,
				ClientIP:  "127.0.0.1",
				UserAgent: "test-agent",
			},
			wantErr: true,
			errMsg:  "stream key is required",
		},
		{
			name: "Empty room ID",
			req: &port.PushURLRequest{
				StreamKey: "test-stream",
				RoomID:    "",
				Protocol:  model.StreamProtocolRTMP,
				Quality:   model.StreamQualityHigh,
				ClientIP:  "127.0.0.1",
				UserAgent: "test-agent",
			},
			wantErr: true,
			errMsg:  "room ID is required",
		},
		{
			name: "Invalid protocol",
			req: &port.PushURLRequest{
				StreamKey: "test-stream",
				RoomID:    "test-room",
				Protocol:  "invalid",
				Quality:   model.StreamQualityHigh,
				ClientIP:  "127.0.0.1",
				UserAgent: "test-agent",
			},
			wantErr: true,
			errMsg:  "unsupported protocol",
		},
		{
			name: "Invalid quality level",
			req: &port.PushURLRequest{
				StreamKey: "test-stream",
				RoomID:    "test-room",
				Protocol:  model.StreamProtocolRTMP,
				Quality:   "invalid",
				ClientIP:  "127.0.0.1",
				UserAgent: "test-agent",
			},
			wantErr: true,
			errMsg:  "invalid quality level",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			url, err := adapter.GeneratePushURL(context.Background(), tt.req)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
				assert.Nil(t, url)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, url)
			}
		})
	}
}

func TestSRSAdapter_GeneratePlayURLs_ValidationErrors(t *testing.T) {
	config := &SRSConfig{
		Nodes: []*SRSNode{
			{
				ID:       "test-node",
				Host:     "test-host",
				RTMPPort: 1935,
				HTTPPort: 8080,
				APIPort:  1985,
				Status:   "active",
			},
		},
		DefaultNode:   "test-node",
		WebhookSecret: "test-secret",
		APITimeout:    time.Second * 5,
		HLSPath:       "/hls",
		FLVPath:       "/flv",
		DASHPath:      "/dash",
	}

	adapter, err := NewSRSAdapter(config)
	require.NoError(t, err)

	tests := []struct {
		name    string
		req     *port.RequestPlayURLsRequest
		wantErr bool
		errMsg  string
	}{
		{
			name: "Empty stream key",
			req: &port.RequestPlayURLsRequest{
				StreamKey: "",
				Protocols: []model.PlayProtocol{model.PlayProtocolHLS},
				Quality:   model.StreamQualityHigh,
				Region:    "test-region",
				ClientIP:  "127.0.0.1",
				UserAgent: "test-agent",
			},
			wantErr: true,
			errMsg:  "stream key is required",
		},
		{
			name: "Empty protocols",
			req: &port.RequestPlayURLsRequest{
				StreamKey: "test-stream",
				Protocols: []model.PlayProtocol{},
				Quality:   model.StreamQualityHigh,
				Region:    "test-region",
				ClientIP:  "127.0.0.1",
				UserAgent: "test-agent",
			},
			wantErr: true,
			errMsg:  "at least one protocol is required",
		},
		{
			name: "Invalid protocol",
			req: &port.RequestPlayURLsRequest{
				StreamKey: "test-stream",
				Protocols: []model.PlayProtocol{"invalid"},
				Quality:   model.StreamQualityHigh,
				Region:    "test-region",
				ClientIP:  "127.0.0.1",
				UserAgent: "test-agent",
			},
			wantErr: true,
			errMsg:  "unsupported protocol",
		},
		{
			name: "Invalid quality level",
			req: &port.RequestPlayURLsRequest{
				StreamKey: "test-stream",
				Protocols: []model.PlayProtocol{model.PlayProtocolHLS},
				Quality:   "invalid",
				Region:    "test-region",
				ClientIP:  "127.0.0.1",
				UserAgent: "test-agent",
			},
			wantErr: true,
			errMsg:  "invalid quality level",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			urls, err := adapter.GeneratePlayURLs(context.Background(), tt.req)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
				assert.Nil(t, urls)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, urls)
			}
		})
	}
}

func TestSRSAdapter_GetStreamInfo_ValidationErrors(t *testing.T) {
	config := &SRSConfig{
		Nodes: []*SRSNode{
			{
				ID:       "test-node",
				Host:     "test-host",
				RTMPPort: 1935,
				APIPort:  1985,
				Status:   "active",
			},
		},
		DefaultNode:   "test-node",
		WebhookSecret: "test-secret",
		APITimeout:    time.Second * 5,
	}

	adapter, err := NewSRSAdapter(config)
	require.NoError(t, err)

	tests := []struct {
		name      string
		streamKey string
		wantErr   bool
		errMsg    string
	}{
		{
			name:      "Empty stream key",
			streamKey: "",
			wantErr:   true,
			errMsg:    "stream key is required",
		},
		{
			name:      "Invalid stream key format",
			streamKey: "invalid/key",
			wantErr:   true,
			errMsg:    "invalid stream key format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			info, err := adapter.GetStreamInfo(context.Background(), tt.streamKey)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
				assert.Nil(t, info)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, info)
			}
		})
	}
}

func TestSRSAdapter_KickStream_ValidationErrors(t *testing.T) {
	config := &SRSConfig{
		Nodes: []*SRSNode{
			{
				ID:       "test-node",
				Host:     "test-host",
				RTMPPort: 1935,
				APIPort:  1985,
				Status:   "active",
			},
		},
		DefaultNode:   "test-node",
		WebhookSecret: "test-secret",
		APITimeout:    time.Second * 5,
	}

	adapter, err := NewSRSAdapter(config)
	require.NoError(t, err)

	tests := []struct {
		name      string
		streamKey string
		wantErr   bool
		errMsg    string
	}{
		{
			name:      "Empty stream key",
			streamKey: "",
			wantErr:   true,
			errMsg:    "stream key is required",
		},
		{
			name:      "Invalid stream key format",
			streamKey: "invalid/key",
			wantErr:   true,
			errMsg:    "invalid stream key format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := adapter.KickStream(context.Background(), tt.streamKey)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
			} else {
				assert.NoError(t, err)
			}
		})
	}
}

func TestSRSAdapter_GetServerStats_ValidationErrors(t *testing.T) {
	config := &SRSConfig{
		Nodes: []*SRSNode{
			{
				ID:       "test-node",
				Host:     "test-host",
				RTMPPort: 1935,
				APIPort:  1985,
				Status:   "active",
			},
		},
		DefaultNode:   "test-node",
		WebhookSecret: "test-secret",
		APITimeout:    time.Second * 5,
	}

	adapter, err := NewSRSAdapter(config)
	require.NoError(t, err)

	tests := []struct {
		name    string
		nodeID  string
		wantErr bool
		errMsg  string
	}{
		{
			name:    "Empty node ID",
			nodeID:  "",
			wantErr: true,
			errMsg:  "node ID is required",
		},
		{
			name:    "Invalid node ID format",
			nodeID:  "invalid/id",
			wantErr: true,
			errMsg:  "invalid node ID format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			stats, err := adapter.GetServerStats(context.Background(), tt.nodeID)
			if tt.wantErr {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errMsg)
				assert.Nil(t, stats)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, stats)
			}
		})
	}
}

func createTestAdapter(t *testing.T) *SRSAdapter {
	config := &SRSConfig{
		Nodes: []*SRSNode{
			{
				ID:       "test-node",
				Host:     "localhost",
				APIPort:  1985,
				RTMPPort: 1935,
				HTTPPort: 8080,
				Region:   "test-region",
				Weight:   100,
				Status:   "active",
			},
		},
		DefaultNode:   "test-node",
		WebhookSecret: "test-secret",
		APITimeout:    time.Second * 5,
		RTMPPort:      1935,
		HTTPPort:      8080,
		HLSPath:       "/hls",
		FLVPath:       "/flv",
		DASHPath:      "/dash",
		CDNDomain:     "cdn.example.com",
	}

	adapter, err := NewSRSAdapter(config)
	require.NoError(t, err)
	return adapter.(*SRSAdapter)
}
