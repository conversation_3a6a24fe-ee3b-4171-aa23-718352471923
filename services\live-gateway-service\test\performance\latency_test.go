package performance

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"

	"cina.club/services/live-gateway-service/internal/application/port"
	"cina.club/services/live-gateway-service/internal/domain/model"
)

const (
	latencyThresholdP50 = 50 * time.Millisecond  // 50ms for P50
	latencyThresholdP90 = 100 * time.Millisecond // 100ms for P90
	latencyThresholdP99 = 200 * time.Millisecond // 200ms for P99
)

func BenchmarkRequestPushURL(b *testing.B) {
	ctx := context.Background()
	svc := setupTestService(b)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			req := &port.CreateStreamRequest{
				RoomID:    uuid.New(),
				UserID:    uuid.New(),
				Protocol:  model.StreamProtocolRTMP,
				Quality:   model.StreamQualityHigh,
				ClientIP:  "127.0.0.1",
				UserAgent: "test-agent",
			}

			start := time.Now()
			_, err := svc.RequestPushURL(ctx, req)
			latency := time.Since(start)

			require.NoError(b, err)
			require.True(b, latency < latencyThresholdP99, "latency %v exceeds P99 threshold %v", latency, latencyThresholdP99)
		}
	})
}

func BenchmarkRequestPlayURLs(b *testing.B) {
	ctx := context.Background()
	svc := setupTestService(b)

	// Create a test stream
	streamKey := setupTestStreamForBench(b, svc)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			req := &port.RequestPlayURLsRequest{
				StreamKey: streamKey,
				Protocols: []model.PlayProtocol{
					model.PlayProtocolHLS,
					model.PlayProtocolFLV,
				},
				Quality:   model.StreamQualityHigh,
				EnableCDN: true,
				Region:    "test-region",
				ClientIP:  "127.0.0.1",
				UserAgent: "test-agent",
			}

			start := time.Now()
			_, err := svc.RequestPlayURLs(ctx, req)
			latency := time.Since(start)

			require.NoError(b, err)
			require.True(b, latency < latencyThresholdP99, "latency %v exceeds P99 threshold %v", latency, latencyThresholdP99)
		}
	})
}

func BenchmarkHandleWebhook(b *testing.B) {
	ctx := context.Background()
	svc := setupTestService(b)

	// Create a test stream
	streamKey := setupTestStreamForBench(b, svc)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			req := &port.WebhookRequest{
				EventType:  model.WebhookEventTypePublish,
				StreamKey:  streamKey,
				AuthToken:  "test-token",
				ClientIP:   "127.0.0.1",
				UserAgent:  "test-agent",
				ServerNode: "test-node",
				Protocol:   model.StreamProtocolRTMP,
				Timestamp:  time.Now(),
			}

			start := time.Now()
			_, err := svc.HandleWebhook(ctx, req)
			latency := time.Since(start)

			require.NoError(b, err)
			require.True(b, latency < latencyThresholdP99, "latency %v exceeds P99 threshold %v", latency, latencyThresholdP99)
		}
	})
}

func BenchmarkGetStreamInfo(b *testing.B) {
	ctx := context.Background()
	svc := setupTestService(b)

	// Create a test stream
	streamKey := setupTestStreamForBench(b, svc)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			start := time.Now()
			_, err := svc.GetStreamInfo(ctx, streamKey)
			latency := time.Since(start)

			require.NoError(b, err)
			require.True(b, latency < latencyThresholdP99, "latency %v exceeds P99 threshold %v", latency, latencyThresholdP99)
		}
	})
}

func BenchmarkGetServerStats(b *testing.B) {
	ctx := context.Background()
	svc := setupTestService(b)

	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			start := time.Now()
			_, err := svc.GetServerStats(ctx)
			latency := time.Since(start)

			require.NoError(b, err)
			require.True(b, latency < latencyThresholdP99, "latency %v exceeds P99 threshold %v", latency, latencyThresholdP99)
		}
	})
}

// setupTestStreamForBench creates a test stream for benchmarking
func setupTestStreamForBench(b *testing.B, svc port.GatewayService) string {
	ctx := context.Background()
	req := &port.CreateStreamRequest{
		RoomID:    uuid.New(),
		UserID:    uuid.New(),
		Protocol:  model.StreamProtocolRTMP,
		Quality:   model.StreamQualityHigh,
		ClientIP:  "127.0.0.1",
		UserAgent: "test-agent",
	}

	pushURL, err := svc.RequestPushURL(ctx, req)
	require.NoError(b, err)
	return pushURL.StreamKey // Use StreamKey instead of Path
}
