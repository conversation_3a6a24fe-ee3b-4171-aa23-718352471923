/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package circuitbreaker

import (
	"context"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"cina.club/services/live-im-service/internal/application/port"
	"cina.club/services/live-im-service/internal/domain/model"
	"cina.club/services/live-im-service/pkg/errors"
)

// RedisClientWrapper wraps a Redis client with circuit breaker protection
type RedisClientWrapper struct {
	client  *redis.Client
	manager *Manager
	logger  *logrus.Logger
}

// NewRedisClientWrapper creates a new Redis client wrapper with circuit breaker
func NewRedisClientWrapper(client *redis.Client, manager *Manager, logger *logrus.Logger) *RedisClientWrapper {
	return &RedisClientWrapper{
		client:  client,
		manager: manager,
		logger:  logger,
	}
}

// Get executes a Redis GET command with circuit breaker protection
func (r *RedisClientWrapper) Get(ctx context.Context, key string) *redis.StringCmd {
	var result *redis.StringCmd
	
	err := r.manager.ExecuteWithContext(ctx, "redis", func(ctx context.Context) error {
		result = r.client.Get(ctx, key)
		return result.Err()
	})
	
	if err != nil {
		// Return a command with the error
		cmd := redis.NewStringCmd(ctx, "get", key)
		cmd.SetErr(err)
		return cmd
	}
	
	return result
}

// Set executes a Redis SET command with circuit breaker protection
func (r *RedisClientWrapper) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) *redis.StatusCmd {
	var result *redis.StatusCmd
	
	err := r.manager.ExecuteWithContext(ctx, "redis", func(ctx context.Context) error {
		result = r.client.Set(ctx, key, value, expiration)
		return result.Err()
	})
	
	if err != nil {
		cmd := redis.NewStatusCmd(ctx, "set", key, value)
		cmd.SetErr(err)
		return cmd
	}
	
	return result
}

// Publish executes a Redis PUBLISH command with circuit breaker protection
func (r *RedisClientWrapper) Publish(ctx context.Context, channel string, message interface{}) *redis.IntCmd {
	var result *redis.IntCmd
	
	err := r.manager.ExecuteWithContext(ctx, "redis", func(ctx context.Context) error {
		result = r.client.Publish(ctx, channel, message)
		return result.Err()
	})
	
	if err != nil {
		cmd := redis.NewIntCmd(ctx, "publish", channel, message)
		cmd.SetErr(err)
		return cmd
	}
	
	return result
}

// LiveAPIClientWrapper wraps a LiveAPIClient with circuit breaker protection
type LiveAPIClientWrapper struct {
	client  port.LiveAPIClient
	manager *Manager
	logger  *logrus.Logger
}

// NewLiveAPIClientWrapper creates a new LiveAPIClient wrapper with circuit breaker
func NewLiveAPIClientWrapper(client port.LiveAPIClient, manager *Manager, logger *logrus.Logger) *LiveAPIClientWrapper {
	return &LiveAPIClientWrapper{
		client:  client,
		manager: manager,
		logger:  logger,
	}
}

// GetRoomInfo gets room information with circuit breaker protection
func (l *LiveAPIClientWrapper) GetRoomInfo(ctx context.Context, roomID string) (*model.RoomInfo, error) {
	var result *model.RoomInfo
	
	err := l.manager.ExecuteWithContext(ctx, "live_api", func(ctx context.Context) error {
		var err error
		result, err = l.client.GetRoomInfo(ctx, roomID)
		return err
	})
	
	if err != nil {
		// Convert circuit breaker errors to appropriate service errors
		if err == ErrOpenState {
			return nil, errors.ErrServiceUnavailable("live_api", err)
		}
		return nil, err
	}
	
	return result, nil
}

// CheckRoomAccess checks room access with circuit breaker protection
func (l *LiveAPIClientWrapper) CheckRoomAccess(ctx context.Context, userID uuid.UUID, roomID string) (bool, error) {
	var result bool
	
	err := l.manager.ExecuteWithContext(ctx, "live_api", func(ctx context.Context) error {
		var err error
		result, err = l.client.CheckRoomAccess(ctx, userID, roomID)
		return err
	})
	
	if err != nil {
		if err == ErrOpenState {
			return false, errors.ErrServiceUnavailable("live_api", err)
		}
		return false, err
	}
	
	return result, nil
}

// NotifyUserJoin notifies user join with circuit breaker protection
func (l *LiveAPIClientWrapper) NotifyUserJoin(ctx context.Context, userID uuid.UUID, roomID string) error {
	return l.manager.ExecuteWithContext(ctx, "live_api", func(ctx context.Context) error {
		err := l.client.NotifyUserJoin(ctx, userID, roomID)
		if err != nil && err == ErrOpenState {
			return errors.ErrServiceUnavailable("live_api", err)
		}
		return err
	})
}

// NotifyUserLeave notifies user leave with circuit breaker protection
func (l *LiveAPIClientWrapper) NotifyUserLeave(ctx context.Context, userID uuid.UUID, roomID string) error {
	return l.manager.ExecuteWithContext(ctx, "live_api", func(ctx context.Context) error {
		err := l.client.NotifyUserLeave(ctx, userID, roomID)
		if err != nil && err == ErrOpenState {
			return errors.ErrServiceUnavailable("live_api", err)
		}
		return err
	})
}

// BillingClientWrapper wraps a BillingClient with circuit breaker protection
type BillingClientWrapper struct {
	client  port.BillingClient
	manager *Manager
	logger  *logrus.Logger
}

// NewBillingClientWrapper creates a new BillingClient wrapper with circuit breaker
func NewBillingClientWrapper(client port.BillingClient, manager *Manager, logger *logrus.Logger) *BillingClientWrapper {
	return &BillingClientWrapper{
		client:  client,
		manager: manager,
		logger:  logger,
	}
}

// CreateInvoice creates an invoice with circuit breaker protection
func (b *BillingClientWrapper) CreateInvoice(ctx context.Context, userID uuid.UUID, amount int, description string) (*port.Invoice, error) {
	var result *port.Invoice
	
	err := b.manager.ExecuteWithContext(ctx, "billing", func(ctx context.Context) error {
		var err error
		result, err = b.client.CreateInvoice(ctx, userID, amount, description)
		return err
	})
	
	if err != nil {
		if err == ErrOpenState {
			return nil, errors.ErrServiceUnavailable("billing", err)
		}
		return nil, err
	}
	
	return result, nil
}

// ProcessPayment processes a payment with circuit breaker protection
func (b *BillingClientWrapper) ProcessPayment(ctx context.Context, invoiceID string) (*port.PaymentResult, error) {
	var result *port.PaymentResult
	
	err := b.manager.ExecuteWithContext(ctx, "billing", func(ctx context.Context) error {
		var err error
		result, err = b.client.ProcessPayment(ctx, invoiceID)
		return err
	})
	
	if err != nil {
		if err == ErrOpenState {
			return nil, errors.ErrServiceUnavailable("billing", err)
		}
		return nil, err
	}
	
	return result, nil
}

// CinaCoinClientWrapper wraps a CinaCoinClient with circuit breaker protection
type CinaCoinClientWrapper struct {
	client  port.CinaCoinClient
	manager *Manager
	logger  *logrus.Logger
}

// NewCinaCoinClientWrapper creates a new CinaCoinClient wrapper with circuit breaker
func NewCinaCoinClientWrapper(client port.CinaCoinClient, manager *Manager, logger *logrus.Logger) *CinaCoinClientWrapper {
	return &CinaCoinClientWrapper{
		client:  client,
		manager: manager,
		logger:  logger,
	}
}

// Debit debits an amount with circuit breaker protection
func (c *CinaCoinClientWrapper) Debit(ctx context.Context, userID uuid.UUID, amount int, reason string) (*port.TransactionResult, error) {
	var result *port.TransactionResult
	
	err := c.manager.ExecuteWithContext(ctx, "cina_coin", func(ctx context.Context) error {
		var err error
		result, err = c.client.Debit(ctx, userID, amount, reason)
		return err
	})
	
	if err != nil {
		if err == ErrOpenState {
			return nil, errors.ErrServiceUnavailable("cina_coin", err)
		}
		return nil, err
	}
	
	return result, nil
}

// Credit credits an amount with circuit breaker protection
func (c *CinaCoinClientWrapper) Credit(ctx context.Context, userID uuid.UUID, amount int, reason string) (*port.TransactionResult, error) {
	var result *port.TransactionResult
	
	err := c.manager.ExecuteWithContext(ctx, "cina_coin", func(ctx context.Context) error {
		var err error
		result, err = c.client.Credit(ctx, userID, amount, reason)
		return err
	})
	
	if err != nil {
		if err == ErrOpenState {
			return nil, errors.ErrServiceUnavailable("cina_coin", err)
		}
		return nil, err
	}
	
	return result, nil
}

// GetBalance gets balance with circuit breaker protection
func (c *CinaCoinClientWrapper) GetBalance(ctx context.Context, userID uuid.UUID) (int, error) {
	var result int
	
	err := c.manager.ExecuteWithContext(ctx, "cina_coin", func(ctx context.Context) error {
		var err error
		result, err = c.client.GetBalance(ctx, userID)
		return err
	})
	
	if err != nil {
		if err == ErrOpenState {
			return 0, errors.ErrServiceUnavailable("cina_coin", err)
		}
		return 0, err
	}
	
	return result, nil
}

// AuthServiceWrapper wraps an AuthService with circuit breaker protection
type AuthServiceWrapper struct {
	service port.AuthService
	manager *Manager
	logger  *logrus.Logger
}

// NewAuthServiceWrapper creates a new AuthService wrapper with circuit breaker
func NewAuthServiceWrapper(service port.AuthService, manager *Manager, logger *logrus.Logger) *AuthServiceWrapper {
	return &AuthServiceWrapper{
		service: service,
		manager: manager,
		logger:  logger,
	}
}

// ValidateToken validates a token with circuit breaker protection
func (a *AuthServiceWrapper) ValidateToken(ctx context.Context, token string) (*port.UserInfo, error) {
	var result *port.UserInfo
	
	err := a.manager.ExecuteWithContext(ctx, "auth", func(ctx context.Context) error {
		var err error
		result, err = a.service.ValidateToken(ctx, token)
		return err
	})
	
	if err != nil {
		if err == ErrOpenState {
			return nil, errors.ErrServiceUnavailable("auth", err)
		}
		return nil, err
	}
	
	return result, nil
}

// CheckRoomPermission checks room permission with circuit breaker protection
func (a *AuthServiceWrapper) CheckRoomPermission(ctx context.Context, userID uuid.UUID, roomID string) (bool, error) {
	var result bool
	
	err := a.manager.ExecuteWithContext(ctx, "auth", func(ctx context.Context) error {
		var err error
		result, err = a.service.CheckRoomPermission(ctx, userID, roomID)
		return err
	})
	
	if err != nil {
		if err == ErrOpenState {
			return false, errors.ErrServiceUnavailable("auth", err)
		}
		return false, err
	}
	
	return result, nil
}
