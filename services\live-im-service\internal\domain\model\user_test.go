/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:12:13
Modified: 2025-07-11 10:12:13
*/

package model

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
)

func TestNewUser(t *testing.T) {
	userID := uuid.New()
	username := "testuser"
	avatar := "avatar.jpg"

	user := NewUser(userID, username, avatar)

	assert.Equal(t, userID, user.UserID)
	assert.Equal(t, username, user.Username)
	assert.Equal(t, avatar, user.Avatar)
	assert.Equal(t, RoleViewer, user.Role) // Default role
	assert.Equal(t, 1, user.Level)         // Default level
	assert.Equal(t, 0, user.VIPLevel)      // Default VIP level
	assert.Empty(t, user.Badges)           // Default empty badges
}

func TestUserRoles(t *testing.T) {
	tests := []struct {
		name     string
		role     UserRole
		expected string
	}{
		{"Viewer", <PERSON><PERSON><PERSON><PERSON>, "viewer"},
		{"Streamer", RoleStreamer, "streamer"},
		{"Moderator", RoleModerator, "moderator"},
		{"Admin", RoleAdmin, "admin"},
		{"VIP", RoleVIP, "vip"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.Equal(t, tt.expected, string(tt.role))
		})
	}
}

func TestUserPermissions(t *testing.T) {
	userID := uuid.New()
	user := NewUser(userID, "testuser", "avatar.jpg")

	t.Run("Viewer permissions", func(t *testing.T) {
		user.Role = RoleViewer
		assert.True(t, user.CanSendMessage())
		assert.False(t, user.CanModerate())
		assert.False(t, user.CanManageRoom())
	})

	t.Run("Moderator permissions", func(t *testing.T) {
		user.Role = RoleModerator
		assert.True(t, user.CanSendMessage())
		assert.True(t, user.CanModerate())
		assert.False(t, user.CanManageRoom())
	})

	t.Run("Admin permissions", func(t *testing.T) {
		user.Role = RoleAdmin
		assert.True(t, user.CanSendMessage())
		assert.True(t, user.CanModerate())
		assert.True(t, user.CanManageRoom())
	})

	t.Run("Streamer permissions", func(t *testing.T) {
		user.Role = RoleStreamer
		assert.True(t, user.CanSendMessage())
		assert.True(t, user.CanModerate())
		assert.True(t, user.CanManageRoom())
	})

	t.Run("VIP permissions", func(t *testing.T) {
		user.Role = RoleVIP
		assert.True(t, user.CanSendMessage())
		assert.False(t, user.CanModerate())
		assert.False(t, user.CanManageRoom())
	})
}

func TestUserBadges(t *testing.T) {
	userID := uuid.New()
	user := NewUser(userID, "testuser", "avatar.jpg")

	t.Run("Add badge", func(t *testing.T) {
		user.AddBadge("vip")
		assert.Contains(t, user.Badges, "vip")
		assert.Len(t, user.Badges, 1)
	})

	t.Run("Add duplicate badge", func(t *testing.T) {
		user.AddBadge("vip") // Already exists
		assert.Contains(t, user.Badges, "vip")
		assert.Len(t, user.Badges, 1) // Should not duplicate
	})

	t.Run("Add multiple badges", func(t *testing.T) {
		user.AddBadge("moderator")
		user.AddBadge("verified")
		assert.Contains(t, user.Badges, "vip")
		assert.Contains(t, user.Badges, "moderator")
		assert.Contains(t, user.Badges, "verified")
		assert.Len(t, user.Badges, 3)
	})

	t.Run("Remove badge", func(t *testing.T) {
		user.RemoveBadge("moderator")
		assert.NotContains(t, user.Badges, "moderator")
		assert.Contains(t, user.Badges, "vip")
		assert.Contains(t, user.Badges, "verified")
		assert.Len(t, user.Badges, 2)
	})

	t.Run("Remove non-existent badge", func(t *testing.T) {
		user.RemoveBadge("nonexistent")
		assert.Len(t, user.Badges, 2) // Should remain unchanged
	})

	t.Run("Has badge", func(t *testing.T) {
		assert.True(t, user.HasBadge("vip"))
		assert.True(t, user.HasBadge("verified"))
		assert.False(t, user.HasBadge("moderator"))
		assert.False(t, user.HasBadge("nonexistent"))
	})
}

func TestUserLevel(t *testing.T) {
	userID := uuid.New()
	user := NewUser(userID, "testuser", "avatar.jpg")

	t.Run("Initial level", func(t *testing.T) {
		assert.Equal(t, 1, user.Level)
	})

	t.Run("Set level", func(t *testing.T) {
		user.SetLevel(10)
		assert.Equal(t, 10, user.Level)
	})

	t.Run("Level up", func(t *testing.T) {
		user.LevelUp()
		assert.Equal(t, 11, user.Level)
	})

	t.Run("Level down", func(t *testing.T) {
		user.LevelDown()
		assert.Equal(t, 10, user.Level)
	})

	t.Run("Level down minimum", func(t *testing.T) {
		user.SetLevel(1)
		user.LevelDown()
		assert.Equal(t, 1, user.Level) // Should not go below 1
	})
}

func TestUserVIPLevel(t *testing.T) {
	userID := uuid.New()
	user := NewUser(userID, "testuser", "avatar.jpg")

	t.Run("Initial VIP level", func(t *testing.T) {
		assert.Equal(t, 0, user.VIPLevel)
	})

	t.Run("Set VIP level", func(t *testing.T) {
		user.SetVIPLevel(5)
		assert.Equal(t, 5, user.VIPLevel)
	})

	t.Run("Is VIP", func(t *testing.T) {
		assert.True(t, user.IsVIP())

		user.SetVIPLevel(0)
		assert.False(t, user.IsVIP())
	})
}

func TestUserOnlineStatus(t *testing.T) {
	userID := uuid.New()
	user := NewUser(userID, "testuser", "avatar.jpg")

	// Note: User struct doesn't have IsOnline field in current implementation
	// SetOnline method exists for compatibility but doesn't store state
	t.Run("Set online", func(t *testing.T) {
		user.SetOnline(true)
		// No assertion since IsOnline field doesn't exist
	})

	t.Run("Set offline", func(t *testing.T) {
		user.SetOnline(false)
		// No assertion since IsOnline field doesn't exist
	})
}

func TestUserValidation(t *testing.T) {
	t.Run("Valid user", func(t *testing.T) {
		userID := uuid.New()
		user := NewUser(userID, "testuser", "avatar.jpg")

		assert.NotEqual(t, uuid.Nil, user.UserID)
		assert.NotEmpty(t, user.Username)
		assert.NotEmpty(t, user.Avatar)
	})

	t.Run("Empty username", func(t *testing.T) {
		userID := uuid.New()
		user := NewUser(userID, "", "avatar.jpg")

		assert.Empty(t, user.Username)
	})

	t.Run("Empty avatar", func(t *testing.T) {
		userID := uuid.New()
		user := NewUser(userID, "testuser", "")

		assert.Empty(t, user.Avatar)
	})
}

func TestUserString(t *testing.T) {
	userID := uuid.New()
	user := NewUser(userID, "testuser", "avatar.jpg")
	user.Role = RoleModerator
	user.Level = 5
	user.VIPLevel = 2

	str := user.String()
	assert.Contains(t, str, "testuser")
	assert.Contains(t, str, "moderator")
	assert.Contains(t, str, "5")
	assert.Contains(t, str, "2")
}

// Note: Clone method would need to be implemented in the User model
