# Live IM Service Configuration
server:
  http_addr: ":8080"
  health_addr: ":8081"
  metrics_addr: ":9090"
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "120s"
  max_header_bytes: 1048576
  enable_tls: false

redis:
  host: "localhost"
  port: 6379
  password: ""
  database: 3
  pool_size: 20
  min_idle_conns: 5
  dial_timeout: "5s"
  read_timeout: "3s"
  write_timeout: "3s"

clients:
  live_api:
    endpoint: "live-api-service:50051"
    timeout: "10s"
    max_retries: 3
    enable_tls: false
    enable_auth: false
  user_core:
    endpoint: "user-core-service:50051"
    timeout: "10s"
    max_retries: 3
    enable_tls: false
    enable_auth: false
  billing:
    endpoint: "billing-service:50051"
    timeout: "10s"
    max_retries: 3
    enable_tls: false
    enable_auth: false
  cina_coin:
    endpoint: "cina-coin-ledger-service:50051"
    timeout: "10s"
    max_retries: 3
    enable_tls: false
    enable_auth: false

hub:
  max_clients: 50000
  max_rooms: 1000
  heartbeat_interval: "30s"
  cleanup_interval: "5m"
  stats_interval: "1m"
  message_buffer_size: 1000
  broadcast_buffer_size: 10000
  like_aggregate_interval: "1s"
  max_message_size: 512
  enable_metrics: true
  enable_room_analytics: true

websocket:
  write_wait: "10s"
  pong_wait: "60s"
  ping_period: "54s"
  max_message_size: 512
  read_buffer_size: 1024
  write_buffer_size: 1024
  check_origin: false
  enable_compression: true

rate_limit:
  enabled: true
  barrage_rps: 1
  like_rps: 10
  gift_rps: 1
  connection_rps: 10
  window_size: "1s"
  max_burst: 5

logging:
  level: "info"
  format: "json"
  output: "stdout"
  enable_structured: true

tracing:
  enabled: false
  service_name: "live-im-service"
  endpoint: ""
  sample_rate: 0.1

security:
  jwt_secret: "your-super-secret-jwt-key-change-this-in-production"
  allowed_origins:
    - "http://localhost:3000"
    - "https://cina.club"
  enable_ip_whitelist: false
  ip_whitelist: []
  max_connections_per_ip: 100
  enable_cors: true

monitoring:
  enable_metrics: true
  metrics_path: "/metrics"
  enable_pprof: false
  pprof_path: "/debug/pprof"
  enable_healthz: true
  healthz_path: "/health"
