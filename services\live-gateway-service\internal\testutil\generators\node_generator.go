package generators

import (
	"fmt"
	"math/rand"
	"time"

	"cina.club/services/live-gateway-service/internal/domain/model"
)

// NodeGenerator generates test node data
type NodeGenerator struct {
	rand *rand.Rand
}

// NewNodeGenerator creates a new NodeGenerator
func NewNodeGenerator(seed int64) *NodeGenerator {
	return &NodeGenerator{
		rand: rand.New(rand.NewSource(seed)),
	}
}

// GenerateNodeID generates a valid node ID
func (g *NodeGenerator) GenerateNodeID() string {
	return fmt.Sprintf("node-%d", g.rand.Intn(1000))
}

// GenerateMediaNode generates a random media node
func (g *NodeGenerator) GenerateMediaNode() *model.MediaNode {
	protocols := []model.StreamProtocol{
		model.StreamProtocolRTMP,
		model.StreamProtocolWebRTC,
		model.StreamProtocolSRT,
	}

	// Randomly select 1-3 protocols
	numProtocols := g.rand.Intn(3) + 1
	selectedProtocols := make([]model.StreamProtocol, 0, numProtocols)
	protocolsCopy := make([]model.StreamProtocol, len(protocols))
	copy(protocolsCopy, protocols)
	for i := 0; i < numProtocols; i++ {
		idx := g.rand.Intn(len(protocolsCopy))
		selectedProtocols = append(selectedProtocols, protocolsCopy[idx])
		protocolsCopy = append(protocolsCopy[:idx], protocolsCopy[idx+1:]...)
	}

	regions := []string{
		"us-east-1",
		"us-west-2",
		"eu-west-1",
		"ap-northeast-1",
		"ap-southeast-1",
	}

	capacity := g.rand.Intn(1000) + 100 // 100-1100 capacity
	load := g.rand.Intn(capacity)       // 0 to capacity load

	return &model.MediaNode{
		ID:         g.GenerateNodeID(),
		ServerType: model.MediaServerTypeSRS, // Default to SRS
		Address:    "127.0.0.1",              // Default test address
		Port:       1935 + g.rand.Intn(100),  // Random port 1935-2034
		Region:     regions[g.rand.Intn(len(regions))],
		Capacity:   capacity,
		Load:       load,
		Status:     g.generateNodeStatus(),
		Capabilities: model.NodeCapabilities{
			RTMP:   true,                   // Always enable RTMP
			HLS:    g.rand.Float32() < 0.8, // 80% chance
			WebRTC: g.rand.Float32() < 0.6, // 60% chance
			SRT:    g.rand.Float32() < 0.4, // 40% chance
			DVR:    g.rand.Float32() < 0.5, // 50% chance
		},
		Protocols: selectedProtocols,
		Stats:     g.GenerateNodeStats(),
	}
}

// GenerateNodeStats generates random node statistics
func (g *NodeGenerator) GenerateNodeStats() *model.NodeStats {
	return &model.NodeStats{
		NodeID:       g.GenerateNodeID(),
		CPUUsage:     float64(g.rand.Intn(100)),
		MemoryUsage:  float64(g.rand.Intn(100)),
		NetworkIn:    int64(g.rand.Intn(1000000000)),
		NetworkOut:   int64(g.rand.Intn(1000000000)),
		StreamCount:  g.rand.Intn(100),
		ClientCount:  g.rand.Intn(1000),
		IsAvailable:  g.rand.Float32() < 0.9, // 90% chance of being available
		LastSeen:     time.Now().UTC().Format(time.RFC3339),
		ErrorCount:   g.rand.Intn(10),
		WarningCount: g.rand.Intn(20),
	}
}

// generateNodeStatus generates a random node status
func (g *NodeGenerator) generateNodeStatus() model.NodeStatus {
	statuses := []model.NodeStatus{
		model.NodeStatusActive,
		model.NodeStatusDraining,
		model.NodeStatusMaintenance,
		model.NodeStatusOffline,
		model.NodeStatusError,
	}

	// Active status has higher probability
	if g.rand.Float32() < 0.7 {
		return model.NodeStatusActive
	}

	return statuses[g.rand.Intn(len(statuses))]
}

// GenerateValidationTestCases generates test cases for validation testing
func (g *NodeGenerator) GenerateValidationTestCases() []struct {
	Name    string
	Node    *model.MediaNode
	WantErr bool
} {
	validNode := g.GenerateMediaNode()
	testCases := []struct {
		Name    string
		Node    *model.MediaNode
		WantErr bool
	}{
		{
			Name:    "valid node",
			Node:    validNode,
			WantErr: false,
		},
	}

	// Generate invalid test cases
	invalidNode := *validNode
	invalidNode.Capacity = -1 // invalid capacity
	testCases = append(testCases, struct {
		Name    string
		Node    *model.MediaNode
		WantErr bool
	}{
		Name:    "invalid capacity",
		Node:    &invalidNode,
		WantErr: true,
	})

	invalidNode = *validNode
	invalidNode.Load = invalidNode.Capacity + 1 // load exceeds capacity
	testCases = append(testCases, struct {
		Name    string
		Node    *model.MediaNode
		WantErr bool
	}{
		Name:    "load exceeds capacity",
		Node:    &invalidNode,
		WantErr: true,
	})

	invalidNode = *validNode
	invalidNode.Protocols = nil // no protocols
	testCases = append(testCases, struct {
		Name    string
		Node    *model.MediaNode
		WantErr bool
	}{
		Name:    "no protocols",
		Node:    &invalidNode,
		WantErr: true,
	})

	return testCases
}
