package cache

import (
	"context"
	"testing"
	"time"

	"github.com/alicebob/miniredis/v2"
	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"cina.club/services/live-gateway-service/internal/application/port"
	"cina.club/services/live-gateway-service/internal/domain/model"
)

func setupTestRedis(t *testing.T) (*RedisCache, *miniredis.Miniredis, func()) {
	mr, err := miniredis.Run()
	require.NoError(t, err)

	client := redis.NewClient(&redis.Options{
		Addr: mr.Addr(),
	})

	config := &port.CacheConfig{
		DefaultTTL:       time.Hour,
		StreamMappingTTL: time.Hour * 2,
		StreamStatsTTL:   time.Minute * 5,
		NodeLoadTTL:      time.Minute,
	}

	cache := NewRedisCache(client, config)
	require.NotNil(t, cache)

	cleanup := func() {
		client.Close()
		mr.Close()
	}

	return cache, mr, cleanup
}

func TestRedisCache_StreamMapping(t *testing.T) {
	cache, mr, cleanup := setupTestRedis(t)
	defer cleanup()

	ctx := context.Background()
	roomID := uuid.New()
	userID := uuid.New()

	mapping := &model.StreamMapping{
		StreamKey:  "test-stream",
		RoomID:     roomID,
		UserID:     userID,
		AuthToken:  "test-token",
		ServerNode: "test-node",
		CreatedAt:  time.Now(),
		ExpiresAt:  time.Now().Add(time.Hour),
	}

	// Test store
	err := cache.StoreStreamMapping(ctx, mapping)
	assert.NoError(t, err)

	// Verify data in Redis
	key := cache.keyGen.StreamMappingKey(mapping.StreamKey)
	assert.True(t, mr.Exists(key))

	// Test get
	result, err := cache.GetStreamMapping(ctx, mapping.StreamKey)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, mapping.StreamKey, result.StreamKey)
	assert.Equal(t, mapping.RoomID, result.RoomID)
	assert.Equal(t, mapping.UserID, result.UserID)
	assert.Equal(t, mapping.AuthToken, result.AuthToken)
	assert.Equal(t, mapping.ServerNode, result.ServerNode)

	// Test delete
	err = cache.DeleteStreamMapping(ctx, mapping.StreamKey)
	assert.NoError(t, err)
	assert.False(t, mr.Exists(key))

	// Test get non-existent
	result, err = cache.GetStreamMapping(ctx, "non-existent")
	assert.NoError(t, err)
	assert.Nil(t, result)
}

func TestRedisCache_StreamStats(t *testing.T) {
	cache, mr, cleanup := setupTestRedis(t)
	defer cleanup()

	ctx := context.Background()
	stats := &model.StreamStats{
		VideoBitrate:    5000000,
		AudioBitrate:    128000,
		VideoFPS:        30,
		VideoKeyframes:  1000,
		VideoFrames:     30000,
		AudioFrames:     48000,
		BytesSent:       1000000,
		BytesReceived:   1000000,
		PacketsLost:     0,
		PacketsReceived: 10000,
		RTT:             10,
		Jitter:          1,
		AudioLevel:      0.8,
		Width:           1920,
		Height:          1080,
		AudioCodec:      "aac",
		VideoCodec:      "h264",
		DroppedFrames:   0,
		ViewerCount:     100,
	}

	// Test store
	err := cache.StoreStreamStats(ctx, "test-stream", stats)
	assert.NoError(t, err)

	// Verify data in Redis
	key := cache.keyGen.StreamStatsKey("test-stream")
	assert.True(t, mr.Exists(key))

	// Test get
	result, err := cache.GetStreamStats(ctx, "test-stream")
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, stats.VideoBitrate, result.VideoBitrate)
	assert.Equal(t, stats.AudioBitrate, result.AudioBitrate)
	assert.Equal(t, stats.VideoFPS, result.VideoFPS)
	assert.Equal(t, stats.ViewerCount, result.ViewerCount)

	// Test delete
	err = cache.DeleteStreamStats(ctx, "test-stream")
	assert.NoError(t, err)
	assert.False(t, mr.Exists(key))

	// Test get non-existent
	result, err = cache.GetStreamStats(ctx, "non-existent")
	assert.NoError(t, err)
	assert.Nil(t, result)
}

func TestRedisCache_NodeLoad(t *testing.T) {
	cache, mr, cleanup := setupTestRedis(t)
	defer cleanup()

	ctx := context.Background()
	load := &model.NodeLoad{
		NodeID:         "test-node",
		LoadScore:      0.75,
		CurrentStreams: 10,
		CurrentBitrate: 50000000,
		CPUUsage:       0.5,
		MemoryUsage:    0.6,
		NetworkIn:      1000000,
		NetworkOut:     2000000,
		LastUpdated:    time.Now(),
	}

	// Test store
	err := cache.StoreNodeLoad(ctx, load.NodeID, load)
	assert.NoError(t, err)

	// Verify data in Redis
	key := cache.keyGen.NodeLoadKey(load.NodeID)
	assert.True(t, mr.Exists(key))

	// Test get
	result, err := cache.GetNodeLoad(ctx, load.NodeID)
	assert.NoError(t, err)
	assert.NotNil(t, result)
	assert.Equal(t, load.NodeID, result.NodeID)
	assert.Equal(t, load.LoadScore, result.LoadScore)
	assert.Equal(t, load.CurrentStreams, result.CurrentStreams)
	assert.Equal(t, load.CurrentBitrate, result.CurrentBitrate)

	// Test list
	loads, err := cache.ListNodeLoads(ctx)
	assert.NoError(t, err)
	assert.NotNil(t, loads)
	assert.Len(t, loads, 1)
	assert.Contains(t, loads, load.NodeID)

	// Test delete
	err = cache.DeleteNodeLoad(ctx, load.NodeID)
	assert.NoError(t, err)
	assert.False(t, mr.Exists(key))

	// Test get non-existent
	result, err = cache.GetNodeLoad(ctx, "non-existent")
	assert.NoError(t, err)
	assert.Nil(t, result)
}

func TestRedisCache_ErrorHandling(t *testing.T) {
	cache, mr, cleanup := setupTestRedis(t)
	defer cleanup()

	ctx := context.Background()

	// Test connection error
	mr.Close()

	// Test stream mapping operations
	mapping := &model.StreamMapping{
		StreamKey: "test-stream",
		RoomID:    uuid.New(),
		UserID:    uuid.New(),
	}
	err := cache.StoreStreamMapping(ctx, mapping)
	assert.Error(t, err)

	_, err = cache.GetStreamMapping(ctx, mapping.StreamKey)
	assert.Error(t, err)

	err = cache.DeleteStreamMapping(ctx, mapping.StreamKey)
	assert.Error(t, err)

	// Test stream stats operations
	stats := &model.StreamStats{
		VideoBitrate: 5000000,
		AudioBitrate: 128000,
	}
	err = cache.StoreStreamStats(ctx, "test-stream", stats)
	assert.Error(t, err)

	_, err = cache.GetStreamStats(ctx, "test-stream")
	assert.Error(t, err)

	err = cache.DeleteStreamStats(ctx, "test-stream")
	assert.Error(t, err)

	// Test node load operations
	load := &model.NodeLoad{
		NodeID:    "test-node",
		LoadScore: 0.75,
	}
	err = cache.StoreNodeLoad(ctx, load.NodeID, load)
	assert.Error(t, err)

	_, err = cache.GetNodeLoad(ctx, load.NodeID)
	assert.Error(t, err)

	_, err = cache.ListNodeLoads(ctx)
	assert.Error(t, err)

	err = cache.DeleteNodeLoad(ctx, load.NodeID)
	assert.Error(t, err)
}

func TestRedisCache_InvalidData(t *testing.T) {
	cache, mr, cleanup := setupTestRedis(t)
	defer cleanup()

	ctx := context.Background()

	// Test invalid stream mapping data
	key := cache.keyGen.StreamMappingKey("test-stream")
	mr.Set(key, "invalid json")

	_, err := cache.GetStreamMapping(ctx, "test-stream")
	assert.Error(t, err)

	// Test invalid stream stats data
	key = cache.keyGen.StreamStatsKey("test-stream")
	mr.Set(key, "invalid json")

	_, err = cache.GetStreamStats(ctx, "test-stream")
	assert.Error(t, err)

	// Test invalid node load data
	key = cache.keyGen.NodeLoadKey("test-node")
	mr.Set(key, "invalid json")

	_, err = cache.GetNodeLoad(ctx, "test-node")
	assert.Error(t, err)
}

func TestRedisCache_TTL(t *testing.T) {
	cache, mr, cleanup := setupTestRedis(t)
	defer cleanup()

	ctx := context.Background()

	// Test stream mapping TTL
	mapping := &model.StreamMapping{
		StreamKey: "test-stream",
		RoomID:    uuid.New(),
		UserID:    uuid.New(),
	}
	err := cache.StoreStreamMapping(ctx, mapping)
	assert.NoError(t, err)

	key := cache.keyGen.StreamMappingKey(mapping.StreamKey)
	ttl := mr.TTL(key)
	assert.Equal(t, float64(7200), ttl.Seconds())

	// Test stream stats TTL
	stats := &model.StreamStats{
		VideoBitrate: 5000000,
		AudioBitrate: 128000,
	}
	err = cache.StoreStreamStats(ctx, "test-stream", stats)
	assert.NoError(t, err)

	key = cache.keyGen.StreamStatsKey("test-stream")
	ttl = mr.TTL(key)
	assert.Equal(t, float64(300), ttl.Seconds())

	// Test node load TTL
	load := &model.NodeLoad{
		NodeID:    "test-node",
		LoadScore: 0.75,
	}
	err = cache.StoreNodeLoad(ctx, load.NodeID, load)
	assert.NoError(t, err)

	key = cache.keyGen.NodeLoadKey(load.NodeID)
	ttl = mr.TTL(key)
	assert.Equal(t, float64(60), ttl.Seconds())
}

func TestRedisCache_Ping(t *testing.T) {
	cache, mr, cleanup := setupTestRedis(t)
	defer cleanup()

	ctx := context.Background()

	// Test successful ping
	err := cache.Ping(ctx)
	assert.NoError(t, err)

	// Test failed ping
	mr.Close()
	err = cache.Ping(ctx)
	assert.Error(t, err)
}

func TestRedisCache_Close(t *testing.T) {
	cache, _, cleanup := setupTestRedis(t)
	defer cleanup()

	err := cache.Close()
	assert.NoError(t, err)
}
