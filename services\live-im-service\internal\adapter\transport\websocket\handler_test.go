/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:12:13
Modified: 2025-07-11 10:12:13
*/

package websocket

import (
	"context"
	"testing"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"cina.club/services/live-im-service/internal/application/port"
	"cina.club/services/live-im-service/internal/application/service"
	"cina.club/services/live-im-service/internal/domain/model"
)

// MockHub implements a mock Hub for testing
type MockHub struct {
	mock.Mock
}

func (m *MockHub) Start() {
	m.Called()
}

func (m *MockHub) Stop() {
	m.Called()
}

func (m *MockHub) RegisterClient(client *model.Client) {
	m.Called(client)
}

func (m *MockHub) UnregisterClient(client *model.Client) {
	m.Called(client)
}

func (m *MockHub) BroadcastToRoom(roomID string, message model.Message, excludeClient *model.Client) {
	m.Called(roomID, message, excludeClient)
}

func (m *MockHub) NotifyUserEvent(event model.UserEvent) {
	m.Called(event)
}

func (m *MockHub) GetStats() *service.HubStats {
	args := m.Called()
	return args.Get(0).(*service.HubStats)
}

// MockAuthService implements a mock AuthService for testing
type MockAuthService struct {
	mock.Mock
}

func (m *MockAuthService) ValidateToken(ctx context.Context, token string) (*port.UserInfo, error) {
	args := m.Called(ctx, token)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.UserInfo), args.Error(1)
}

func (m *MockAuthService) CheckRoomPermission(ctx context.Context, userID uuid.UUID, roomID string) (bool, error) {
	args := m.Called(ctx, userID, roomID)
	return args.Bool(0), args.Error(1)
}

// MockRateLimiter implements a mock RateLimiter for testing
type MockRateLimiter struct {
	mock.Mock
}

func (m *MockRateLimiter) Allow(ctx context.Context, userID uuid.UUID, action string) (bool, error) {
	args := m.Called(ctx, userID, action)
	return args.Bool(0), args.Error(1)
}

func (m *MockRateLimiter) AllowN(ctx context.Context, userID uuid.UUID, action string, n int) (bool, error) {
	args := m.Called(ctx, userID, action, n)
	return args.Bool(0), args.Error(1)
}

func (m *MockRateLimiter) Reset(ctx context.Context, userID uuid.UUID, action string) error {
	args := m.Called(ctx, userID, action)
	return args.Error(0)
}

func (m *MockRateLimiter) GetRemaining(ctx context.Context, userID uuid.UUID, action string) (int, error) {
	args := m.Called(ctx, userID, action)
	return args.Int(0), args.Error(1)
}

func TestNewHandler(t *testing.T) {
	// Create a real hub for testing since NewHandler expects *service.Hub
	logger := logrus.New()

	wsConfig := WebSocketConfig{
		WriteWait:         10 * time.Second,
		PongWait:          60 * time.Second,
		PingPeriod:        54 * time.Second,
		MaxMessageSize:    512,
		ReadBufferSize:    1024,
		WriteBufferSize:   1024,
		CheckOrigin:       false,
		EnableCompression: false,
	}

	securityConfig := SecurityConfig{
		JWTSecret:           "test-secret",
		AllowedOrigins:      []string{"*"},
		EnableIPWhitelist:   false,
		IPWhitelist:         []string{},
		MaxConnectionsPerIP: 100,
		EnableCORS:          true,
	}

	// Create a minimal hub for testing (we can pass nil since we're just testing structure)
	handler := NewHandler(nil, wsConfig, securityConfig, logger)

	assert.NotNil(t, handler)
}

func TestHandler_HandleWebSocketStructure(t *testing.T) {
	logger := logrus.New()

	wsConfig := WebSocketConfig{
		WriteWait:         10 * time.Second,
		PongWait:          60 * time.Second,
		PingPeriod:        54 * time.Second,
		MaxMessageSize:    512,
		ReadBufferSize:    1024,
		WriteBufferSize:   1024,
		CheckOrigin:       false,
		EnableCompression: false,
	}

	securityConfig := SecurityConfig{
		JWTSecret:           "test-secret",
		AllowedOrigins:      []string{"*"},
		EnableIPWhitelist:   false,
		IPWhitelist:         []string{},
		MaxConnectionsPerIP: 100,
		EnableCORS:          true,
	}

	handler := NewHandler(nil, wsConfig, securityConfig, logger)

	// Test that HandleWebSocket method exists
	assert.NotNil(t, handler)

	// Create a test gin context to verify the method signature
	gin.SetMode(gin.TestMode)
	router := gin.New()
	router.GET("/ws", handler.HandleWebSocket)

	// Test that the route can be registered (method exists with correct signature)
	assert.NotNil(t, router)
}

func TestHandler_ConfigValidation(t *testing.T) {
	logger := logrus.New()

	wsConfig := WebSocketConfig{
		WriteWait:         10 * time.Second,
		PongWait:          60 * time.Second,
		PingPeriod:        54 * time.Second,
		MaxMessageSize:    512,
		ReadBufferSize:    1024,
		WriteBufferSize:   1024,
		CheckOrigin:       false,
		EnableCompression: false,
	}

	securityConfig := SecurityConfig{
		JWTSecret:           "test-secret",
		AllowedOrigins:      []string{"*"},
		EnableIPWhitelist:   false,
		IPWhitelist:         []string{},
		MaxConnectionsPerIP: 100,
		EnableCORS:          true,
	}

	handler := NewHandler(nil, wsConfig, securityConfig, logger)

	// Test that handler was created with the correct configuration
	assert.NotNil(t, handler)
}
