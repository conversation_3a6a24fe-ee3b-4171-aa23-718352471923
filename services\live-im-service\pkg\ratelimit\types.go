/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package ratelimit

import (
	"context"
	"time"

	"github.com/google/uuid"
)

// Algorithm represents different rate limiting algorithms
type Algorithm string

const (
	// AlgorithmTokenBucket uses token bucket algorithm
	AlgorithmTokenBucket Algorithm = "token_bucket"
	// AlgorithmSlidingWindow uses sliding window algorithm
	AlgorithmSlidingWindow Algorithm = "sliding_window"
	// AlgorithmFixedWindow uses fixed window algorithm
	AlgorithmFixedWindow Algorithm = "fixed_window"
	// AlgorithmLeakyBucket uses leaky bucket algorithm
	AlgorithmLeakyBucket Algorithm = "leaky_bucket"
)

// Action represents different types of actions that can be rate limited
type Action string

const (
	// ActionBarrage represents barrage/chat messages
	ActionBarrage Action = "barrage"
	// ActionLike represents like actions
	ActionLike Action = "like"
	// ActionGift represents gift sending
	ActionGift Action = "gift"
	// ActionConnection represents connection attempts
	ActionConnection Action = "connection"
	// ActionAuth represents authentication attempts
	ActionAuth Action = "auth"
	// ActionAPI represents general API calls
	ActionAPI Action = "api"
	// ActionUpload represents file uploads
	ActionUpload Action = "upload"
	// ActionDownload represents file downloads
	ActionDownload Action = "download"
)

// Tier represents different user tiers with different rate limits
type Tier string

const (
	// TierAnonymous for anonymous users
	TierAnonymous Tier = "anonymous"
	// TierBasic for basic registered users
	TierBasic Tier = "basic"
	// TierPremium for premium users
	TierPremium Tier = "premium"
	// TierVIP for VIP users
	TierVIP Tier = "vip"
	// TierAdmin for admin users
	TierAdmin Tier = "admin"
)

// Config represents rate limiting configuration
type Config struct {
	// Algorithm to use for rate limiting
	Algorithm Algorithm `json:"algorithm" yaml:"algorithm"`
	
	// Enabled indicates if rate limiting is enabled
	Enabled bool `json:"enabled" yaml:"enabled"`
	
	// Limit is the maximum number of requests allowed
	Limit int `json:"limit" yaml:"limit"`
	
	// Window is the time window for the limit
	Window time.Duration `json:"window" yaml:"window"`
	
	// Burst is the maximum burst size (for token bucket)
	Burst int `json:"burst" yaml:"burst"`
	
	// RefillRate is the rate at which tokens are refilled (for token bucket)
	RefillRate time.Duration `json:"refill_rate" yaml:"refill_rate"`
	
	// BlockDuration is how long to block after exceeding limits
	BlockDuration time.Duration `json:"block_duration" yaml:"block_duration"`
	
	// SkipSuccessfulRequests indicates if successful requests should be counted
	SkipSuccessfulRequests bool `json:"skip_successful_requests" yaml:"skip_successful_requests"`
	
	// SkipFailedRequests indicates if failed requests should be counted
	SkipFailedRequests bool `json:"skip_failed_requests" yaml:"skip_failed_requests"`
}

// TierConfig represents rate limiting configuration for different user tiers
type TierConfig struct {
	// Multiplier for the base rate limit
	Multiplier float64 `json:"multiplier" yaml:"multiplier"`
	
	// Override specific limits for this tier
	Overrides map[Action]Config `json:"overrides" yaml:"overrides"`
}

// ActionConfig represents rate limiting configuration for specific actions
type ActionConfig struct {
	// Base configuration
	Base Config `json:"base" yaml:"base"`
	
	// Tier-specific configurations
	Tiers map[Tier]TierConfig `json:"tiers" yaml:"tiers"`
}

// ManagerConfig represents the overall rate limiting manager configuration
type ManagerConfig struct {
	// Default configuration for all actions
	Default Config `json:"default" yaml:"default"`
	
	// Action-specific configurations
	Actions map[Action]ActionConfig `json:"actions" yaml:"actions"`
	
	// Global settings
	EnableMetrics bool `json:"enable_metrics" yaml:"enable_metrics"`
	EnableLogging bool `json:"enable_logging" yaml:"enable_logging"`
	
	// Redis key prefix
	KeyPrefix string `json:"key_prefix" yaml:"key_prefix"`
	
	// Cleanup settings
	CleanupInterval time.Duration `json:"cleanup_interval" yaml:"cleanup_interval"`
	CleanupBatchSize int `json:"cleanup_batch_size" yaml:"cleanup_batch_size"`
}

// Result represents the result of a rate limit check
type Result struct {
	// Allowed indicates if the request is allowed
	Allowed bool `json:"allowed"`
	
	// Limit is the configured limit
	Limit int `json:"limit"`
	
	// Remaining is the number of requests remaining in the current window
	Remaining int `json:"remaining"`
	
	// ResetTime is when the rate limit resets
	ResetTime time.Time `json:"reset_time"`
	
	// RetryAfter is how long to wait before retrying (if blocked)
	RetryAfter time.Duration `json:"retry_after"`
	
	// Blocked indicates if the user is currently blocked
	Blocked bool `json:"blocked"`
	
	// BlockedUntil is when the block expires
	BlockedUntil time.Time `json:"blocked_until"`
}

// Stats represents rate limiting statistics
type Stats struct {
	// Action being tracked
	Action Action `json:"action"`
	
	// UserID being tracked
	UserID uuid.UUID `json:"user_id"`
	
	// Current limit
	Limit int `json:"limit"`
	
	// Current usage
	Usage int `json:"usage"`
	
	// Remaining quota
	Remaining int `json:"remaining"`
	
	// Window start time
	WindowStart time.Time `json:"window_start"`
	
	// Window end time
	WindowEnd time.Time `json:"window_end"`
	
	// Total requests in current window
	TotalRequests int `json:"total_requests"`
	
	// Blocked requests in current window
	BlockedRequests int `json:"blocked_requests"`
	
	// Last request time
	LastRequest time.Time `json:"last_request"`
	
	// Is currently blocked
	IsBlocked bool `json:"is_blocked"`
	
	// Block expiry time
	BlockExpiry time.Time `json:"block_expiry"`
}

// Limiter defines the interface for rate limiters
type Limiter interface {
	// Allow checks if a single request is allowed
	Allow(ctx context.Context, userID uuid.UUID, action Action) (*Result, error)
	
	// AllowN checks if N requests are allowed
	AllowN(ctx context.Context, userID uuid.UUID, action Action, n int) (*Result, error)
	
	// Reserve reserves N tokens for future use
	Reserve(ctx context.Context, userID uuid.UUID, action Action, n int) (*Result, error)
	
	// Reset resets the rate limit for a user and action
	Reset(ctx context.Context, userID uuid.UUID, action Action) error
	
	// GetStats returns current statistics
	GetStats(ctx context.Context, userID uuid.UUID, action Action) (*Stats, error)
	
	// Block manually blocks a user for a specific action
	Block(ctx context.Context, userID uuid.UUID, action Action, duration time.Duration) error
	
	// Unblock manually unblocks a user for a specific action
	Unblock(ctx context.Context, userID uuid.UUID, action Action) error
	
	// IsBlocked checks if a user is currently blocked
	IsBlocked(ctx context.Context, userID uuid.UUID, action Action) (bool, time.Time, error)
}

// Manager defines the interface for rate limiting managers
type Manager interface {
	// GetLimiter returns a limiter for a specific action and tier
	GetLimiter(action Action, tier Tier) Limiter
	
	// Allow checks if a request is allowed with automatic tier detection
	Allow(ctx context.Context, userID uuid.UUID, action Action, tier Tier) (*Result, error)
	
	// AllowN checks if N requests are allowed with automatic tier detection
	AllowN(ctx context.Context, userID uuid.UUID, action Action, tier Tier, n int) (*Result, error)
	
	// GetUserStats returns comprehensive statistics for a user
	GetUserStats(ctx context.Context, userID uuid.UUID) (map[Action]*Stats, error)
	
	// GetActionStats returns statistics for all users of a specific action
	GetActionStats(ctx context.Context, action Action) ([]*Stats, error)
	
	// GetGlobalStats returns global rate limiting statistics
	GetGlobalStats(ctx context.Context) (map[Action]map[Tier]int, error)
	
	// UpdateConfig updates the configuration for a specific action and tier
	UpdateConfig(action Action, tier Tier, config Config) error
	
	// Cleanup removes expired rate limiting data
	Cleanup(ctx context.Context) error
	
	// Close closes the manager and cleans up resources
	Close() error
}

// Middleware defines the interface for rate limiting middleware
type Middleware interface {
	// HTTPMiddleware returns HTTP middleware for rate limiting
	HTTPMiddleware() func(next interface{}) interface{}
	
	// WebSocketMiddleware returns WebSocket middleware for rate limiting
	WebSocketMiddleware() func(next interface{}) interface{}
	
	// GRPCMiddleware returns gRPC middleware for rate limiting
	GRPCMiddleware() func(next interface{}) interface{}
}

// MetricsCollector defines the interface for collecting rate limiting metrics
type MetricsCollector interface {
	// RecordRequest records a rate limit request
	RecordRequest(action Action, tier Tier, allowed bool)
	
	// RecordBlock records a user being blocked
	RecordBlock(action Action, tier Tier, duration time.Duration)
	
	// RecordUnblock records a user being unblocked
	RecordUnblock(action Action, tier Tier)
	
	// UpdateCurrentLimits updates current limit metrics
	UpdateCurrentLimits(action Action, tier Tier, limit int, usage int)
	
	// RecordLatency records rate limiting check latency
	RecordLatency(action Action, tier Tier, duration time.Duration)
}

// Event represents a rate limiting event
type Event struct {
	// Type of event
	Type EventType `json:"type"`
	
	// Timestamp of the event
	Timestamp time.Time `json:"timestamp"`
	
	// UserID involved in the event
	UserID uuid.UUID `json:"user_id"`
	
	// Action being rate limited
	Action Action `json:"action"`
	
	// User tier
	Tier Tier `json:"tier"`
	
	// Result of the rate limit check
	Result *Result `json:"result,omitempty"`
	
	// Additional metadata
	Metadata map[string]interface{} `json:"metadata,omitempty"`
}

// EventType represents different types of rate limiting events
type EventType string

const (
	// EventTypeAllow represents an allowed request
	EventTypeAllow EventType = "allow"
	// EventTypeDeny represents a denied request
	EventTypeDeny EventType = "deny"
	// EventTypeBlock represents a user being blocked
	EventTypeBlock EventType = "block"
	// EventTypeUnblock represents a user being unblocked
	EventTypeUnblock EventType = "unblock"
	// EventTypeReset represents a rate limit being reset
	EventTypeReset EventType = "reset"
	// EventTypeConfigUpdate represents a configuration update
	EventTypeConfigUpdate EventType = "config_update"
)

// EventHandler defines the interface for handling rate limiting events
type EventHandler interface {
	// HandleEvent handles a rate limiting event
	HandleEvent(ctx context.Context, event *Event) error
}

// Error types for rate limiting
type Error struct {
	Code    string `json:"code"`
	Message string `json:"message"`
	Action  Action `json:"action"`
	UserID  string `json:"user_id"`
}

func (e *Error) Error() string {
	return e.Message
}

// Common error codes
const (
	ErrCodeRateLimitExceeded = "RATE_LIMIT_EXCEEDED"
	ErrCodeUserBlocked       = "USER_BLOCKED"
	ErrCodeInvalidConfig     = "INVALID_CONFIG"
	ErrCodeStorageError      = "STORAGE_ERROR"
	ErrCodeInternalError     = "INTERNAL_ERROR"
)
