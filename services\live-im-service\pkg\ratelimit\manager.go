/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package ratelimit

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

// RateLimitManager implements the Manager interface
type RateLimitManager struct {
	client           *redis.Client
	config           ManagerConfig
	limiters         map[string]Limiter
	limitersMutex    sync.RWMutex
	logger           *logrus.Logger
	metricsCollector MetricsCollector
	eventHandler     EventHandler
	cleanupTicker    *time.Ticker
	stopCleanup      chan struct{}
}

// NewRateLimitManager creates a new rate limiting manager
func NewRateLimitManager(
	client *redis.Client,
	config ManagerConfig,
	logger *logrus.Logger,
	metricsCollector MetricsCollector,
	eventHandler EventHandler,
) *RateLimitManager {
	if config.KeyPrefix == "" {
		config.KeyPrefix = "ratelimit"
	}
	if config.CleanupInterval == 0 {
		config.CleanupInterval = 5 * time.Minute
	}
	if config.CleanupBatchSize == 0 {
		config.CleanupBatchSize = 1000
	}

	manager := &RateLimitManager{
		client:           client,
		config:           config,
		limiters:         make(map[string]Limiter),
		logger:           logger,
		metricsCollector: metricsCollector,
		eventHandler:     eventHandler,
		stopCleanup:      make(chan struct{}),
	}

	// Start cleanup routine
	if config.CleanupInterval > 0 {
		manager.cleanupTicker = time.NewTicker(config.CleanupInterval)
		go manager.cleanupRoutine()
	}

	return manager
}

// GetLimiter returns a limiter for a specific action and tier
func (m *RateLimitManager) GetLimiter(action Action, tier Tier) Limiter {
	key := fmt.Sprintf("%s:%s", string(action), string(tier))

	m.limitersMutex.RLock()
	if limiter, exists := m.limiters[key]; exists {
		m.limitersMutex.RUnlock()
		return limiter
	}
	m.limitersMutex.RUnlock()

	// Create new limiter
	m.limitersMutex.Lock()
	defer m.limitersMutex.Unlock()

	// Double-check after acquiring write lock
	if limiter, exists := m.limiters[key]; exists {
		return limiter
	}

	// Get configuration for this action and tier
	config := m.getConfigForActionAndTier(action, tier)

	// Create limiter based on algorithm
	var limiter Limiter
	switch config.Algorithm {
	case AlgorithmTokenBucket:
		limiter = NewTokenBucketLimiter(
			m.client,
			config,
			action,
			tier,
			m.config.KeyPrefix,
			m.logger,
			m.metricsCollector,
		)
	case AlgorithmSlidingWindow:
		limiter = NewSlidingWindowLimiter(
			m.client,
			config,
			action,
			tier,
			m.config.KeyPrefix,
			m.logger,
			m.metricsCollector,
		)
	default:
		// Default to sliding window
		limiter = NewSlidingWindowLimiter(
			m.client,
			config,
			action,
			tier,
			m.config.KeyPrefix,
			m.logger,
			m.metricsCollector,
		)
	}

	m.limiters[key] = limiter
	return limiter
}

// Allow checks if a request is allowed with automatic tier detection
func (m *RateLimitManager) Allow(ctx context.Context, userID uuid.UUID, action Action, tier Tier) (*Result, error) {
	return m.AllowN(ctx, userID, action, tier, 1)
}

// AllowN checks if N requests are allowed with automatic tier detection
func (m *RateLimitManager) AllowN(ctx context.Context, userID uuid.UUID, action Action, tier Tier, n int) (*Result, error) {
	limiter := m.GetLimiter(action, tier)
	result, err := limiter.AllowN(ctx, userID, action, n)

	if err != nil {
		return nil, err
	}

	// Emit event
	if m.eventHandler != nil {
		eventType := EventTypeAllow
		if !result.Allowed {
			eventType = EventTypeDeny
		}

		event := &Event{
			Type:      eventType,
			Timestamp: time.Now(),
			UserID:    userID,
			Action:    action,
			Tier:      tier,
			Result:    result,
		}

		go func() {
			if eventErr := m.eventHandler.HandleEvent(ctx, event); eventErr != nil {
				m.logger.WithError(eventErr).Warn("Failed to handle rate limit event")
			}
		}()
	}

	return result, nil
}

// GetUserStats returns comprehensive statistics for a user
func (m *RateLimitManager) GetUserStats(ctx context.Context, userID uuid.UUID) (map[Action]*Stats, error) {
	stats := make(map[Action]*Stats)

	// Get stats for all configured actions
	for action := range m.config.Actions {
		// For now, use basic tier - in a real implementation, you'd determine the user's tier
		limiter := m.GetLimiter(action, TierBasic)
		actionStats, err := limiter.GetStats(ctx, userID, action)
		if err != nil {
			m.logger.WithError(err).WithFields(logrus.Fields{
				"user_id": userID.String(),
				"action":  string(action),
			}).Warn("Failed to get user stats for action")
			continue
		}
		stats[action] = actionStats
	}

	return stats, nil
}

// GetActionStats returns statistics for all users of a specific action
func (m *RateLimitManager) GetActionStats(ctx context.Context, action Action) ([]*Stats, error) {
	// This would require scanning Redis keys, which is expensive
	// In a production system, you'd want to maintain a separate index
	// For now, return empty slice
	return []*Stats{}, nil
}

// GetGlobalStats returns global rate limiting statistics
func (m *RateLimitManager) GetGlobalStats(ctx context.Context) (map[Action]map[Tier]int, error) {
	// This would require aggregating data from all users
	// In a production system, you'd want to maintain separate counters
	// For now, return empty map
	return make(map[Action]map[Tier]int), nil
}

// UpdateConfig updates the configuration for a specific action and tier
func (m *RateLimitManager) UpdateConfig(action Action, tier Tier, config Config) error {
	// Update in-memory config
	if _, exists := m.config.Actions[action]; !exists {
		m.config.Actions[action] = ActionConfig{
			Base:  config,
			Tiers: make(map[Tier]TierConfig),
		}
	}

	actionConfig := m.config.Actions[action]
	if _, exists := actionConfig.Tiers[tier]; !exists {
		actionConfig.Tiers[tier] = TierConfig{
			Multiplier: 1.0,
			Overrides:  make(map[Action]Config),
		}
	}

	tierConfig := actionConfig.Tiers[tier]
	tierConfig.Overrides[action] = config
	actionConfig.Tiers[tier] = tierConfig
	m.config.Actions[action] = actionConfig

	// Remove existing limiter to force recreation with new config
	key := fmt.Sprintf("%s:%s", string(action), string(tier))
	m.limitersMutex.Lock()
	delete(m.limiters, key)
	m.limitersMutex.Unlock()

	m.logger.WithFields(logrus.Fields{
		"action": string(action),
		"tier":   string(tier),
		"config": config,
	}).Info("Rate limit configuration updated")

	return nil
}

// Cleanup removes expired rate limiting data
func (m *RateLimitManager) Cleanup(ctx context.Context) error {
	// This is a simplified cleanup - in production you'd want more sophisticated cleanup
	pattern := fmt.Sprintf("%s:*", m.config.KeyPrefix)
	
	iter := m.client.Scan(ctx, 0, pattern, int64(m.config.CleanupBatchSize)).Iterator()
	keysToCheck := make([]string, 0, m.config.CleanupBatchSize)
	
	for iter.Next(ctx) {
		keysToCheck = append(keysToCheck, iter.Val())
		
		if len(keysToCheck) >= m.config.CleanupBatchSize {
			m.cleanupKeys(ctx, keysToCheck)
			keysToCheck = keysToCheck[:0]
		}
	}
	
	if len(keysToCheck) > 0 {
		m.cleanupKeys(ctx, keysToCheck)
	}
	
	return iter.Err()
}

// Close closes the manager and cleans up resources
func (m *RateLimitManager) Close() error {
	if m.cleanupTicker != nil {
		m.cleanupTicker.Stop()
	}
	
	close(m.stopCleanup)
	
	m.logger.Info("Rate limit manager closed")
	return nil
}

// Helper methods

func (m *RateLimitManager) getConfigForActionAndTier(action Action, tier Tier) Config {
	// Start with default config
	config := m.config.Default

	// Apply action-specific config if available
	if actionConfig, exists := m.config.Actions[action]; exists {
		// Apply base action config
		if actionConfig.Base.Enabled {
			config = actionConfig.Base
		}

		// Apply tier-specific config if available
		if tierConfig, exists := actionConfig.Tiers[tier]; exists {
			// Apply multiplier to limits
			if tierConfig.Multiplier != 0 {
				config.Limit = int(float64(config.Limit) * tierConfig.Multiplier)
				config.Burst = int(float64(config.Burst) * tierConfig.Multiplier)
			}

			// Apply tier-specific overrides
			if override, exists := tierConfig.Overrides[action]; exists {
				config = override
			}
		}
	}

	return config
}

func (m *RateLimitManager) cleanupKeys(ctx context.Context, keys []string) {
	pipe := m.client.Pipeline()
	
	for _, key := range keys {
		// Check TTL and remove keys that should have expired
		pipe.TTL(ctx, key)
	}
	
	results, err := pipe.Exec(ctx)
	if err != nil {
		m.logger.WithError(err).Warn("Failed to check TTL for cleanup")
		return
	}
	
	keysToDelete := make([]string, 0)
	for i, result := range results {
		if ttlCmd, ok := result.(*redis.DurationCmd); ok {
			ttl := ttlCmd.Val()
			if ttl == -1 { // Key exists but has no expiration
				// For rate limiting keys, this might indicate a problem
				// We could set a default expiration or investigate further
				continue
			} else if ttl == -2 { // Key doesn't exist
				continue
			} else if ttl < time.Minute { // Key expires soon, let it expire naturally
				continue
			}
		}
		
		// If we can't determine TTL or there's an issue, mark for potential cleanup
		// This is conservative - in production you'd want more sophisticated logic
		if len(keysToDelete) < 100 { // Limit batch deletions
			keysToDelete = append(keysToDelete, keys[i])
		}
	}
	
	if len(keysToDelete) > 0 {
		m.logger.WithField("count", len(keysToDelete)).Debug("Cleaning up rate limit keys")
		// Note: Be very careful with bulk deletions in production
		// This is a simplified example
	}
}

func (m *RateLimitManager) cleanupRoutine() {
	for {
		select {
		case <-m.cleanupTicker.C:
			ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
			if err := m.Cleanup(ctx); err != nil {
				m.logger.WithError(err).Warn("Rate limit cleanup failed")
			}
			cancel()
		case <-m.stopCleanup:
			return
		}
	}
}
