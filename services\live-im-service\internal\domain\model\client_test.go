/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:12:13
Modified: 2025-07-11 10:12:13
*/

package model

import (
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// MockHub implements the Hub interface for testing
type MockHub struct {
	clients         map[string]*Client
	registrations   []*Client
	unregistrations []*Client
	broadcasts      []BroadcastMessage
}

func NewMockHub() *MockHub {
	return &MockHub{
		clients:         make(map[string]*Client),
		registrations:   make([]*Client, 0),
		unregistrations: make([]*Client, 0),
		broadcasts:      make([]BroadcastMessage, 0),
	}
}

func (m *MockHub) RegisterClient(client *Client) {
	m.registrations = append(m.registrations, client)
	m.clients[client.ID] = client
}

func (m *MockHub) UnregisterClient(client *Client) {
	m.unregistrations = append(m.unregistrations, client)
	delete(m.clients, client.ID)
}

func (m *MockHub) BroadcastToRoom(roomID string, message Message, excludeClient *Client) {
	m.broadcasts = append(m.broadcasts, BroadcastMessage{
		RoomID:  roomID,
		Message: message,
	})
}

func (m *MockHub) NotifyUserEvent(event UserEvent) {
	// Mock implementation
}

// BroadcastMessage represents a broadcast message for testing
type BroadcastMessage struct {
	RoomID  string
	Message Message
}

// TestUserEvent represents a user event for testing
type TestUserEvent struct {
	Type   string
	UserID uuid.UUID
	RoomID string
}

// Helper function to create a WebSocket connection for testing
func createTestWebSocketConnection(t *testing.T) (*websocket.Conn, *httptest.Server) {
	upgrader := websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool {
			return true
		},
	}

	server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		conn, err := upgrader.Upgrade(w, r, nil)
		require.NoError(t, err)
		defer conn.Close()

		// Keep connection alive for testing
		for {
			_, _, err := conn.ReadMessage()
			if err != nil {
				break
			}
		}
	}))

	// Convert http:// to ws://
	wsURL := "ws" + strings.TrimPrefix(server.URL, "http")
	conn, _, err := websocket.DefaultDialer.Dial(wsURL, nil)
	require.NoError(t, err)

	return conn, server
}

func TestNewClient(t *testing.T) {
	userID := uuid.New()
	username := "testuser"
	avatar := "avatar.jpg"
	conn, server := createTestWebSocketConnection(t)
	defer server.Close()
	defer conn.Close()

	req := httptest.NewRequest("GET", "/ws", nil)
	hub := NewMockHub()
	logger := logrus.New()

	client := NewClient(userID, username, avatar, conn, req, hub, logger)

	assert.NotEmpty(t, client.ID)
	assert.Equal(t, userID, client.UserID)
	assert.Equal(t, username, client.Username)
	assert.Equal(t, avatar, client.Avatar)
	assert.Equal(t, conn, client.Conn)
	assert.Equal(t, RoleViewer, client.Role) // Default role
	assert.Empty(t, client.RoomID)           // Not in any room initially
	assert.WithinDuration(t, time.Now(), client.ConnectedAt, time.Second)
}

func TestClientSetRole(t *testing.T) {
	userID := uuid.New()
	conn, server := createTestWebSocketConnection(t)
	defer server.Close()
	defer conn.Close()

	req := httptest.NewRequest("GET", "/ws", nil)
	hub := NewMockHub()
	logger := logrus.New()

	client := NewClient(userID, "testuser", "avatar.jpg", conn, req, hub, logger)

	// Test setting different roles
	roles := []UserRole{RoleViewer, RoleStreamer, RoleModerator, RoleAdmin, RoleVIP}
	for _, role := range roles {
		client.SetRole(role)
		assert.Equal(t, role, client.Role)
	}
}

func TestClientJoinRoom(t *testing.T) {
	userID := uuid.New()
	conn, server := createTestWebSocketConnection(t)
	defer server.Close()
	defer conn.Close()

	req := httptest.NewRequest("GET", "/ws", nil)
	hub := NewMockHub()
	logger := logrus.New()

	client := NewClient(userID, "testuser", "avatar.jpg", conn, req, hub, logger)

	roomID := "test-room"
	err := client.JoinRoom(roomID)

	assert.NoError(t, err)
	assert.Equal(t, roomID, client.RoomID)
}

func TestClientLeaveRoom(t *testing.T) {
	userID := uuid.New()
	conn, server := createTestWebSocketConnection(t)
	defer server.Close()
	defer conn.Close()

	req := httptest.NewRequest("GET", "/ws", nil)
	hub := NewMockHub()
	logger := logrus.New()

	client := NewClient(userID, "testuser", "avatar.jpg", conn, req, hub, logger)

	// Join a room first
	roomID := "test-room"
	err := client.JoinRoom(roomID)
	require.NoError(t, err)

	// Leave the room
	client.LeaveRoom()
	assert.Empty(t, client.RoomID)
}

func TestClientSendMessage(t *testing.T) {
	userID := uuid.New()
	conn, server := createTestWebSocketConnection(t)
	defer server.Close()
	defer conn.Close()

	req := httptest.NewRequest("GET", "/ws", nil)
	hub := NewMockHub()
	logger := logrus.New()

	client := NewClient(userID, "testuser", "avatar.jpg", conn, req, hub, logger)

	message := NewPingMessage()

	// Test sending message (should not block)
	err := client.SendMessage(message)
	assert.NoError(t, err)
}

func TestClientClose(t *testing.T) {
	userID := uuid.New()
	conn, server := createTestWebSocketConnection(t)
	defer server.Close()

	req := httptest.NewRequest("GET", "/ws", nil)
	hub := NewMockHub()
	logger := logrus.New()

	client := NewClient(userID, "testuser", "avatar.jpg", conn, req, hub, logger)

	// Close the client
	client.Close()

	// Verify connection is closed
	assert.False(t, client.IsActive)
}

func TestClientGetInfo(t *testing.T) {
	userID := uuid.New()
	username := "testuser"
	avatar := "avatar.jpg"
	conn, server := createTestWebSocketConnection(t)
	defer server.Close()
	defer conn.Close()

	req := httptest.NewRequest("GET", "/ws", nil)
	hub := NewMockHub()
	logger := logrus.New()

	client := NewClient(userID, username, avatar, conn, req, hub, logger)
	client.SetRole(RoleModerator)

	info := client.GetInfo()

	assert.Equal(t, userID, info.UserID)
	assert.Equal(t, username, info.Username)
	assert.Equal(t, avatar, info.Avatar)
	assert.Equal(t, RoleModerator, info.Role)
	assert.WithinDuration(t, time.Now(), info.ConnectedAt, time.Second)
}

func TestClientLastSeen(t *testing.T) {
	userID := uuid.New()
	conn, server := createTestWebSocketConnection(t)
	defer server.Close()
	defer conn.Close()

	req := httptest.NewRequest("GET", "/ws", nil)
	hub := NewMockHub()
	logger := logrus.New()

	client := NewClient(userID, "testuser", "avatar.jpg", conn, req, hub, logger)

	// Check that LastSeenAt is set during creation
	assert.WithinDuration(t, time.Now(), client.LastSeenAt, time.Second)
}

func TestClientBasicInfo(t *testing.T) {
	userID := uuid.New()
	username := "testuser"
	conn, server := createTestWebSocketConnection(t)
	defer server.Close()
	defer conn.Close()

	req := httptest.NewRequest("GET", "/ws", nil)
	hub := NewMockHub()
	logger := logrus.New()

	client := NewClient(userID, username, "avatar.jpg", conn, req, hub, logger)
	client.SetRole(RoleModerator)

	// Test basic client information
	assert.Equal(t, userID, client.UserID)
	assert.Equal(t, username, client.Username)
	assert.Equal(t, RoleModerator, client.Role)
}
