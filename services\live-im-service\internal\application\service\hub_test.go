/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:12:13
Modified: 2025-07-11 10:12:13
*/

package service

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	"cina.club/services/live-im-service/internal/application/port"
	"cina.club/services/live-im-service/internal/domain/model"
)

// MockBroadcaster implements the port.Broadcaster interface for testing
type MockBroadcaster struct {
	mock.Mock
}

func (m *MockBroadcaster) Broadcast(ctx context.Context, roomID string, message model.Message) error {
	args := m.Called(ctx, roomID, message)
	return args.Error(0)
}

func (m *MockBroadcaster) Subscribe(ctx context.Context, handler port.BroadcastHandler) error {
	args := m.Called(ctx, handler)
	return args.Error(0)
}

func (m *MockBroadcaster) Unsubscribe(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *MockBroadcaster) Close() error {
	args := m.Called()
	return args.Error(0)
}

// MockRoomStore implements the port.RoomStore interface for testing
type MockRoomStore struct {
	mock.Mock
}

func (m *MockRoomStore) Subscribe(ctx context.Context, userID uuid.UUID, roomID string) error {
	args := m.Called(ctx, userID, roomID)
	return args.Error(0)
}

func (m *MockRoomStore) Unsubscribe(ctx context.Context, userID uuid.UUID, roomID string) error {
	args := m.Called(ctx, userID, roomID)
	return args.Error(0)
}

func (m *MockRoomStore) GetRoomMembers(ctx context.Context, roomID string) ([]uuid.UUID, error) {
	args := m.Called(ctx, roomID)
	return args.Get(0).([]uuid.UUID), args.Error(1)
}

func (m *MockRoomStore) GetUserRooms(ctx context.Context, userID uuid.UUID) ([]string, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockRoomStore) IsUserInRoom(ctx context.Context, userID uuid.UUID, roomID string) (bool, error) {
	args := m.Called(ctx, userID, roomID)
	return args.Bool(0), args.Error(1)
}

func (m *MockRoomStore) GetRoomCount(ctx context.Context, roomID string) (int, error) {
	args := m.Called(ctx, roomID)
	return args.Int(0), args.Error(1)
}

// MockRateLimiter implements the port.RateLimiter interface for testing
type MockRateLimiter struct {
	mock.Mock
}

func (m *MockRateLimiter) Allow(ctx context.Context, userID uuid.UUID, action string) (bool, error) {
	args := m.Called(ctx, userID, action)
	return args.Bool(0), args.Error(1)
}

func (m *MockRateLimiter) AllowN(ctx context.Context, userID uuid.UUID, action string, n int) (bool, error) {
	args := m.Called(ctx, userID, action, n)
	return args.Bool(0), args.Error(1)
}

func (m *MockRateLimiter) Reset(ctx context.Context, userID uuid.UUID, action string) error {
	args := m.Called(ctx, userID, action)
	return args.Error(0)
}

func (m *MockRateLimiter) GetRemaining(ctx context.Context, userID uuid.UUID, action string) (int, error) {
	args := m.Called(ctx, userID, action)
	return args.Int(0), args.Error(1)
}

// MockAuthService implements the port.AuthService interface for testing
type MockAuthService struct {
	mock.Mock
}

func (m *MockAuthService) ValidateToken(ctx context.Context, token string) (*port.UserInfo, error) {
	args := m.Called(ctx, token)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.UserInfo), args.Error(1)
}

func (m *MockAuthService) CheckRoomPermission(ctx context.Context, userID uuid.UUID, roomID string) (bool, error) {
	args := m.Called(ctx, userID, roomID)
	return args.Bool(0), args.Error(1)
}

// Note: MockMessageProcessor already exists in message_processor.go

func TestNewHub(t *testing.T) {
	logger := logrus.New()
	mockBroadcaster := &MockBroadcaster{}
	mockRoomStore := &MockRoomStore{}
	mockRateLimiter := &MockRateLimiter{}
	mockAuthService := &MockAuthService{}
	mockMessageProcessor := &MockMessageProcessor{}

	hub := NewHub(
		nil, // Use default config
		mockBroadcaster,
		mockRoomStore,
		mockMessageProcessor,
		mockRateLimiter,
		mockAuthService,
		logger,
	)

	assert.NotNil(t, hub)
	assert.NotNil(t, hub.register)
	assert.NotNil(t, hub.unregister)
	assert.NotNil(t, hub.broadcast)
	assert.NotNil(t, hub.clients)
	assert.NotNil(t, hub.rooms)
}

func TestHubStart(t *testing.T) {
	logger := logrus.New()
	mockBroadcaster := &MockBroadcaster{}
	mockRoomStore := &MockRoomStore{}
	mockRateLimiter := &MockRateLimiter{}
	mockAuthService := &MockAuthService{}
	mockMessageProcessor := NewMockMessageProcessor(logger)

	hub := NewHub(
		nil, // Use default config
		mockBroadcaster,
		mockRoomStore,
		mockMessageProcessor,
		mockRateLimiter,
		mockAuthService,
		logger,
	)

	// Mock the Subscribe and Unsubscribe methods
	mockBroadcaster.On("Subscribe", mock.Anything, mock.Anything).Return(nil)
	mockBroadcaster.On("Unsubscribe", mock.Anything).Return(nil)

	// Start the hub
	go hub.Start()

	// Give it a moment to start
	time.Sleep(10 * time.Millisecond)

	// Stop the hub
	hub.Stop()

	// Verify methods were called
	mockBroadcaster.AssertExpectations(t)
}

func TestHubRegisterClient(t *testing.T) {
	logger := logrus.New()
	mockBroadcaster := &MockBroadcaster{}
	mockRoomStore := &MockRoomStore{}
	mockRateLimiter := &MockRateLimiter{}
	mockAuthService := &MockAuthService{}
	mockMessageProcessor := NewMockMessageProcessor(logger)

	hub := NewHub(
		nil, // Use default config
		mockBroadcaster,
		mockRoomStore,
		mockMessageProcessor,
		mockRateLimiter,
		mockAuthService,
		logger,
	)

	// Create a mock client
	userID := uuid.New()
	client := &model.Client{
		ID:     uuid.New().String(),
		UserID: userID,
		RoomID: "test-room",
	}

	// Register the client
	hub.RegisterClient(client)

	// Verify client is registered (check using GetStats since clients map is private)
	stats := hub.GetStats()
	assert.Equal(t, 1, stats.ConnectedClients)
}

func TestHubUnregisterClient(t *testing.T) {
	logger := logrus.New()
	mockBroadcaster := &MockBroadcaster{}
	mockRoomStore := &MockRoomStore{}
	mockRateLimiter := &MockRateLimiter{}
	mockAuthService := &MockAuthService{}
	mockMessageProcessor := NewMockMessageProcessor(logger)

	hub := NewHub(
		nil, // Use default config
		mockBroadcaster,
		mockRoomStore,
		mockMessageProcessor,
		mockRateLimiter,
		mockAuthService,
		logger,
	)

	// Create and register a mock client
	userID := uuid.New()
	client := &model.Client{
		ID:     uuid.New().String(),
		UserID: userID,
		RoomID: "test-room",
	}

	hub.RegisterClient(client)
	stats := hub.GetStats()
	assert.Equal(t, 1, stats.ConnectedClients)

	// Mock the Unsubscribe method
	mockRoomStore.On("Unsubscribe", mock.Anything, userID, "test-room").Return(nil)

	// Unregister the client
	hub.UnregisterClient(client)

	// Verify client is unregistered
	stats = hub.GetStats()
	assert.Equal(t, 0, stats.ConnectedClients)

	// Verify Unsubscribe was called
	mockRoomStore.AssertExpectations(t)
}

func TestHubGetStats(t *testing.T) {
	logger := logrus.New()
	mockBroadcaster := &MockBroadcaster{}
	mockRoomStore := &MockRoomStore{}
	mockRateLimiter := &MockRateLimiter{}
	mockAuthService := &MockAuthService{}
	mockMessageProcessor := NewMockMessageProcessor(logger)

	hub := NewHub(
		nil, // Use default config
		mockBroadcaster,
		mockRoomStore,
		mockMessageProcessor,
		mockRateLimiter,
		mockAuthService,
		logger,
	)

	// Register some clients
	for i := 0; i < 3; i++ {
		client := &model.Client{
			ID:     uuid.New().String(),
			UserID: uuid.New(),
			RoomID: "test-room",
		}
		hub.RegisterClient(client)
	}

	stats := hub.GetStats()

	assert.Equal(t, 3, stats.ConnectedClients)
	assert.Equal(t, 1, stats.ActiveRooms)
	assert.False(t, stats.StartTime.IsZero())
}
