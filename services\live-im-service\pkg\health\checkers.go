/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package health

import (
	"context"
	"fmt"
	"net/http"
	"time"

	"github.com/go-redis/redis/v8"
	"github.com/sirupsen/logrus"

	"cina.club/services/live-im-service/internal/application/port"
	"cina.club/services/live-im-service/internal/application/service"
)

// RedisHealthChecker checks Redis connectivity and performance
type RedisHealthChecker struct {
	client   *redis.Client
	timeout  time.Duration
	critical bool
	logger   *logrus.Logger
}

// NewRedisHealthChecker creates a new Redis health checker
func NewRedisHealthChecker(client *redis.Client, timeout time.Duration, critical bool, logger *logrus.Logger) *RedisHealthChecker {
	if timeout == 0 {
		timeout = 5 * time.Second
	}
	return &RedisHealthChecker{
		client:   client,
		timeout:  timeout,
		critical: critical,
		logger:   logger,
	}
}

func (r *RedisHealthChecker) Name() string           { return "redis" }
func (r *<PERSON><PERSON><PERSON>eal<PERSON><PERSON><PERSON><PERSON>) Timeout() time.Duration { return r.timeout }
func (r *<PERSON>isHealthChecker) Critical() bool         { return r.critical }

func (r *RedisHealthChecker) Check(ctx context.Context) CheckResult {
	result := CheckResult{
		Name:    "redis",
		Details: make(map[string]interface{}),
	}

	// Test basic connectivity
	start := time.Now()
	pong, err := r.client.Ping(ctx).Result()
	pingDuration := time.Since(start)

	if err != nil {
		result.Status = StatusUnhealthy
		result.Message = "Redis connection failed"
		result.Error = err.Error()
		result.Details["ping_duration_ms"] = pingDuration.Milliseconds()
		return result
	}

	result.Details["ping_result"] = pong
	result.Details["ping_duration_ms"] = pingDuration.Milliseconds()

	// Test read/write operations
	testKey := fmt.Sprintf("health_check_%d", time.Now().UnixNano())
	testValue := "health_check_value"

	// SET operation
	start = time.Now()
	err = r.client.Set(ctx, testKey, testValue, time.Minute).Err()
	setDuration := time.Since(start)
	result.Details["set_duration_ms"] = setDuration.Milliseconds()

	if err != nil {
		result.Status = StatusDegraded
		result.Message = "Redis SET operation failed"
		result.Error = err.Error()
		return result
	}

	// GET operation
	start = time.Now()
	retrievedValue, err := r.client.Get(ctx, testKey).Result()
	getDuration := time.Since(start)
	result.Details["get_duration_ms"] = getDuration.Milliseconds()

	if err != nil {
		result.Status = StatusDegraded
		result.Message = "Redis GET operation failed"
		result.Error = err.Error()
		return result
	}

	if retrievedValue != testValue {
		result.Status = StatusDegraded
		result.Message = "Redis data integrity check failed"
		result.Details["expected"] = testValue
		result.Details["actual"] = retrievedValue
		return result
	}

	// Clean up test key
	r.client.Del(ctx, testKey)

	// Get Redis info
	info, err := r.client.Info(ctx, "server", "memory", "stats").Result()
	if err == nil {
		result.Details["redis_info"] = info
	}

	// Performance thresholds
	totalDuration := pingDuration + setDuration + getDuration
	if totalDuration > 100*time.Millisecond {
		result.Status = StatusDegraded
		result.Message = "Redis performance degraded"
	} else {
		result.Status = StatusHealthy
		result.Message = "Redis is healthy"
	}

	result.Details["total_duration_ms"] = totalDuration.Milliseconds()
	return result
}

// HubHealthChecker checks the Hub service health
type HubHealthChecker struct {
	hub      *service.Hub
	timeout  time.Duration
	critical bool
	logger   *logrus.Logger
}

// NewHubHealthChecker creates a new Hub health checker
func NewHubHealthChecker(hub *service.Hub, timeout time.Duration, critical bool, logger *logrus.Logger) *HubHealthChecker {
	if timeout == 0 {
		timeout = 3 * time.Second
	}
	return &HubHealthChecker{
		hub:      hub,
		timeout:  timeout,
		critical: critical,
		logger:   logger,
	}
}

func (h *HubHealthChecker) Name() string           { return "hub" }
func (h *HubHealthChecker) Timeout() time.Duration { return h.timeout }
func (h *HubHealthChecker) Critical() bool         { return h.critical }

func (h *HubHealthChecker) Check(ctx context.Context) CheckResult {
	result := CheckResult{
		Name:    "hub",
		Details: make(map[string]interface{}),
	}

	// Get Hub statistics
	stats := h.hub.GetStats()
	if stats == nil {
		result.Status = StatusUnhealthy
		result.Message = "Unable to retrieve Hub statistics"
		return result
	}

	result.Details["connected_clients"] = stats.ConnectedClients
	result.Details["active_rooms"] = stats.ActiveRooms
	result.Details["total_messages"] = stats.TotalMessages
	result.Details["total_broadcasts"] = stats.TotalBroadcasts
	result.Details["messages_per_second"] = stats.MessagesPerSecond
	result.Details["broadcasts_per_second"] = stats.BroadcastsPerSecond
	result.Details["uptime_seconds"] = time.Since(stats.StartTime).Seconds()

	// Check if Hub is responsive
	clientCount := h.hub.GetConnectedClientsCount()
	result.Details["client_count_check"] = clientCount

	// Health thresholds
	if stats.ConnectedClients < 0 {
		result.Status = StatusUnhealthy
		result.Message = "Hub statistics are invalid"
		return result
	}

	// Check for performance issues
	if stats.MessagesPerSecond > 10000 {
		result.Status = StatusDegraded
		result.Message = "Hub is under high load"
	} else {
		result.Status = StatusHealthy
		result.Message = "Hub is healthy"
	}

	return result
}

// HTTPServiceHealthChecker checks external HTTP services
type HTTPServiceHealthChecker struct {
	name     string
	url      string
	timeout  time.Duration
	critical bool
	client   *http.Client
	logger   *logrus.Logger
}

// NewHTTPServiceHealthChecker creates a new HTTP service health checker
func NewHTTPServiceHealthChecker(name, url string, timeout time.Duration, critical bool, logger *logrus.Logger) *HTTPServiceHealthChecker {
	if timeout == 0 {
		timeout = 10 * time.Second
	}

	client := &http.Client{
		Timeout: timeout,
	}

	return &HTTPServiceHealthChecker{
		name:     name,
		url:      url,
		timeout:  timeout,
		critical: critical,
		client:   client,
		logger:   logger,
	}
}

func (h *HTTPServiceHealthChecker) Name() string           { return h.name }
func (h *HTTPServiceHealthChecker) Timeout() time.Duration { return h.timeout }
func (h *HTTPServiceHealthChecker) Critical() bool         { return h.critical }

func (h *HTTPServiceHealthChecker) Check(ctx context.Context) CheckResult {
	result := CheckResult{
		Name:    h.name,
		Details: make(map[string]interface{}),
	}

	req, err := http.NewRequestWithContext(ctx, "GET", h.url, nil)
	if err != nil {
		result.Status = StatusUnhealthy
		result.Message = "Failed to create HTTP request"
		result.Error = err.Error()
		return result
	}

	start := time.Now()
	resp, err := h.client.Do(req)
	duration := time.Since(start)

	result.Details["url"] = h.url
	result.Details["duration_ms"] = duration.Milliseconds()

	if err != nil {
		result.Status = StatusUnhealthy
		result.Message = "HTTP request failed"
		result.Error = err.Error()
		return result
	}
	defer resp.Body.Close()

	result.Details["status_code"] = resp.StatusCode
	result.Details["headers"] = resp.Header

	if resp.StatusCode >= 200 && resp.StatusCode < 300 {
		result.Status = StatusHealthy
		result.Message = "Service is healthy"
	} else if resp.StatusCode >= 500 {
		result.Status = StatusUnhealthy
		result.Message = "Service returned server error"
	} else {
		result.Status = StatusDegraded
		result.Message = "Service returned non-success status"
	}

	// Performance check
	if duration > 5*time.Second {
		result.Status = StatusDegraded
		result.Message = "Service response time is slow"
	}

	return result
}

// AuthServiceHealthChecker checks the authentication service
type AuthServiceHealthChecker struct {
	authService port.AuthService
	timeout     time.Duration
	critical    bool
	logger      *logrus.Logger
}

// NewAuthServiceHealthChecker creates a new auth service health checker
func NewAuthServiceHealthChecker(authService port.AuthService, timeout time.Duration, critical bool, logger *logrus.Logger) *AuthServiceHealthChecker {
	if timeout == 0 {
		timeout = 5 * time.Second
	}
	return &AuthServiceHealthChecker{
		authService: authService,
		timeout:     timeout,
		critical:    critical,
		logger:      logger,
	}
}

func (a *AuthServiceHealthChecker) Name() string           { return "auth_service" }
func (a *AuthServiceHealthChecker) Timeout() time.Duration { return a.timeout }
func (a *AuthServiceHealthChecker) Critical() bool         { return a.critical }

func (a *AuthServiceHealthChecker) Check(ctx context.Context) CheckResult {
	result := CheckResult{
		Name:    "auth_service",
		Details: make(map[string]interface{}),
	}

	// Test with a dummy token to check service responsiveness
	start := time.Now()
	_, err := a.authService.ValidateToken(ctx, "health_check_dummy_token")
	duration := time.Since(start)

	result.Details["duration_ms"] = duration.Milliseconds()

	// We expect this to fail with invalid token, but service should respond
	if err != nil {
		// Check if it's a service error or expected validation error
		if duration > a.timeout/2 {
			result.Status = StatusDegraded
			result.Message = "Auth service is slow to respond"
		} else {
			result.Status = StatusHealthy
			result.Message = "Auth service is responsive"
		}
	} else {
		// Unexpected success with dummy token
		result.Status = StatusDegraded
		result.Message = "Auth service validation may be compromised"
	}

	return result
}

// ApplicationHealthChecker checks the application itself
type ApplicationHealthChecker struct {
	startTime time.Time
	version   string
	timeout   time.Duration
	critical  bool
	logger    *logrus.Logger
}

// NewApplicationHealthChecker creates a new application health checker
func NewApplicationHealthChecker(startTime time.Time, version string, timeout time.Duration, critical bool, logger *logrus.Logger) *ApplicationHealthChecker {
	if timeout == 0 {
		timeout = 1 * time.Second
	}
	return &ApplicationHealthChecker{
		startTime: startTime,
		version:   version,
		timeout:   timeout,
		critical:  critical,
		logger:    logger,
	}
}

func (a *ApplicationHealthChecker) Name() string           { return "application" }
func (a *ApplicationHealthChecker) Timeout() time.Duration { return a.timeout }
func (a *ApplicationHealthChecker) Critical() bool         { return a.critical }

func (a *ApplicationHealthChecker) Check(ctx context.Context) CheckResult {
	result := CheckResult{
		Name:    "application",
		Details: make(map[string]interface{}),
	}

	uptime := time.Since(a.startTime)
	result.Details["uptime_seconds"] = uptime.Seconds()
	result.Details["uptime_human"] = uptime.String()
	result.Details["start_time"] = a.startTime.Format(time.RFC3339)
	result.Details["version"] = a.version
	result.Details["go_version"] = "go1.21+"

	// Check if application has been running long enough to be considered stable
	if uptime > 30*time.Second {
		result.Status = StatusHealthy
		result.Message = "Application is healthy and stable"
	} else if uptime > 5*time.Second {
		result.Status = StatusDegraded
		result.Message = "Application is starting up"
	} else {
		result.Status = StatusDegraded
		result.Message = "Application just started"
	}

	return result
}

// DatabaseHealthChecker checks database connectivity (if applicable)
type DatabaseHealthChecker struct {
	name     string
	pingFunc func(ctx context.Context) error
	timeout  time.Duration
	critical bool
	logger   *logrus.Logger
}

// NewDatabaseHealthChecker creates a new database health checker
func NewDatabaseHealthChecker(name string, pingFunc func(ctx context.Context) error, timeout time.Duration, critical bool, logger *logrus.Logger) *DatabaseHealthChecker {
	if timeout == 0 {
		timeout = 5 * time.Second
	}
	return &DatabaseHealthChecker{
		name:     name,
		pingFunc: pingFunc,
		timeout:  timeout,
		critical: critical,
		logger:   logger,
	}
}

func (d *DatabaseHealthChecker) Name() string           { return d.name }
func (d *DatabaseHealthChecker) Timeout() time.Duration { return d.timeout }
func (d *DatabaseHealthChecker) Critical() bool         { return d.critical }

func (d *DatabaseHealthChecker) Check(ctx context.Context) CheckResult {
	result := CheckResult{
		Name:    d.name,
		Details: make(map[string]interface{}),
	}

	start := time.Now()
	err := d.pingFunc(ctx)
	duration := time.Since(start)

	result.Details["duration_ms"] = duration.Milliseconds()

	if err != nil {
		result.Status = StatusUnhealthy
		result.Message = "Database connection failed"
		result.Error = err.Error()
		return result
	}

	if duration > 1*time.Second {
		result.Status = StatusDegraded
		result.Message = "Database response is slow"
	} else {
		result.Status = StatusHealthy
		result.Message = "Database is healthy"
	}

	return result
}
