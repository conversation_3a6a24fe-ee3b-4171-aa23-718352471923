/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:12:13
Modified: 2025-07-11 10:12:13
*/

package redis

import (
	"context"
	"fmt"

	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"cina.club/services/live-im-service/internal/application/port"
)

const (
	// Redis key patterns
	RoomMembersKeyPrefix = "live:im:room_members:" // live:im:room_members:{roomId}
	UserRoomsKeyPrefix   = "live:im:user_rooms:"   // live:im:user_rooms:{userId}
)

// RoomStore implements the port.RoomStore interface using Redis.
type RoomStore struct {
	client *redis.Client
	logger *logrus.Entry
}

// NewRoomStore creates a new Redis room store.
func NewRoomStore(client *redis.Client, logger *logrus.Logger) port.RoomStore {
	return &RoomStore{
		client: client,
		logger: logger.WithField("component", "redis_room_store"),
	}
}

// Subscribe adds a user to a room.
func (r *RoomStore) Subscribe(ctx context.Context, userID uuid.UUID, roomID string) error {
	userIDStr := userID.String()
	roomMembersKey := RoomMembersKeyPrefix + roomID
	userRoomsKey := UserRoomsKeyPrefix + userIDStr

	// Use pipeline for atomic operations
	pipe := r.client.Pipeline()

	// Add user to room members set
	pipe.SAdd(ctx, roomMembersKey, userIDStr)

	// Add room to user's rooms set
	pipe.SAdd(ctx, userRoomsKey, roomID)

	// Execute pipeline
	if _, err := pipe.Exec(ctx); err != nil {
		return fmt.Errorf("failed to subscribe user %s to room %s: %w", userIDStr, roomID, err)
	}

	r.logger.WithFields(logrus.Fields{
		"user_id": userIDStr,
		"room_id": roomID,
	}).Debug("User subscribed to room")

	return nil
}

// Unsubscribe removes a user from a room.
func (r *RoomStore) Unsubscribe(ctx context.Context, userID uuid.UUID, roomID string) error {
	userIDStr := userID.String()
	roomMembersKey := RoomMembersKeyPrefix + roomID
	userRoomsKey := UserRoomsKeyPrefix + userIDStr

	// Use pipeline for atomic operations
	pipe := r.client.Pipeline()

	// Remove user from room members set
	pipe.SRem(ctx, roomMembersKey, userIDStr)

	// Remove room from user's rooms set
	pipe.SRem(ctx, userRoomsKey, roomID)

	// Execute pipeline
	if _, err := pipe.Exec(ctx); err != nil {
		return fmt.Errorf("failed to unsubscribe user %s from room %s: %w", userIDStr, roomID, err)
	}

	r.logger.WithFields(logrus.Fields{
		"user_id": userIDStr,
		"room_id": roomID,
	}).Debug("User unsubscribed from room")

	return nil
}

// GetRoomMembers gets all members of a room.
func (r *RoomStore) GetRoomMembers(ctx context.Context, roomID string) ([]uuid.UUID, error) {
	roomMembersKey := RoomMembersKeyPrefix + roomID

	// Get all members from the set
	members, err := r.client.SMembers(ctx, roomMembersKey).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get room members for room %s: %w", roomID, err)
	}

	// Convert strings to UUIDs
	userIDs := make([]uuid.UUID, 0, len(members))
	for _, member := range members {
		userID, err := uuid.Parse(member)
		if err != nil {
			r.logger.WithError(err).WithField("user_id", member).Warn("Invalid user ID in room members")
			continue
		}
		userIDs = append(userIDs, userID)
	}

	return userIDs, nil
}

// GetUserRooms gets all rooms a user is subscribed to.
func (r *RoomStore) GetUserRooms(ctx context.Context, userID uuid.UUID) ([]string, error) {
	userIDStr := userID.String()
	userRoomsKey := UserRoomsKeyPrefix + userIDStr

	// Get all rooms from the set
	rooms, err := r.client.SMembers(ctx, userRoomsKey).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get user rooms for user %s: %w", userIDStr, err)
	}

	return rooms, nil
}

// IsUserInRoom checks if a user is in a room.
func (r *RoomStore) IsUserInRoom(ctx context.Context, userID uuid.UUID, roomID string) (bool, error) {
	userIDStr := userID.String()
	roomMembersKey := RoomMembersKeyPrefix + roomID

	// Check if user is a member of the room
	isMember, err := r.client.SIsMember(ctx, roomMembersKey, userIDStr).Result()
	if err != nil {
		return false, fmt.Errorf("failed to check if user %s is in room %s: %w", userIDStr, roomID, err)
	}

	return isMember, nil
}

// GetRoomCount gets the number of members in a room.
func (r *RoomStore) GetRoomCount(ctx context.Context, roomID string) (int, error) {
	roomMembersKey := RoomMembersKeyPrefix + roomID

	// Get the cardinality of the set
	count, err := r.client.SCard(ctx, roomMembersKey).Result()
	if err != nil {
		return 0, fmt.Errorf("failed to get room count for room %s: %w", roomID, err)
	}

	return int(count), nil
}

// CleanupExpiredRooms removes empty rooms (optional maintenance method).
func (r *RoomStore) CleanupExpiredRooms(ctx context.Context) error {
	// This is a maintenance method that can be called periodically
	// to clean up empty room sets

	// Get all room member keys
	pattern := RoomMembersKeyPrefix + "*"
	keys, err := r.client.Keys(ctx, pattern).Result()
	if err != nil {
		return fmt.Errorf("failed to get room keys: %w", err)
	}

	// Check each room and remove if empty
	for _, key := range keys {
		count, err := r.client.SCard(ctx, key).Result()
		if err != nil {
			r.logger.WithError(err).WithField("key", key).Warn("Failed to get cardinality")
			continue
		}

		if count == 0 {
			if err := r.client.Del(ctx, key).Err(); err != nil {
				r.logger.WithError(err).WithField("key", key).Warn("Failed to delete empty room")
			} else {
				roomID := key[len(RoomMembersKeyPrefix):]
				r.logger.WithField("room_id", roomID).Debug("Cleaned up empty room")
			}
		}
	}

	return nil
}

// GetRoomStats gets statistics for all rooms (optional method for monitoring).
func (r *RoomStore) GetRoomStats(ctx context.Context) (map[string]int, error) {
	// Get all room member keys
	pattern := RoomMembersKeyPrefix + "*"
	keys, err := r.client.Keys(ctx, pattern).Result()
	if err != nil {
		return nil, fmt.Errorf("failed to get room keys: %w", err)
	}

	stats := make(map[string]int)

	// Get count for each room
	for _, key := range keys {
		count, err := r.client.SCard(ctx, key).Result()
		if err != nil {
			r.logger.WithError(err).WithField("key", key).Warn("Failed to get cardinality")
			continue
		}

		roomID := key[len(RoomMembersKeyPrefix):]
		stats[roomID] = int(count)
	}

	return stats, nil
}
