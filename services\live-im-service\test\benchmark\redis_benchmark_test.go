/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package benchmark

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/redis/go-redis/v9"

	"cina.club/services/live-im-service/internal/adapter/serializer"
	"cina.club/services/live-im-service/internal/domain/model"
)

// setupRedisClient creates a Redis client for benchmarking
func setupRedisClient(b *testing.B) *redis.Client {
	client := redis.NewClient(&redis.Options{
		Addr:     "localhost:6379",
		Password: "",
		DB:       15, // Use test database
	})

	// Test connectivity
	ctx := context.Background()
	err := client.Ping(ctx).Err()
	if err != nil {
		b.Skipf("Redis not available for benchmarking: %v", err)
	}

	// Clean up any existing test data
	client.FlushDB(ctx)

	return client
}

// BenchmarkRedisBasicOperations benchmarks basic Redis operations
func BenchmarkRedisBasicOperations(b *testing.B) {
	if testing.Short() {
		b.<PERSON>p("Skipping Redis benchmark in short mode")
	}

	client := setupRedisClient(b)
	defer client.Close()

	ctx := context.Background()

	b.Run("SET", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			key := "bench:set:" + uuid.New().String()
			err := client.Set(ctx, key, "benchmark value", time.Minute).Err()
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("GET", func(b *testing.B) {
		// Pre-populate keys
		keys := make([]string, b.N)
		for i := 0; i < b.N; i++ {
			keys[i] = "bench:get:" + uuid.New().String()
			client.Set(ctx, keys[i], "benchmark value", time.Minute)
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := client.Get(ctx, keys[i]).Result()
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("INCR", func(b *testing.B) {
		key := "bench:incr:" + uuid.New().String()
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := client.Incr(ctx, key).Result()
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("DEL", func(b *testing.B) {
		// Pre-populate keys
		keys := make([]string, b.N)
		for i := 0; i < b.N; i++ {
			keys[i] = "bench:del:" + uuid.New().String()
			client.Set(ctx, keys[i], "benchmark value", time.Minute)
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			err := client.Del(ctx, keys[i]).Err()
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

// BenchmarkRedisSetOperations benchmarks Redis set operations for room management
func BenchmarkRedisSetOperations(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping Redis benchmark in short mode")
	}

	client := setupRedisClient(b)
	defer client.Close()

	ctx := context.Background()
	roomKey := "bench:room:" + uuid.New().String()

	b.Run("SADD", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			userID := uuid.New().String()
			err := client.SAdd(ctx, roomKey, userID).Err()
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("SISMEMBER", func(b *testing.B) {
		// Pre-populate set
		userIDs := make([]string, b.N)
		for i := 0; i < b.N; i++ {
			userIDs[i] = uuid.New().String()
			client.SAdd(ctx, roomKey+"_ismember", userIDs[i])
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := client.SIsMember(ctx, roomKey+"_ismember", userIDs[i]).Result()
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("SCARD", func(b *testing.B) {
		// Pre-populate set
		setKey := roomKey + "_card"
		for i := 0; i < 1000; i++ {
			client.SAdd(ctx, setKey, uuid.New().String())
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := client.SCard(ctx, setKey).Result()
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("SMEMBERS", func(b *testing.B) {
		// Pre-populate set with 100 members
		setKey := roomKey + "_members"
		for i := 0; i < 100; i++ {
			client.SAdd(ctx, setKey, uuid.New().String())
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := client.SMembers(ctx, setKey).Result()
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("SREM", func(b *testing.B) {
		// Pre-populate set
		setKey := roomKey + "_rem"
		userIDs := make([]string, b.N)
		for i := 0; i < b.N; i++ {
			userIDs[i] = uuid.New().String()
			client.SAdd(ctx, setKey, userIDs[i])
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			err := client.SRem(ctx, setKey, userIDs[i]).Err()
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

// BenchmarkRedisPubSub benchmarks Redis pub/sub operations
func BenchmarkRedisPubSub(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping Redis benchmark in short mode")
	}

	client := setupRedisClient(b)
	defer client.Close()

	ctx := context.Background()
	channel := "bench:channel:" + uuid.New().String()

	b.Run("PUBLISH", func(b *testing.B) {
		message := "Benchmark message content for pub/sub testing"
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			err := client.Publish(ctx, channel, message).Err()
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("SUBSCRIBE", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			pubsub := client.Subscribe(ctx, channel+string(rune(i)))
			pubsub.Close()
		}
	})
}

// BenchmarkRedisMessageSerialization benchmarks message serialization with Redis
func BenchmarkRedisMessageSerialization(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping Redis benchmark in short mode")
	}

	client := setupRedisClient(b)
	defer client.Close()

	ctx := context.Background()
	serializer := serializer.NewJSONSerializer()

	user := &model.User{
		UserID:   uuid.New(),
		Username: "benchuser",
		Role:     model.RoleViewer,
		Level:    5,
		VIPLevel: 2,
		Badges:   []string{"active", "supporter"},
	}

	msg := model.NewBarrageMessage("Redis benchmark message", "room-123", user)

	b.Run("SerializeAndStore", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			data, err := serializer.SerializeMessage(msg)
			if err != nil {
				b.Fatal(err)
			}

			key := "bench:msg:" + uuid.New().String()
			err = client.Set(ctx, key, data, time.Minute).Err()
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("RetrieveAndDeserialize", func(b *testing.B) {
		// Pre-store serialized messages
		keys := make([]string, b.N)
		data, _ := serializer.SerializeMessage(msg)
		for i := 0; i < b.N; i++ {
			keys[i] = "bench:retrieve:" + uuid.New().String()
			client.Set(ctx, keys[i], data, time.Minute)
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			data, err := client.Get(ctx, keys[i]).Bytes()
			if err != nil {
				b.Fatal(err)
			}

			_, err = serializer.DeserializeMessage(data)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

// BenchmarkRedisRateLimiting benchmarks rate limiting operations
func BenchmarkRedisRateLimiting(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping Redis benchmark in short mode")
	}

	client := setupRedisClient(b)
	defer client.Close()

	ctx := context.Background()

	b.Run("RateLimitCheck", func(b *testing.B) {
		userID := uuid.New().String()
		key := "rate_limit:" + userID + ":barrage"

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			// Simulate rate limit check: increment and check TTL
			count, err := client.Incr(ctx, key).Result()
			if err != nil {
				b.Fatal(err)
			}

			if count == 1 {
				// Set TTL on first increment
				client.Expire(ctx, key, time.Minute)
			}

			// Check if rate limited (example: max 10 per minute)
			_ = count <= 10
		}
	})

	b.Run("RateLimitReset", func(b *testing.B) {
		// Pre-populate rate limit keys
		keys := make([]string, b.N)
		for i := 0; i < b.N; i++ {
			userID := uuid.New().String()
			keys[i] = "rate_limit:" + userID + ":reset"
			client.Set(ctx, keys[i], "10", time.Minute)
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			err := client.Del(ctx, keys[i]).Err()
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

// BenchmarkRedisConcurrentOperations benchmarks concurrent Redis operations
func BenchmarkRedisConcurrentOperations(b *testing.B) {
	if testing.Short() {
		b.Skip("Skipping Redis benchmark in short mode")
	}

	client := setupRedisClient(b)
	defer client.Close()

	ctx := context.Background()

	b.Run("ConcurrentSET", func(b *testing.B) {
		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				key := "bench:concurrent:set:" + uuid.New().String()
				err := client.Set(ctx, key, "concurrent value", time.Minute).Err()
				if err != nil {
					b.Error(err)
				}
			}
		})
	})

	b.Run("ConcurrentSADD", func(b *testing.B) {
		setKey := "bench:concurrent:set:" + uuid.New().String()
		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				userID := uuid.New().String()
				err := client.SAdd(ctx, setKey, userID).Err()
				if err != nil {
					b.Error(err)
				}
			}
		})
	})

	b.Run("ConcurrentPUBLISH", func(b *testing.B) {
		channel := "bench:concurrent:channel:" + uuid.New().String()
		message := "Concurrent benchmark message"
		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				err := client.Publish(ctx, channel, message).Err()
				if err != nil {
					b.Error(err)
				}
			}
		})
	})
}
