/*
Copyright (c) 2025 Cina.Club
All rights reserved.
*/

package provider

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"

	"github.com/sirupsen/logrus"

	"cina.club/services/live-gateway-service/internal/application/port"
	"cina.club/services/live-gateway-service/internal/domain/model"
)

var (
	ErrJanusNotImplemented = errors.New("janus adapter is not fully implemented")
)

// JanusAdapter implements the MediaServerAdapter for Janus.
type JanusAdapter struct {
	client *http.Client
	config *JanusConfig
	logger *logrus.Logger
}

// NewJanusAdapter creates a new adapter for Janus.
func NewJanusAdapter(config interface{}, logger *logrus.Logger) (port.MediaServerAdapter, error) {
	return &JanusAdapter{
		logger: logger,
	}, nil
}

func (a *JanusAdapter) GetType() model.MediaServerType {
	return model.MediaServerTypeJanus
}

// ValidateWebhookRequest validates an incoming webhook request from Janus.
func (a *JanusAdapter) ValidateWebhookRequest(req *port.WebhookValidateRequest) error {
	// Janus doesn't require webhook validation
	return nil
}

// ParseWebhookRequest parses an incoming webhook request from Janus.
func (a *JanusAdapter) ParseWebhookRequest(req *port.WebhookParseRequest) (*port.WebhookRequest, error) {
	var rawData map[string]interface{}
	if err := json.Unmarshal(req.Body, &rawData); err != nil {
		a.logger.WithError(err).Error("Failed to unmarshal Janus webhook body")
		return nil, fmt.Errorf("invalid webhook body: %w", err)
	}

	eventType, err := a.mapEventType(rawData)
	if err != nil {
		a.logger.WithError(err).WithField("raw_data", rawData).Warn("Unknown Janus event type")
		return nil, err
	}

	streamKey, _ := rawData["stream"].(string)

	// Get server node ID, handle nil config gracefully
	serverNodeID := "janus-node"
	if a.config != nil {
		serverNodeID = a.config.NodeID
	}

	return &port.WebhookRequest{
		EventType:  eventType,
		StreamKey:  streamKey,
		ClientIP:   req.ClientIP,
		UserAgent:  req.UserAgent,
		ServerNode: serverNodeID,
		Timestamp:  req.Timestamp,
		RawData:    rawData,
	}, nil
}

func (a *JanusAdapter) GeneratePushURL(ctx context.Context, req *port.PushURLRequest) (*model.PushURL, error) {
	a.logger.Warn(ErrJanusNotImplemented.Error())
	return nil, ErrJanusNotImplemented
}

func (a *JanusAdapter) GeneratePlayURLs(ctx context.Context, req *port.RequestPlayURLsRequest) ([]*model.PlayURL, error) {
	a.logger.Warn(ErrJanusNotImplemented.Error())
	return nil, ErrJanusNotImplemented
}

func (a *JanusAdapter) GetStreamInfo(ctx context.Context, streamKey string) (*port.StreamInfo, error) {
	a.logger.Warn(ErrJanusNotImplemented.Error())
	return nil, ErrJanusNotImplemented
}

func (a *JanusAdapter) KickStream(ctx context.Context, streamKey string) error {
	a.logger.Warn(ErrJanusNotImplemented.Error())
	return ErrJanusNotImplemented
}

func (a *JanusAdapter) GetServerStats(ctx context.Context, nodeID string) (*model.NodeLoad, error) {
	a.logger.Warn(ErrJanusNotImplemented.Error())
	return nil, ErrJanusNotImplemented
}

func (a *JanusAdapter) HealthCheck(ctx context.Context) error {
	a.logger.Warn(ErrJanusNotImplemented.Error())
	return nil // Return nil to not fail the gateway's overall health check
}

// RespondWebhookSuccess sends a success response to a webhook.
func (a *JanusAdapter) RespondWebhookSuccess(req *port.WebhookResponseRequest) error {
	// Janus doesn't require a specific success response body,
	// so we just ensure the HTTP status code is set correctly by the caller.
	a.logger.Info("Responding to webhook with success")
	// The actual response is written by the caller using the http.ResponseWriter.
	return nil
}

// RespondWebhookFailure sends a failure response to a webhook.
func (a *JanusAdapter) RespondWebhookFailure(req *port.WebhookErrorRequest) error {
	a.logger.WithFields(logrus.Fields{
		"status_code": req.StatusCode,
		"message":     req.Message,
		"error_code":  req.ErrorCode,
	}).Warn("Responding to webhook with failure")
	return nil
}

func (a *JanusAdapter) mapEventType(data map[string]interface{}) (model.WebhookEventType, error) {
	if event, ok := data["type"].(string); ok {
		switch event {
		case "published":
			return model.WebhookEventTypePublish, nil
		case "unpublished":
			return model.WebhookEventTypeUnpublish, nil
		case "session_created":
			// Map session creation to publish event for WebRTC streams
			return model.WebhookEventTypePublish, nil
		case "session_destroyed":
			// Map session destruction to unpublish event for WebRTC streams
			return model.WebhookEventTypeUnpublish, nil
		case "sdp_offer", "ice_gathering_done", "webrtc_up":
			// Map WebRTC signaling events to publish (connection establishment)
			return model.WebhookEventTypePublish, nil
		case "hangup", "transport_closed":
			// Map disconnection events to unpublish
			return model.WebhookEventTypeUnpublish, nil
		case "media", "media_stats":
			// Map media events to DVR for stats/recording purposes
			return model.WebhookEventTypeDVR, nil
		case "kicked":
			return model.WebhookEventTypeKick, nil
		default:
			return "", fmt.Errorf("unknown janus event type: %s", event)
		}
	}
	return "", errors.New("janus event type not found in webhook data")
}

func (a *JanusAdapter) extractData(data map[string]interface{}, eventType model.WebhookEventType) map[string]interface{} {
	return data
}
