# Live IM Service Performance Benchmarks

This directory contains comprehensive performance benchmarks for the Live IM Service components.

## Benchmark Categories

### 1. Message Benchmarks (`message_benchmark_test.go`)
- **Message Creation**: Tests performance of creating different message types
- **Message Cloning**: Tests message cloning performance for broadcasting
- **User Operations**: Tests user badge management and role checking
- **Room Operations**: Tests room creation and management
- **Client Operations**: Tests client creation and management
- **Concurrent Operations**: Tests thread-safe message creation
- **Message Validation**: Tests message validation performance
- **UUID Generation**: Tests UUID generation performance

### 2. Serializer Benchmarks (`serializer_benchmark_test.go`)
- **JSON Serialization**: Tests JSON serialization of different message types and sizes
- **JSON Deserialization**: Tests JSON deserialization performance
- **Round Trip**: Tests full serialize-deserialize cycles
- **Concurrent Serialization**: Tests thread-safe serialization
- **Memory Allocation**: Tests memory allocation patterns during serialization

### 3. Hub Benchmarks (`hub_benchmark_test.go`)
- **Client Operations**: Tests client registration/unregistration performance
- **Message Processing**: Tests message processing throughput
- **Concurrent Operations**: Tests concurrent client and message operations
- **Statistics**: Tests statistics collection performance

### 4. Redis Benchmarks (`redis_benchmark_test.go`)
- **Basic Operations**: Tests SET, GET, INCR, DEL operations
- **Set Operations**: Tests SADD, SISMEMBER, SCARD, SMEMBERS, SREM for room management
- **Pub/Sub Operations**: Tests PUBLISH and SUBSCRIBE operations
- **Message Serialization**: Tests Redis storage with message serialization
- **Rate Limiting**: Tests rate limiting operations
- **Concurrent Operations**: Tests concurrent Redis operations

## Running Benchmarks

### Run All Benchmarks
```bash
cd services/live-im-service
go test ./test/benchmark -bench=. -benchmem
```

### Run Specific Benchmark Categories
```bash
# Message benchmarks only
go test ./test/benchmark -bench=BenchmarkMessage -benchmem

# Serializer benchmarks only
go test ./test/benchmark -bench=BenchmarkJSON -benchmem

# Hub benchmarks only
go test ./test/benchmark -bench=BenchmarkHub -benchmem

# Redis benchmarks only (requires Redis server)
go test ./test/benchmark -bench=BenchmarkRedis -benchmem
```

### Run Specific Benchmarks
```bash
# Message creation benchmarks
go test ./test/benchmark -bench=BenchmarkMessageCreation -benchmem

# Concurrent operations
go test ./test/benchmark -bench=Concurrent -benchmem

# Memory allocation analysis
go test ./test/benchmark -bench=BenchmarkSerializationMemoryAllocation -benchmem
```

### Performance Profiling
```bash
# CPU profiling
go test ./test/benchmark -bench=BenchmarkHubMessageProcessing -cpuprofile=cpu.prof

# Memory profiling
go test ./test/benchmark -bench=BenchmarkSerializationMemoryAllocation -memprofile=mem.prof

# Analyze profiles
go tool pprof cpu.prof
go tool pprof mem.prof
```

## Benchmark Results Interpretation

### Key Metrics
- **ns/op**: Nanoseconds per operation (lower is better)
- **B/op**: Bytes allocated per operation (lower is better)
- **allocs/op**: Number of allocations per operation (lower is better)

### Performance Targets
Based on live streaming requirements:

#### Message Processing
- **Message Creation**: < 1,000 ns/op
- **Message Serialization**: < 10,000 ns/op
- **Message Validation**: < 500 ns/op

#### Hub Operations
- **Client Registration**: < 5,000 ns/op
- **Message Processing**: < 2,000 ns/op
- **Statistics Collection**: < 1,000 ns/op

#### Redis Operations
- **Basic Operations**: < 100,000 ns/op (network dependent)
- **Set Operations**: < 150,000 ns/op
- **Pub/Sub**: < 200,000 ns/op

#### Memory Allocation
- **Small Messages**: < 1,000 B/op, < 10 allocs/op
- **Medium Messages**: < 5,000 B/op, < 20 allocs/op
- **Large Messages**: < 50,000 B/op, < 50 allocs/op

## Prerequisites

### For Redis Benchmarks
1. Redis server running on `localhost:6379`
2. Redis accessible without authentication
3. Database 15 available for testing

### For All Benchmarks
1. Go 1.21 or later
2. All project dependencies installed (`go mod download`)

## Continuous Integration

### Benchmark Regression Testing
```bash
# Run benchmarks and save baseline
go test ./test/benchmark -bench=. -benchmem > baseline.txt

# Compare with previous results
go test ./test/benchmark -bench=. -benchmem > current.txt
benchcmp baseline.txt current.txt
```

### Performance Monitoring
- Run benchmarks on every major release
- Monitor for performance regressions > 20%
- Track memory allocation increases
- Validate concurrent operation performance

## Optimization Guidelines

### Message Processing
1. **Minimize allocations** in hot paths
2. **Reuse objects** where possible
3. **Optimize JSON serialization** for common message types
4. **Use object pools** for frequently created objects

### Hub Operations
1. **Batch operations** where possible
2. **Minimize lock contention** in concurrent scenarios
3. **Optimize statistics collection** frequency
4. **Use efficient data structures** for client/room management

### Redis Operations
1. **Pipeline operations** when possible
2. **Use appropriate data structures** (sets for membership, strings for counters)
3. **Optimize serialization** before Redis storage
4. **Monitor connection pool** performance

## Troubleshooting

### Common Issues
1. **Redis connection failures**: Ensure Redis is running and accessible
2. **High memory allocation**: Check for memory leaks or inefficient object creation
3. **Poor concurrent performance**: Look for lock contention or race conditions
4. **Network timeouts**: Adjust Redis client timeouts for benchmarking

### Debugging Performance Issues
1. Use `go test -bench=. -cpuprofile=cpu.prof` for CPU profiling
2. Use `go test -bench=. -memprofile=mem.prof` for memory profiling
3. Use `go test -bench=. -trace=trace.out` for execution tracing
4. Analyze with `go tool pprof` and `go tool trace`
