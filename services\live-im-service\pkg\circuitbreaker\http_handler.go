/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package circuitbreaker

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// HTTPHandler provides HTTP endpoints for circuit breaker management and monitoring
type HTTPHandler struct {
	manager *Manager
	logger  *logrus.Logger
}

// NewHTTPHandler creates a new HTTP handler for circuit breaker management
func NewHTTPHandler(manager *Manager, logger *logrus.Logger) *HTTPHandler {
	return &HTTPHandler{
		manager: manager,
		logger:  logger,
	}
}

// RegisterRoutes registers circuit breaker routes with a Gin router
func (h *HTTPHandler) RegisterRoutes(router *gin.RouterGroup) {
	cb := router.Group("/circuit-breakers")
	{
		cb.GET("/", h.GetAllStats)
		cb.GET("/:name", h.GetStats)
		cb.POST("/:name/reset", h.Reset)
		cb.GET("/health", h.<PERSON><PERSON>he<PERSON>)
	}
}

// GetAllStatsResponse represents the response for all circuit breaker stats
type GetAllStatsResponse struct {
	CircuitBreakers map[string]CircuitBreakerStats `json:"circuit_breakers"`
	Summary         StatsSummary                   `json:"summary"`
	Timestamp       string                         `json:"timestamp"`
}

// StatsSummary provides a summary of all circuit breakers
type StatsSummary struct {
	Total       int `json:"total"`
	Closed      int `json:"closed"`
	Open        int `json:"open"`
	HalfOpen    int `json:"half_open"`
	Healthy     int `json:"healthy"`
	Unhealthy   int `json:"unhealthy"`
}

// GetStatsResponse represents the response for a single circuit breaker stats
type GetStatsResponse struct {
	CircuitBreaker CircuitBreakerStats `json:"circuit_breaker"`
	Timestamp      string              `json:"timestamp"`
}

// ResetResponse represents the response for reset operation
type ResetResponse struct {
	Success   bool   `json:"success"`
	Message   string `json:"message"`
	Timestamp string `json:"timestamp"`
}

// HealthCheckResponse represents the health check response
type HealthCheckResponse struct {
	Status    string                         `json:"status"`
	Healthy   bool                           `json:"healthy"`
	Summary   StatsSummary                   `json:"summary"`
	Details   map[string]CircuitBreakerStats `json:"details,omitempty"`
	Timestamp string                         `json:"timestamp"`
}

// GetAllStats returns statistics for all circuit breakers
// @Summary Get all circuit breaker statistics
// @Description Returns statistics for all registered circuit breakers
// @Tags circuit-breakers
// @Accept json
// @Produce json
// @Success 200 {object} GetAllStatsResponse
// @Router /circuit-breakers [get]
func (h *HTTPHandler) GetAllStats(c *gin.Context) {
	stats := h.manager.GetStats()
	summary := h.calculateSummary(stats)

	response := GetAllStatsResponse{
		CircuitBreakers: stats,
		Summary:         summary,
		Timestamp:       time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

// GetStats returns statistics for a specific circuit breaker
// @Summary Get circuit breaker statistics
// @Description Returns statistics for a specific circuit breaker
// @Tags circuit-breakers
// @Accept json
// @Produce json
// @Param name path string true "Circuit breaker name"
// @Success 200 {object} GetStatsResponse
// @Failure 404 {object} gin.H
// @Router /circuit-breakers/{name} [get]
func (h *HTTPHandler) GetStats(c *gin.Context) {
	name := c.Param("name")
	
	breaker, exists := h.manager.GetBreaker(name)
	if !exists {
		c.JSON(http.StatusNotFound, gin.H{
			"error":     "Circuit breaker not found",
			"name":      name,
			"timestamp": time.Now().Format(time.RFC3339),
		})
		return
	}

	counts := breaker.Counts()
	stats := CircuitBreakerStats{
		Name:                 name,
		State:                breaker.State(),
		Requests:             counts.Requests,
		TotalSuccesses:       counts.TotalSuccesses,
		TotalFailures:        counts.TotalFailures,
		ConsecutiveSuccesses: counts.ConsecutiveSuccesses,
		ConsecutiveFailures:  counts.ConsecutiveFailures,
		SuccessRate:          calculateSuccessRate(counts),
	}

	response := GetStatsResponse{
		CircuitBreaker: stats,
		Timestamp:      time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

// Reset resets a specific circuit breaker
// @Summary Reset circuit breaker
// @Description Resets a specific circuit breaker to closed state
// @Tags circuit-breakers
// @Accept json
// @Produce json
// @Param name path string true "Circuit breaker name"
// @Success 200 {object} ResetResponse
// @Failure 404 {object} gin.H
// @Failure 500 {object} gin.H
// @Router /circuit-breakers/{name}/reset [post]
func (h *HTTPHandler) Reset(c *gin.Context) {
	name := c.Param("name")
	
	err := h.manager.Reset(name)
	if err != nil {
		h.logger.WithFields(logrus.Fields{
			"circuit_breaker": name,
			"error":           err.Error(),
		}).Error("Failed to reset circuit breaker")

		if err.Error() == "circuit breaker "+name+" not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"error":     "Circuit breaker not found",
				"name":      name,
				"timestamp": time.Now().Format(time.RFC3339),
			})
			return
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"error":     "Failed to reset circuit breaker",
			"details":   err.Error(),
			"timestamp": time.Now().Format(time.RFC3339),
		})
		return
	}

	h.logger.WithField("circuit_breaker", name).Info("Circuit breaker reset via HTTP API")

	response := ResetResponse{
		Success:   true,
		Message:   "Circuit breaker reset successfully",
		Timestamp: time.Now().Format(time.RFC3339),
	}

	c.JSON(http.StatusOK, response)
}

// HealthCheck provides a health check endpoint for circuit breakers
// @Summary Circuit breaker health check
// @Description Returns the overall health status of all circuit breakers
// @Tags circuit-breakers
// @Accept json
// @Produce json
// @Param details query bool false "Include detailed statistics"
// @Success 200 {object} HealthCheckResponse
// @Router /circuit-breakers/health [get]
func (h *HTTPHandler) HealthCheck(c *gin.Context) {
	stats := h.manager.GetStats()
	summary := h.calculateSummary(stats)
	
	// Consider system healthy if no circuit breakers are open
	healthy := summary.Open == 0
	status := "healthy"
	if !healthy {
		status = "unhealthy"
	}

	response := HealthCheckResponse{
		Status:    status,
		Healthy:   healthy,
		Summary:   summary,
		Timestamp: time.Now().Format(time.RFC3339),
	}

	// Include details if requested
	includeDetails := c.Query("details") == "true"
	if includeDetails {
		response.Details = stats
	}

	// Set appropriate HTTP status
	httpStatus := http.StatusOK
	if !healthy {
		httpStatus = http.StatusServiceUnavailable
	}

	c.JSON(httpStatus, response)
}

// calculateSummary calculates a summary of circuit breaker states
func (h *HTTPHandler) calculateSummary(stats map[string]CircuitBreakerStats) StatsSummary {
	summary := StatsSummary{
		Total: len(stats),
	}

	for _, stat := range stats {
		switch stat.State {
		case StateClosed:
			summary.Closed++
		case StateOpen:
			summary.Open++
		case StateHalfOpen:
			summary.HalfOpen++
		}

		// Consider healthy if closed or half-open with good success rate
		if stat.State == StateClosed || (stat.State == StateHalfOpen && stat.SuccessRate >= 0.8) {
			summary.Healthy++
		} else {
			summary.Unhealthy++
		}
	}

	return summary
}

// Middleware provides a Gin middleware that adds circuit breaker information to request context
func (h *HTTPHandler) Middleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Add circuit breaker manager to context for use in handlers
		c.Set("circuit_breaker_manager", h.manager)
		c.Next()
	}
}

// GetManagerFromContext extracts the circuit breaker manager from Gin context
func GetManagerFromContext(c *gin.Context) (*Manager, bool) {
	manager, exists := c.Get("circuit_breaker_manager")
	if !exists {
		return nil, false
	}
	
	cbManager, ok := manager.(*Manager)
	return cbManager, ok
}

// ExecuteWithCircuitBreaker is a helper function to execute operations with circuit breaker protection
// This can be used in HTTP handlers to protect external service calls
func ExecuteWithCircuitBreaker(c *gin.Context, serviceName string, fn func() error) error {
	manager, exists := GetManagerFromContext(c)
	if !exists {
		// If no circuit breaker manager is available, execute directly
		return fn()
	}

	return manager.Execute(serviceName, fn)
}

// PrometheusMetrics provides Prometheus-compatible metrics for circuit breakers
type PrometheusMetrics struct {
	manager *Manager
}

// NewPrometheusMetrics creates a new Prometheus metrics provider
func NewPrometheusMetrics(manager *Manager) *PrometheusMetrics {
	return &PrometheusMetrics{manager: manager}
}

// GetMetrics returns Prometheus-formatted metrics
func (p *PrometheusMetrics) GetMetrics() string {
	stats := p.manager.GetStats()
	metrics := ""

	for name, stat := range stats {
		// Circuit breaker state (0=closed, 1=half-open, 2=open)
		stateValue := 0
		switch stat.State {
		case StateHalfOpen:
			stateValue = 1
		case StateOpen:
			stateValue = 2
		}
		
		metrics += "# HELP circuit_breaker_state Circuit breaker state (0=closed, 1=half-open, 2=open)\n"
		metrics += "# TYPE circuit_breaker_state gauge\n"
		metrics += "circuit_breaker_state{name=\"" + name + "\"} " + string(rune(stateValue+'0')) + "\n"
		
		metrics += "# HELP circuit_breaker_requests_total Total number of requests\n"
		metrics += "# TYPE circuit_breaker_requests_total counter\n"
		metrics += "circuit_breaker_requests_total{name=\"" + name + "\"} " + string(rune(stat.Requests+'0')) + "\n"
		
		metrics += "# HELP circuit_breaker_successes_total Total number of successful requests\n"
		metrics += "# TYPE circuit_breaker_successes_total counter\n"
		metrics += "circuit_breaker_successes_total{name=\"" + name + "\"} " + string(rune(stat.TotalSuccesses+'0')) + "\n"
		
		metrics += "# HELP circuit_breaker_failures_total Total number of failed requests\n"
		metrics += "# TYPE circuit_breaker_failures_total counter\n"
		metrics += "circuit_breaker_failures_total{name=\"" + name + "\"} " + string(rune(stat.TotalFailures+'0')) + "\n"
	}

	return metrics
}
