/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package benchmark

import (
	"testing"
	"time"

	"github.com/google/uuid"

	"cina.club/services/live-im-service/internal/adapter/serializer"
	"cina.club/services/live-im-service/internal/domain/model"
)

// BenchmarkJSONSerialization benchmarks JSON serialization performance
func BenchmarkJSONSerialization(b *testing.B) {
	jsonSerializer := serializer.NewJSONSerializer()

	// Create test messages of different types and sizes
	user := &model.User{
		UserID:   uuid.New(),
		Username: "benchuser",
		Role:     model.RoleViewer,
		Level:    5,
		VIPLevel: 2,
		Badges:   []string{"active", "supporter", "premium"},
	}

	// Simple barrage message
	simpleMsg := model.NewBarrageMessage("Hello World!", "room-123", user)

	// Complex barrage message with long content
	longContent := ""
	for i := 0; i < 100; i++ {
		longContent += "This is a long message content for benchmarking purposes. "
	}
	complexMsg := model.NewBarrageMessage(longContent, "room-123", user)

	// Gift message with recipient
	recipientUser := &model.User{
		UserID:   uuid.New(),
		Username: "recipient",
		Role:     model.RoleViewer,
		Level:    10,
		VIPLevel: 5,
		Badges:   []string{"vip", "supporter", "premium", "diamond"},
	}
	giftMsg := model.NewGiftMessage("diamond", 10, "room-123", user, recipientUser)

	// Message with room info
	roomInfoMsg := model.Message{
		MessageID: uuid.New().String(),
		Type:      model.MessageTypeRoomUpdate,
		Timestamp: time.Now(),
		RoomInfo: &model.RoomInfo{
			RoomID:      "room-123",
			Title:       "Benchmark Room",
			Description: "A room for performance benchmarking",
			Category:    "Gaming",
			Tags:        []string{"benchmark", "performance", "testing", "gaming"},
			Status:      "live",
			OnlineCount: 1000,
			StartTime:   time.Now(),
		},
	}

	// Message with online users list
	onlineUsers := make([]*model.User, 50)
	for i := 0; i < 50; i++ {
		onlineUsers[i] = &model.User{
			UserID:   uuid.New(),
			Username: "user" + string(rune('0'+i%10)),
			Role:     model.RoleViewer,
			Level:    i % 20,
			VIPLevel: i % 10,
			Badges:   []string{"active"},
		}
	}
	onlineListMsg := model.Message{
		MessageID:  uuid.New().String(),
		Type:       model.MessageTypeRoomUpdate,
		Timestamp:  time.Now(),
		OnlineList: onlineUsers,
	}

	b.Run("SerializeSimpleMessage", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := jsonSerializer.SerializeMessage(simpleMsg)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("SerializeComplexMessage", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := jsonSerializer.SerializeMessage(complexMsg)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("SerializeGiftMessage", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := jsonSerializer.SerializeMessage(giftMsg)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("SerializeRoomInfoMessage", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := jsonSerializer.SerializeMessage(roomInfoMsg)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("SerializeOnlineListMessage", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := jsonSerializer.SerializeMessage(onlineListMsg)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

// BenchmarkJSONDeserialization benchmarks JSON deserialization performance
func BenchmarkJSONDeserialization(b *testing.B) {
	jsonSerializer := serializer.NewJSONSerializer()

	user := &model.User{
		UserID:   uuid.New(),
		Username: "benchuser",
		Role:     model.RoleViewer,
		Level:    5,
		VIPLevel: 2,
		Badges:   []string{"active", "supporter"},
	}

	// Pre-serialize messages for deserialization benchmarks
	simpleMsg := model.NewBarrageMessage("Hello World!", "room-123", user)
	simpleData, _ := jsonSerializer.SerializeMessage(simpleMsg)

	longContent := ""
	for i := 0; i < 100; i++ {
		longContent += "This is a long message content for benchmarking purposes. "
	}
	complexMsg := model.NewBarrageMessage(longContent, "room-123", user)
	complexData, _ := jsonSerializer.SerializeMessage(complexMsg)

	recipientUser := &model.User{
		UserID:   uuid.New(),
		Username: "recipient",
		Role:     model.RoleViewer,
	}
	giftMsg := model.NewGiftMessage("diamond", 10, "room-123", user, recipientUser)
	giftData, _ := jsonSerializer.SerializeMessage(giftMsg)

	b.Run("DeserializeSimpleMessage", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := jsonSerializer.DeserializeMessage(simpleData)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("DeserializeComplexMessage", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := jsonSerializer.DeserializeMessage(complexData)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("DeserializeGiftMessage", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := jsonSerializer.DeserializeMessage(giftData)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

// BenchmarkSerializationRoundTrip benchmarks full serialization round trip
func BenchmarkSerializationRoundTrip(b *testing.B) {
	jsonSerializer := serializer.NewJSONSerializer()

	user := &model.User{
		UserID:   uuid.New(),
		Username: "benchuser",
		Role:     model.RoleViewer,
		Level:    5,
		VIPLevel: 2,
		Badges:   []string{"active", "supporter"},
	}

	msg := model.NewBarrageMessage("Round trip benchmark message", "room-123", user)

	b.Run("JSONRoundTrip", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			data, err := jsonSerializer.SerializeMessage(msg)
			if err != nil {
				b.Fatal(err)
			}
			_, err = jsonSerializer.DeserializeMessage(data)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}

// BenchmarkConcurrentSerialization benchmarks concurrent serialization
func BenchmarkConcurrentSerialization(b *testing.B) {
	jsonSerializer := serializer.NewJSONSerializer()

	user := &model.User{
		UserID:   uuid.New(),
		Username: "benchuser",
		Role:     model.RoleViewer,
		Level:    5,
		VIPLevel: 2,
		Badges:   []string{"active", "supporter"},
	}

	msg := model.NewBarrageMessage("Concurrent serialization benchmark", "room-123", user)

	b.Run("ConcurrentJSONSerialization", func(b *testing.B) {
		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				_, err := jsonSerializer.SerializeMessage(msg)
				if err != nil {
					b.Error(err)
				}
			}
		})
	})

	// Pre-serialize for concurrent deserialization
	data, _ := jsonSerializer.SerializeMessage(msg)

	b.Run("ConcurrentJSONDeserialization", func(b *testing.B) {
		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			for pb.Next() {
				_, err := jsonSerializer.DeserializeMessage(data)
				if err != nil {
					b.Error(err)
				}
			}
		})
	})
}

// BenchmarkSerializationMemoryAllocation benchmarks memory allocation during serialization
func BenchmarkSerializationMemoryAllocation(b *testing.B) {
	jsonSerializer := serializer.NewJSONSerializer()

	user := &model.User{
		UserID:   uuid.New(),
		Username: "benchuser",
		Role:     model.RoleViewer,
		Level:    5,
		VIPLevel: 2,
		Badges:   []string{"active", "supporter"},
	}

	// Different message sizes to test memory allocation patterns
	smallMsg := model.NewBarrageMessage("Small", "room-123", user)
	
	mediumContent := ""
	for i := 0; i < 10; i++ {
		mediumContent += "Medium message content. "
	}
	mediumMsg := model.NewBarrageMessage(mediumContent, "room-123", user)

	largeContent := ""
	for i := 0; i < 1000; i++ {
		largeContent += "Large message content for memory allocation benchmarking. "
	}
	largeMsg := model.NewBarrageMessage(largeContent, "room-123", user)

	b.Run("SmallMessageSerialization", func(b *testing.B) {
		b.ReportAllocs()
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := jsonSerializer.SerializeMessage(smallMsg)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("MediumMessageSerialization", func(b *testing.B) {
		b.ReportAllocs()
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := jsonSerializer.SerializeMessage(mediumMsg)
			if err != nil {
				b.Fatal(err)
			}
		}
	})

	b.Run("LargeMessageSerialization", func(b *testing.B) {
		b.ReportAllocs()
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := jsonSerializer.SerializeMessage(largeMsg)
			if err != nil {
				b.Fatal(err)
			}
		}
	})
}
