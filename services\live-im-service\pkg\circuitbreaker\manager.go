/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package circuitbreaker

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// Manager manages multiple circuit breakers
type Manager struct {
	breakers map[string]*CircuitBreaker
	mutex    sync.RWMutex
	logger   *logrus.Logger
	metrics  MetricsCollector
}

// MetricsCollector defines the interface for collecting circuit breaker metrics
type MetricsCollector interface {
	// RecordRequest records a request attempt
	RecordRequest(name string, state State)

	// RecordSuccess records a successful request
	RecordSuccess(name string, duration time.Duration)

	// RecordFailure records a failed request
	RecordFailure(name string, duration time.Duration, err error)

	// RecordStateChange records a state change
	RecordStateChange(name string, from State, to State)
}

// ManagerConfig defines the configuration for the circuit breaker manager
type ManagerConfig struct {
	// DefaultConfig is the default configuration for new circuit breakers
	DefaultConfig Config

	// ServiceConfigs contains specific configurations for named services
	ServiceConfigs map[string]Config

	// MetricsCollector is used to collect metrics (optional)
	MetricsCollector MetricsCollector
}

// NewManager creates a new circuit breaker manager
func NewManager(config ManagerConfig, logger *logrus.Logger) *Manager {
	manager := &Manager{
		breakers: make(map[string]*CircuitBreaker),
		logger:   logger,
		metrics:  config.MetricsCollector,
	}

	// Set up default state change handler if not provided
	if config.DefaultConfig.OnStateChange == nil {
		config.DefaultConfig.OnStateChange = manager.onStateChange
	}

	// Create circuit breakers for predefined services
	for serviceName, serviceConfig := range config.ServiceConfigs {
		serviceConfig.Name = serviceName // Ensure name is set
		if serviceConfig.OnStateChange == nil {
			serviceConfig.OnStateChange = manager.onStateChange
		}
		manager.breakers[serviceName] = NewCircuitBreaker(serviceConfig, logger)
	}

	return manager
}

// GetOrCreate gets an existing circuit breaker or creates a new one
func (m *Manager) GetOrCreate(name string, config ...Config) *CircuitBreaker {
	m.mutex.RLock()
	if breaker, exists := m.breakers[name]; exists {
		m.mutex.RUnlock()
		return breaker
	}
	m.mutex.RUnlock()

	m.mutex.Lock()
	defer m.mutex.Unlock()

	// Double-check after acquiring write lock
	if breaker, exists := m.breakers[name]; exists {
		return breaker
	}

	// Use provided config or default
	var cbConfig Config
	if len(config) > 0 {
		cbConfig = config[0]
	} else {
		cbConfig = m.getDefaultConfig()
	}

	cbConfig.Name = name
	if cbConfig.OnStateChange == nil {
		cbConfig.OnStateChange = m.onStateChange
	}

	breaker := NewCircuitBreaker(cbConfig, m.logger)
	m.breakers[name] = breaker

	m.logger.WithField("circuit_breaker", name).Info("Created new circuit breaker")
	return breaker
}

// Execute executes a function with circuit breaker protection
func (m *Manager) Execute(serviceName string, fn func() error) error {
	breaker := m.GetOrCreate(serviceName)

	start := time.Now()
	state := breaker.State()

	if m.metrics != nil {
		m.metrics.RecordRequest(serviceName, state)
	}

	err := breaker.Execute(fn)
	duration := time.Since(start)

	if m.metrics != nil {
		if err != nil {
			m.metrics.RecordFailure(serviceName, duration, err)
		} else {
			m.metrics.RecordSuccess(serviceName, duration)
		}
	}

	return err
}

// ExecuteWithContext executes a function with context and circuit breaker protection
func (m *Manager) ExecuteWithContext(ctx context.Context, serviceName string, fn func(ctx context.Context) error) error {
	breaker := m.GetOrCreate(serviceName)

	start := time.Now()
	state := breaker.State()

	if m.metrics != nil {
		m.metrics.RecordRequest(serviceName, state)
	}

	err := breaker.ExecuteWithContext(ctx, fn)
	duration := time.Since(start)

	if m.metrics != nil {
		if err != nil {
			m.metrics.RecordFailure(serviceName, duration, err)
		} else {
			m.metrics.RecordSuccess(serviceName, duration)
		}
	}

	return err
}

// GetBreaker gets a circuit breaker by name
func (m *Manager) GetBreaker(name string) (*CircuitBreaker, bool) {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	breaker, exists := m.breakers[name]
	return breaker, exists
}

// GetAllBreakers returns all circuit breakers
func (m *Manager) GetAllBreakers() map[string]*CircuitBreaker {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	result := make(map[string]*CircuitBreaker)
	for name, breaker := range m.breakers {
		result[name] = breaker
	}
	return result
}

// GetStats returns statistics for all circuit breakers
func (m *Manager) GetStats() map[string]CircuitBreakerStats {
	m.mutex.RLock()
	defer m.mutex.RUnlock()

	stats := make(map[string]CircuitBreakerStats)
	for name, breaker := range m.breakers {
		counts := breaker.Counts()
		stats[name] = CircuitBreakerStats{
			Name:                 name,
			State:                breaker.State(),
			Requests:             counts.Requests,
			TotalSuccesses:       counts.TotalSuccesses,
			TotalFailures:        counts.TotalFailures,
			ConsecutiveSuccesses: counts.ConsecutiveSuccesses,
			ConsecutiveFailures:  counts.ConsecutiveFailures,
			SuccessRate:          calculateSuccessRate(counts),
		}
	}
	return stats
}

// Reset resets a circuit breaker to closed state
func (m *Manager) Reset(name string) error {
	m.mutex.RLock()
	_, exists := m.breakers[name]
	m.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("circuit breaker %s not found", name)
	}

	// Create a new circuit breaker with the same config to reset it
	config := m.getDefaultConfig()
	config.Name = name
	config.OnStateChange = m.onStateChange

	m.mutex.Lock()
	m.breakers[name] = NewCircuitBreaker(config, m.logger)
	m.mutex.Unlock()

	m.logger.WithField("circuit_breaker", name).Info("Circuit breaker reset")
	return nil
}

// Close closes all circuit breakers and cleans up resources
func (m *Manager) Close() error {
	m.mutex.Lock()
	defer m.mutex.Unlock()

	for name := range m.breakers {
		delete(m.breakers, name)
	}

	m.logger.Info("Circuit breaker manager closed")
	return nil
}

// onStateChange is the default state change handler
func (m *Manager) onStateChange(name string, from State, to State) {
	if m.metrics != nil {
		m.metrics.RecordStateChange(name, from, to)
	}

	m.logger.WithFields(logrus.Fields{
		"circuit_breaker": name,
		"from_state":      from.String(),
		"to_state":        to.String(),
	}).Warn("Circuit breaker state changed")
}

// getDefaultConfig returns the default circuit breaker configuration
func (m *Manager) getDefaultConfig() Config {
	return Config{
		MaxRequests: 10,
		Interval:    60 * time.Second,
		Timeout:     30 * time.Second,
		ReadyToTrip: func(counts Counts) bool {
			failureRate := float64(counts.TotalFailures) / float64(counts.Requests)
			return counts.Requests >= 10 && failureRate >= 0.6
		},
		IsSuccessful: func(err error) bool {
			return err == nil
		},
	}
}

// CircuitBreakerStats represents circuit breaker statistics
type CircuitBreakerStats struct {
	Name                 string  `json:"name"`
	State                State   `json:"state"`
	Requests             uint32  `json:"requests"`
	TotalSuccesses       uint32  `json:"total_successes"`
	TotalFailures        uint32  `json:"total_failures"`
	ConsecutiveSuccesses uint32  `json:"consecutive_successes"`
	ConsecutiveFailures  uint32  `json:"consecutive_failures"`
	SuccessRate          float64 `json:"success_rate"`
}

// calculateSuccessRate calculates the success rate from counts
func calculateSuccessRate(counts Counts) float64 {
	if counts.Requests == 0 {
		return 0.0
	}
	return float64(counts.TotalSuccesses) / float64(counts.Requests)
}

// DefaultMetricsCollector is a simple metrics collector that logs metrics
type DefaultMetricsCollector struct {
	logger *logrus.Logger
}

// NewDefaultMetricsCollector creates a new default metrics collector
func NewDefaultMetricsCollector(logger *logrus.Logger) *DefaultMetricsCollector {
	return &DefaultMetricsCollector{logger: logger}
}

// RecordRequest records a request attempt
func (d *DefaultMetricsCollector) RecordRequest(name string, state State) {
	d.logger.WithFields(logrus.Fields{
		"circuit_breaker": name,
		"state":           state.String(),
		"metric":          "request",
	}).Debug("Circuit breaker request")
}

// RecordSuccess records a successful request
func (d *DefaultMetricsCollector) RecordSuccess(name string, duration time.Duration) {
	d.logger.WithFields(logrus.Fields{
		"circuit_breaker": name,
		"duration_ms":     duration.Milliseconds(),
		"metric":          "success",
	}).Debug("Circuit breaker success")
}

// RecordFailure records a failed request
func (d *DefaultMetricsCollector) RecordFailure(name string, duration time.Duration, err error) {
	d.logger.WithFields(logrus.Fields{
		"circuit_breaker": name,
		"duration_ms":     duration.Milliseconds(),
		"error":           err.Error(),
		"metric":          "failure",
	}).Debug("Circuit breaker failure")
}

// RecordStateChange records a state change
func (d *DefaultMetricsCollector) RecordStateChange(name string, from State, to State) {
	d.logger.WithFields(logrus.Fields{
		"circuit_breaker": name,
		"from_state":      from.String(),
		"to_state":        to.String(),
		"metric":          "state_change",
	}).Info("Circuit breaker state change")
}
