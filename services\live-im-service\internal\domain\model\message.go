/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-06-27 13:00:00
Modified: 2025-06-27 13:00:00
*/

package model

import (
	"encoding/json"
	"fmt"
	"time"

	"github.com/google/uuid"
)

// MessageType defines the type of a message.
type MessageType string

const (
	// Client to Server Messages (C2S)
	MessageTypeAuth      MessageType = "auth"       // Authentication message
	MessageTypeBarrage   MessageType = "barrage"    // Barrage message
	MessageTypeLike      MessageType = "like"       // Like message
	MessageTypeGift      MessageType = "gift"       // Gift message
	MessageTypePing      MessageType = "ping"       // Heartbeat message
	MessageTypeJoinRoom  MessageType = "join_room"  // Join room message
	MessageTypeLeaveRoom MessageType = "leave_room" // Leave room message

	// Server to Client Messages (S2C)
	MessageTypeAuthResult   MessageType = "auth_result"   // Authentication result
	MessageTypeNewBarrage   MessageType = "new_barrage"   // New barrage
	MessageTypeLikeBurst    MessageType = "like_burst"    // Like burst
	MessageTypeNewGift      MessageType = "new_gift"      // New gift
	MessageTypeUserEnter    MessageType = "user_enter"    // User enters
	MessageTypeUserLeave    MessageType = "user_leave"    // User leaves
	MessageTypeRoomInfo     MessageType = "room_info"     // Room information
	MessageTypeError        MessageType = "error"         // Error message
	MessageTypePong         MessageType = "pong"          // Heartbeat response
	MessageTypeSystemNotice MessageType = "system_notice" // System notice
	MessageTypeUserUpdate   MessageType = "user_update"   // User information update
	MessageTypeModeAction   MessageType = "mode_action"   // Moderation action

	// Additional message types for protobuf compatibility
	MessageTypeRateLimited MessageType = "rate_limited" // Rate limited response
	MessageTypeUserJoin    MessageType = "user_join"    // User joins (alias for user_enter)
	MessageTypeRoomUpdate  MessageType = "room_update"  // Room update (alias for room_info)
)

// Message represents a generic message structure.
type Message struct {
	// Base fields
	Type      MessageType `json:"type"`       // Message type
	MessageID string      `json:"message_id"` // Message ID
	RoomID    string      `json:"room_id"`    // Room ID
	Timestamp time.Time   `json:"timestamp"`  // Timestamp

	// User information
	FromUserID string `json:"from_user_id,omitempty"` // Sending user ID
	FromUser   *User  `json:"from_user,omitempty"`    // Sending user information
	ToUserID   string `json:"to_user_id,omitempty"`   // Target user ID (for gifts, etc.)
	ToUser     *User  `json:"to_user,omitempty"`      // Target user information

	// Message content
	Content string                 `json:"content,omitempty"` // Text content
	Data    map[string]interface{} `json:"data,omitempty"`    // Extended data

	// Specific message fields
	Count      int    `json:"count,omitempty"`       // Quantity (likes, gifts)
	GiftID     string `json:"gift_id,omitempty"`     // Gift ID
	GiftName   string `json:"gift_name,omitempty"`   // Gift name
	GiftIcon   string `json:"gift_icon,omitempty"`   // Gift icon
	GiftValue  int    `json:"gift_value,omitempty"`  // Gift value
	GiftEffect string `json:"gift_effect,omitempty"` // Gift effect

	// Authentication fields
	Token     string `json:"token,omitempty"`      // JWT token
	Success   bool   `json:"success,omitempty"`    // Whether the operation was successful
	ErrorCode string `json:"error_code,omitempty"` // Error code
	ErrorMsg  string `json:"error_msg,omitempty"`  // Error message

	// Room information
	RoomInfo   *RoomInfo `json:"room_info,omitempty"`   // Room information
	OnlineList []*User   `json:"online_list,omitempty"` // Online user list

	// Internal fields (not serialized)
	FromClient *Client `json:"-"` // Sending client
	Processed  bool    `json:"-"` // Whether the message has been processed
}

// User represents user information.
type User struct {
	UserID   uuid.UUID `json:"user_id"`   // User ID
	Username string    `json:"username"`  // Username
	Avatar   string    `json:"avatar"`    // Avatar URL
	Role     UserRole  `json:"role"`      // User role
	Level    int       `json:"level"`     // User level
	VIPLevel int       `json:"vip_level"` // VIP level
	Badges   []string  `json:"badges"`    // List of badges
}

// NewUser creates a new user with default values.
func NewUser(userID uuid.UUID, username, avatar string) *User {
	return &User{
		UserID:   userID,
		Username: username,
		Avatar:   avatar,
		Role:     RoleViewer, // Default role
		Level:    1,          // Default level
		VIPLevel: 0,          // Default VIP level
		Badges:   []string{}, // Default empty badges
	}
}

// CanSendMessage checks if user can send messages.
func (u *User) CanSendMessage() bool {
	return true // All users can send messages
}

// CanModerate checks if user can moderate.
func (u *User) CanModerate() bool {
	return u.Role == RoleModerator || u.Role == RoleAdmin || u.Role == RoleStreamer
}

// CanManageRoom checks if user can manage room.
func (u *User) CanManageRoom() bool {
	return u.Role == RoleAdmin || u.Role == RoleStreamer
}

// AddBadge adds a badge to the user.
func (u *User) AddBadge(badge string) {
	for _, b := range u.Badges {
		if b == badge {
			return // Already has badge
		}
	}
	u.Badges = append(u.Badges, badge)
}

// RemoveBadge removes a badge from the user.
func (u *User) RemoveBadge(badge string) {
	for i, b := range u.Badges {
		if b == badge {
			u.Badges = append(u.Badges[:i], u.Badges[i+1:]...)
			return
		}
	}
}

// HasBadge checks if user has a specific badge.
func (u *User) HasBadge(badge string) bool {
	for _, b := range u.Badges {
		if b == badge {
			return true
		}
	}
	return false
}

// SetLevel sets the user level.
func (u *User) SetLevel(level int) {
	if level < 1 {
		level = 1
	}
	u.Level = level
}

// LevelUp increases user level by 1.
func (u *User) LevelUp() {
	u.Level++
}

// LevelDown decreases user level by 1 (minimum 1).
func (u *User) LevelDown() {
	if u.Level > 1 {
		u.Level--
	}
}

// SetVIPLevel sets the VIP level.
func (u *User) SetVIPLevel(level int) {
	if level < 0 {
		level = 0
	}
	u.VIPLevel = level
}

// IsVIP checks if user is VIP.
func (u *User) IsVIP() bool {
	return u.VIPLevel > 0
}

// SetOnline sets the online status.
func (u *User) SetOnline(online bool) {
	// Note: User struct doesn't have IsOnline field in current implementation
	// This method is here for test compatibility
}

// String returns string representation of user.
func (u *User) String() string {
	return fmt.Sprintf("User{ID: %s, Username: %s, Role: %s, Level: %d, VIPLevel: %d}",
		u.UserID.String(), u.Username, u.Role, u.Level, u.VIPLevel)
}

// RoomInfo represents room information.
type RoomInfo struct {
	RoomID      string    `json:"room_id"`      // Room ID
	Title       string    `json:"title"`        // Room title
	Description string    `json:"description"`  // Room description
	Category    string    `json:"category"`     // Category
	Tags        []string  `json:"tags"`         // Tags
	Status      string    `json:"status"`       // Status
	OnlineCount int       `json:"online_count"` // Online count
	StartTime   time.Time `json:"start_time"`   // Start time
	Streamer    *User     `json:"streamer"`     // Streamer information
}

// UserEvent represents a user event.
type UserEvent struct {
	Type     UserEventType `json:"type"`
	UserID   uuid.UUID     `json:"user_id"`
	Username string        `json:"username"`
	Avatar   string        `json:"avatar"`
	RoomID   string        `json:"room_id"`
	Client   *Client       `json:"-"`
}

// UserEventType defines the type of a user event.
type UserEventType string

const (
	UserEventTypeJoin  UserEventType = "join"  // User joins
	UserEventTypeLeave UserEventType = "leave" // User leaves
)

// GiftInfo represents gift information.
type GiftInfo struct {
	ID       string `json:"id"`       // Gift ID
	Name     string `json:"name"`     // Gift name
	Icon     string `json:"icon"`     // Icon URL
	Value    int    `json:"value"`    // Value (virtual currency)
	Effect   string `json:"effect"`   // Special effect
	Category string `json:"category"` // Category
	Rarity   string `json:"rarity"`   // Rarity
}

// ModerationAction represents a moderation action.
type ModerationAction struct {
	Type     string    `json:"type"`     // Action type
	Target   string    `json:"target"`   // Target user ID
	Duration int       `json:"duration"` // Duration (seconds)
	Reason   string    `json:"reason"`   // Reason
	Admin    string    `json:"admin"`    // Administrator who performed the action
	Time     time.Time `json:"time"`     // Time of the action
}

// Message Constructors

// NewAuthMessage creates an authentication message.
func NewAuthMessage(token, roomID string) Message {
	return Message{
		Type:      MessageTypeAuth,
		MessageID: uuid.New().String(),
		RoomID:    roomID,
		Token:     token,
		Timestamp: time.Now(),
	}
}

// NewAuthResultMessage creates an authentication result message.
func NewAuthResultMessage(success bool, roomInfo *RoomInfo, onlineList []*User, errorMsg string) Message {
	msg := Message{
		Type:       MessageTypeAuthResult,
		MessageID:  uuid.New().String(),
		Success:    success,
		Timestamp:  time.Now(),
		RoomInfo:   roomInfo,
		OnlineList: onlineList,
	}

	if !success {
		msg.ErrorMsg = errorMsg
		msg.ErrorCode = "AUTH_FAILED"
	}

	return msg
}

// NewBarrageMessage creates a barrage message.
func NewBarrageMessage(content, roomID string, fromUser *User) Message {
	return Message{
		Type:       MessageTypeBarrage,
		MessageID:  uuid.New().String(),
		RoomID:     roomID,
		Content:    content,
		FromUserID: fromUser.UserID.String(),
		FromUser:   fromUser,
		Timestamp:  time.Now(),
	}
}

// NewNewBarrageMessage creates a new barrage broadcast message.
func NewNewBarrageMessage(content, roomID string, fromUser *User) Message {
	return Message{
		Type:       MessageTypeNewBarrage,
		MessageID:  uuid.New().String(),
		RoomID:     roomID,
		Content:    content,
		FromUserID: fromUser.UserID.String(),
		FromUser:   fromUser,
		Timestamp:  time.Now(),
	}
}

// NewLikeMessage creates a like message.
func NewLikeMessage(count int, roomID string, fromUser *User) Message {
	return Message{
		Type:       MessageTypeLike,
		MessageID:  uuid.New().String(),
		RoomID:     roomID,
		Count:      count,
		FromUserID: fromUser.UserID.String(),
		FromUser:   fromUser,
		Timestamp:  time.Now(),
	}
}

// NewLikeBurstMessage creates a like burst message.
func NewLikeBurstMessage(count int, roomID string) Message {
	return Message{
		Type:      MessageTypeLikeBurst,
		MessageID: uuid.New().String(),
		RoomID:    roomID,
		Count:     count,
		Timestamp: time.Now(),
	}
}

// NewGiftMessage creates a gift message.
func NewGiftMessage(giftID string, count int, roomID string, fromUser, toUser *User) Message {
	return Message{
		Type:       MessageTypeGift,
		MessageID:  uuid.New().String(),
		RoomID:     roomID,
		GiftID:     giftID,
		Count:      count,
		FromUserID: fromUser.UserID.String(),
		FromUser:   fromUser,
		ToUserID:   toUser.UserID.String(),
		ToUser:     toUser,
		Timestamp:  time.Now(),
	}
}

// NewNewGiftMessage creates a new gift broadcast message.
func NewNewGiftMessage(gift *GiftInfo, count int, roomID string, fromUser, toUser *User) Message {
	return Message{
		Type:       MessageTypeNewGift,
		MessageID:  uuid.New().String(),
		RoomID:     roomID,
		GiftID:     gift.ID,
		GiftName:   gift.Name,
		GiftIcon:   gift.Icon,
		GiftValue:  gift.Value,
		GiftEffect: gift.Effect,
		Count:      count,
		FromUserID: fromUser.UserID.String(),
		FromUser:   fromUser,
		ToUserID:   toUser.UserID.String(),
		ToUser:     toUser,
		Timestamp:  time.Now(),
	}
}

// NewUserEnterMessage creates a user enter message.
func NewUserEnterMessage(roomID string, user *User) Message {
	return Message{
		Type:       MessageTypeUserEnter,
		MessageID:  uuid.New().String(),
		RoomID:     roomID,
		FromUserID: user.UserID.String(),
		FromUser:   user,
		Timestamp:  time.Now(),
	}
}

// NewUserLeaveMessage creates a user leave message.
func NewUserLeaveMessage(roomID string, user *User) Message {
	return Message{
		Type:       MessageTypeUserLeave,
		MessageID:  uuid.New().String(),
		RoomID:     roomID,
		FromUserID: user.UserID.String(),
		FromUser:   user,
		Timestamp:  time.Now(),
	}
}

// NewRoomInfoMessage creates a room information message.
func NewRoomInfoMessage(roomInfo *RoomInfo) Message {
	return Message{
		Type:      MessageTypeRoomInfo,
		MessageID: uuid.New().String(),
		RoomID:    roomInfo.RoomID,
		RoomInfo:  roomInfo,
		Timestamp: time.Now(),
	}
}

// NewErrorMessage creates an error message.
func NewErrorMessage(errorMsg string) Message {
	return Message{
		Type:      MessageTypeError,
		MessageID: uuid.New().String(),
		ErrorMsg:  errorMsg,
		ErrorCode: "GENERAL_ERROR",
		Success:   false,
		Timestamp: time.Now(),
	}
}

// NewErrorMessageWithCode creates an error message with an error code.
func NewErrorMessageWithCode(errorCode, errorMsg string) Message {
	return Message{
		Type:      MessageTypeError,
		MessageID: uuid.New().String(),
		ErrorMsg:  errorMsg,
		ErrorCode: errorCode,
		Success:   false,
		Timestamp: time.Now(),
	}
}

// NewPingMessage creates a ping message.
func NewPingMessage() Message {
	return Message{
		Type:      MessageTypePing,
		MessageID: uuid.New().String(),
		Timestamp: time.Now(),
	}
}

// NewPongMessage creates a pong response message.
func NewPongMessage() Message {
	return Message{
		Type:      MessageTypePong,
		MessageID: uuid.New().String(),
		Timestamp: time.Now(),
	}
}

// NewHeartbeatMessage creates a server heartbeat message.
func NewHeartbeatMessage() Message {
	return Message{
		Type:      MessageTypePong,
		MessageID: uuid.New().String(),
		Content:   "heartbeat",
		Timestamp: time.Now(),
	}
}

// NewSystemNoticeMessage creates a system notice message.
func NewSystemNoticeMessage(content, roomID string) Message {
	return Message{
		Type:      MessageTypeSystemNotice,
		MessageID: uuid.New().String(),
		RoomID:    roomID,
		Content:   content,
		Timestamp: time.Now(),
	}
}

// NewModeActionMessage creates a moderation action message.
func NewModeActionMessage(action *ModerationAction, roomID string) Message {
	return Message{
		Type:      MessageTypeModeAction,
		MessageID: uuid.New().String(),
		RoomID:    roomID,
		Data: map[string]interface{}{
			"action": action,
		},
		Timestamp: time.Now(),
	}
}

// Message validation and processing methods

// Validate validates the message.
func (m *Message) Validate() error {
	if m.Type == "" {
		return fmt.Errorf("message type is required")
	}

	switch m.Type {
	case MessageTypeAuth:
		if m.Token == "" {
			return fmt.Errorf("token is required for auth message")
		}
		if m.RoomID == "" {
			return fmt.Errorf("room_id is required for auth message")
		}

	case MessageTypeBarrage:
		if m.Content == "" {
			return fmt.Errorf("content is required for barrage message")
		}
		if len(m.Content) > 500 {
			return fmt.Errorf("barrage content too long (max 500 characters)")
		}
		if m.RoomID == "" {
			return fmt.Errorf("room_id is required for barrage message")
		}

	case MessageTypeGift:
		if m.GiftID == "" {
			return fmt.Errorf("gift_id is required for gift message")
		}
		if m.Count <= 0 {
			return fmt.Errorf("count must be positive for gift message")
		}
		if m.RoomID == "" {
			return fmt.Errorf("room_id is required for gift message")
		}
		if m.ToUserID == "" {
			return fmt.Errorf("to_user_id is required for gift message")
		}

	case MessageTypeLike:
		if m.Count <= 0 {
			m.Count = 1 // Default to 1 like
		}
		if m.RoomID == "" {
			return fmt.Errorf("room_id is required for like message")
		}
	}

	return nil
}

// SetFromUser sets the sending user information.
func (m *Message) SetFromUser(user *User) {
	m.FromUserID = user.UserID.String()
	m.FromUser = user
}

// SetToUser sets the target user information.
func (m *Message) SetToUser(user *User) {
	m.ToUserID = user.UserID.String()
	m.ToUser = user
}

// ToJSON converts the message to a JSON string.
func (m *Message) ToJSON() ([]byte, error) {
	return json.Marshal(m)
}

// FromJSON parses the message from a JSON string.
func (m *Message) FromJSON(data []byte) error {
	return json.Unmarshal(data, m)
}

// IsClientToServer checks if the message is from client to server.
func (m *Message) IsClientToServer() bool {
	switch m.Type {
	case MessageTypeAuth, MessageTypeBarrage, MessageTypeLike,
		MessageTypeGift, MessageTypePing, MessageTypeJoinRoom, MessageTypeLeaveRoom:
		return true
	default:
		return false
	}
}

// IsServerToClient checks if the message is from server to client.
func (m *Message) IsServerToClient() bool {
	switch m.Type {
	case MessageTypeAuthResult, MessageTypeNewBarrage, MessageTypeLikeBurst,
		MessageTypeNewGift, MessageTypeUserEnter, MessageTypeUserLeave,
		MessageTypeRoomInfo, MessageTypeError, MessageTypePong,
		MessageTypeSystemNotice, MessageTypeUserUpdate, MessageTypeModeAction:
		return true
	default:
		return false
	}
}

// IsBroadcastMessage checks if the message is a broadcast message.
func (m *Message) IsBroadcastMessage() bool {
	switch m.Type {
	case MessageTypeNewBarrage, MessageTypeLikeBurst, MessageTypeNewGift,
		MessageTypeUserEnter, MessageTypeUserLeave, MessageTypeSystemNotice:
		return true
	default:
		return false
	}
}

// GetSize gets the estimated size of the message.
func (m *Message) GetSize() int {
	data, err := m.ToJSON()
	if err != nil {
		return 0
	}
	return len(data)
}

// Clone clones the message.
func (m *Message) Clone() Message {
	clone := *m

	// Deep copy user info
	if m.FromUser != nil {
		fromUser := *m.FromUser
		clone.FromUser = &fromUser
	}
	if m.ToUser != nil {
		toUser := *m.ToUser
		clone.ToUser = &toUser
	}

	// Deep copy room info
	if m.RoomInfo != nil {
		roomInfo := *m.RoomInfo
		clone.RoomInfo = &roomInfo
	}

	// Deep copy online list
	if m.OnlineList != nil {
		onlineList := make([]*User, len(m.OnlineList))
		for i, user := range m.OnlineList {
			userCopy := *user
			onlineList[i] = &userCopy
		}
		clone.OnlineList = onlineList
	}

	// Deep copy data map
	if m.Data != nil {
		data := make(map[string]interface{})
		for k, v := range m.Data {
			data[k] = v
		}
		clone.Data = data
	}

	return clone
}

// Error code constants
const (
	ErrorCodeAuthFailed         = "AUTH_FAILED"
	ErrorCodeInvalidToken       = "INVALID_TOKEN"
	ErrorCodeRoomNotFound       = "ROOM_NOT_FOUND"
	ErrorCodeUserMuted          = "USER_MUTED"
	ErrorCodeRateLimited        = "RATE_LIMITED"
	ErrorCodeInvalidMessage     = "INVALID_MESSAGE"
	ErrorCodeInsufficientFunds  = "INSUFFICIENT_FUNDS"
	ErrorCodeGiftNotFound       = "GIFT_NOT_FOUND"
	ErrorCodePermissionDenied   = "PERMISSION_DENIED"
	ErrorCodeServerError        = "SERVER_ERROR"
	ErrorCodeConnectionLost     = "CONNECTION_LOST"
	ErrorCodeTooManyConnections = "TOO_MANY_CONNECTIONS"
)

// Common error message constructors

// NewAuthFailedMessage creates an authentication failed message.
func NewAuthFailedMessage(reason string) Message {
	return NewErrorMessageWithCode(ErrorCodeAuthFailed, "Authentication failed: "+reason)
}

// NewInvalidTokenMessage creates an invalid token message.
func NewInvalidTokenMessage() Message {
	return NewErrorMessageWithCode(ErrorCodeInvalidToken, "Invalid or expired token")
}

// NewRoomNotFoundMessage creates a room not found message.
func NewRoomNotFoundMessage(roomID string) Message {
	return NewErrorMessageWithCode(ErrorCodeRoomNotFound, fmt.Sprintf("Room %s not found", roomID))
}

// NewUserMutedMessage creates a user muted message.
func NewUserMutedMessage(duration time.Duration) Message {
	return NewErrorMessageWithCode(ErrorCodeUserMuted,
		fmt.Sprintf("You are muted for %s", duration.String()))
}

// NewRateLimitedMessage creates a rate limited message.
func NewRateLimitedMessage() Message {
	return NewErrorMessageWithCode(ErrorCodeRateLimited, "Rate limited: too many messages")
}

// NewInsufficientFundsMessage creates an insufficient funds message.
func NewInsufficientFundsMessage() Message {
	return NewErrorMessageWithCode(ErrorCodeInsufficientFunds, "Insufficient funds to send gift")
}

// NewGiftNotFoundMessage creates a gift not found message.
func NewGiftNotFoundMessage(giftID string) Message {
	return NewErrorMessageWithCode(ErrorCodeGiftNotFound, fmt.Sprintf("Gift %s not found", giftID))
}

// NewPermissionDeniedMessage creates a permission denied message.
func NewPermissionDeniedMessage(action string) Message {
	return NewErrorMessageWithCode(ErrorCodePermissionDenied,
		fmt.Sprintf("Permission denied for action: %s", action))
}

// NewServerErrorMessage creates a server error message.
func NewServerErrorMessage() Message {
	return NewErrorMessageWithCode(ErrorCodeServerError, "Internal server error")
}

// UnixToTime converts Unix timestamp to time.Time
func UnixToTime(timestamp int64) time.Time {
	return time.Unix(timestamp, 0).UTC()
}

// ParseUUID parses a string UUID
func ParseUUID(s string) (uuid.UUID, error) {
	return uuid.Parse(s)
}
