/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package errors

import (
	"fmt"
	"net/http"
	"time"
)

// ErrorCode represents the type of error
type ErrorCode string

// Error codes for live IM service
const (
	// Client errors
	ErrCodeClientNotFound    ErrorCode = "CLIENT_NOT_FOUND"
	ErrCodeClientInactive    ErrorCode = "CLIENT_INACTIVE"
	ErrCodeClientClosed      ErrorCode = "CLIENT_CLOSED"
	ErrCodeClientTimeout     ErrorCode = "CLIENT_TIMEOUT"
	ErrCodeClientOverloaded  ErrorCode = "CLIENT_OVERLOADED"
	ErrCodeClientRateLimited ErrorCode = "CLIENT_RATE_LIMITED"

	// Authentication and authorization errors
	ErrCodeUnauthorized     ErrorCode = "UNAUTHORIZED"
	ErrCodeInvalidToken     ErrorCode = "INVALID_TOKEN"
	ErrCodeTokenExpired     ErrorCode = "TOKEN_EXPIRED"
	ErrCodePermissionDenied ErrorCode = "PERMISSION_DENIED"
	ErrCodeUserMuted        ErrorCode = "USER_MUTED"
	ErrCodeUserBanned       ErrorCode = "USER_BANNED"

	// Room errors
	ErrCodeRoomNotFound  ErrorCode = "ROOM_NOT_FOUND"
	ErrCodeRoomClosed    ErrorCode = "ROOM_CLOSED"
	ErrCodeRoomFull      ErrorCode = "ROOM_FULL"
	ErrCodeRoomMismatch  ErrorCode = "ROOM_MISMATCH"
	ErrCodeNotInRoom     ErrorCode = "NOT_IN_ROOM"
	ErrCodeAlreadyInRoom ErrorCode = "ALREADY_IN_ROOM"

	// Message errors
	ErrCodeInvalidMessage     ErrorCode = "INVALID_MESSAGE"
	ErrCodeInvalidMessageType ErrorCode = "INVALID_MESSAGE_TYPE"
	ErrCodeEmptyContent       ErrorCode = "EMPTY_CONTENT"
	ErrCodeContentTooLong     ErrorCode = "CONTENT_TOO_LONG"
	ErrCodeMessageTooLarge    ErrorCode = "MESSAGE_TOO_LARGE"
	ErrCodeInvalidGift        ErrorCode = "INVALID_GIFT"
	ErrCodeInvalidGiftCount   ErrorCode = "INVALID_GIFT_COUNT"

	// Service errors
	ErrCodeServiceUnavailable ErrorCode = "SERVICE_UNAVAILABLE"
	ErrCodeInternalError      ErrorCode = "INTERNAL_ERROR"
	ErrCodeDatabaseError      ErrorCode = "DATABASE_ERROR"
	ErrCodeRedisError         ErrorCode = "REDIS_ERROR"
	ErrCodeNetworkError       ErrorCode = "NETWORK_ERROR"
	ErrCodeTimeoutError       ErrorCode = "TIMEOUT_ERROR"

	// Validation errors
	ErrCodeInvalidInput     ErrorCode = "INVALID_INPUT"
	ErrCodeValidationFailed ErrorCode = "VALIDATION_FAILED"
	ErrCodeMissingField     ErrorCode = "MISSING_FIELD"
	ErrCodeInvalidFormat    ErrorCode = "INVALID_FORMAT"

	// Business logic errors
	ErrCodeInsufficientFunds ErrorCode = "INSUFFICIENT_FUNDS"
	ErrCodeTransactionFailed ErrorCode = "TRANSACTION_FAILED"
	ErrCodeQuotaExceeded     ErrorCode = "QUOTA_EXCEEDED"
	ErrCodeFeatureDisabled   ErrorCode = "FEATURE_DISABLED"
)

// LiveIMError represents a structured error for the live IM service
type LiveIMError struct {
	Code        ErrorCode              `json:"code"`
	Message     string                 `json:"message"`
	Details     string                 `json:"details,omitempty"`
	Field       string                 `json:"field,omitempty"`
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	Cause       error                  `json:"-"`
	Timestamp   time.Time              `json:"timestamp"`
	HTTPStatus  int                    `json:"-"`
	Retryable   bool                   `json:"retryable"`
	UserMessage string                 `json:"user_message,omitempty"`
}

// Error implements the error interface
func (e *LiveIMError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("[%s] %s: %s", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("[%s] %s", e.Code, e.Message)
}

// Unwrap returns the underlying cause error
func (e *LiveIMError) Unwrap() error {
	return e.Cause
}

// Is checks if the error matches the target error
func (e *LiveIMError) Is(target error) bool {
	if t, ok := target.(*LiveIMError); ok {
		return e.Code == t.Code
	}
	return false
}

// GetHTTPStatus returns the appropriate HTTP status code
func (e *LiveIMError) GetHTTPStatus() int {
	if e.HTTPStatus != 0 {
		return e.HTTPStatus
	}
	return getHTTPStatusForCode(e.Code)
}

// GetUserMessage returns a user-friendly error message
func (e *LiveIMError) GetUserMessage() string {
	if e.UserMessage != "" {
		return e.UserMessage
	}
	return getUserMessageForCode(e.Code)
}

// WithMetadata adds metadata to the error
func (e *LiveIMError) WithMetadata(key string, value interface{}) *LiveIMError {
	if e.Metadata == nil {
		e.Metadata = make(map[string]interface{})
	}
	e.Metadata[key] = value
	return e
}

// WithField sets the field that caused the error
func (e *LiveIMError) WithField(field string) *LiveIMError {
	e.Field = field
	return e
}

// WithUserMessage sets a custom user-friendly message
func (e *LiveIMError) WithUserMessage(message string) *LiveIMError {
	e.UserMessage = message
	return e
}

// NewLiveIMError creates a new LiveIMError
func NewLiveIMError(code ErrorCode, message string) *LiveIMError {
	return &LiveIMError{
		Code:       code,
		Message:    message,
		Timestamp:  time.Now(),
		HTTPStatus: getHTTPStatusForCode(code),
		Retryable:  isRetryableCode(code),
	}
}

// NewLiveIMErrorWithDetails creates a new LiveIMError with details
func NewLiveIMErrorWithDetails(code ErrorCode, message, details string) *LiveIMError {
	return &LiveIMError{
		Code:       code,
		Message:    message,
		Details:    details,
		Timestamp:  time.Now(),
		HTTPStatus: getHTTPStatusForCode(code),
		Retryable:  isRetryableCode(code),
	}
}

// NewLiveIMErrorWithCause creates a new LiveIMError wrapping another error
func NewLiveIMErrorWithCause(code ErrorCode, message string, cause error) *LiveIMError {
	return &LiveIMError{
		Code:       code,
		Message:    message,
		Cause:      cause,
		Timestamp:  time.Now(),
		HTTPStatus: getHTTPStatusForCode(code),
		Retryable:  isRetryableCode(code),
	}
}

// WrapError wraps an existing error with LiveIMError
func WrapError(err error, code ErrorCode, message string) *LiveIMError {
	return &LiveIMError{
		Code:       code,
		Message:    message,
		Details:    err.Error(),
		Cause:      err,
		Timestamp:  time.Now(),
		HTTPStatus: getHTTPStatusForCode(code),
		Retryable:  isRetryableCode(code),
	}
}

// IsLiveIMError checks if an error is a LiveIMError
func IsLiveIMError(err error) bool {
	_, ok := err.(*LiveIMError)
	return ok
}

// GetLiveIMError extracts LiveIMError from error
func GetLiveIMError(err error) *LiveIMError {
	if e, ok := err.(*LiveIMError); ok {
		return e
	}
	return nil
}

// HasCode checks if an error has a specific error code
func HasCode(err error, code ErrorCode) bool {
	if e := GetLiveIMError(err); e != nil {
		return e.Code == code
	}
	return false
}

// IsRetryable checks if an error is retryable
func IsRetryable(err error) bool {
	if e := GetLiveIMError(err); e != nil {
		return e.Retryable
	}
	return false
}

// getHTTPStatusForCode maps error codes to HTTP status codes
func getHTTPStatusForCode(code ErrorCode) int {
	switch code {
	case ErrCodeClientNotFound, ErrCodeRoomNotFound:
		return http.StatusNotFound
	case ErrCodeUnauthorized, ErrCodeInvalidToken, ErrCodeTokenExpired:
		return http.StatusUnauthorized
	case ErrCodePermissionDenied, ErrCodeUserMuted, ErrCodeUserBanned:
		return http.StatusForbidden
	case ErrCodeInvalidInput, ErrCodeValidationFailed, ErrCodeMissingField,
		ErrCodeInvalidFormat, ErrCodeInvalidMessage, ErrCodeInvalidMessageType,
		ErrCodeEmptyContent, ErrCodeContentTooLong, ErrCodeMessageTooLarge,
		ErrCodeInvalidGift, ErrCodeInvalidGiftCount:
		return http.StatusBadRequest
	case ErrCodeClientRateLimited, ErrCodeQuotaExceeded:
		return http.StatusTooManyRequests
	case ErrCodeRoomFull:
		return http.StatusConflict
	case ErrCodeServiceUnavailable, ErrCodeDatabaseError, ErrCodeRedisError,
		ErrCodeNetworkError, ErrCodeTimeoutError:
		return http.StatusServiceUnavailable
	case ErrCodeInternalError:
		return http.StatusInternalServerError
	default:
		return http.StatusInternalServerError
	}
}

// isRetryableCode determines if an error code represents a retryable error
func isRetryableCode(code ErrorCode) bool {
	switch code {
	case ErrCodeServiceUnavailable, ErrCodeDatabaseError, ErrCodeRedisError,
		ErrCodeNetworkError, ErrCodeTimeoutError, ErrCodeClientTimeout,
		ErrCodeInternalError:
		return true
	default:
		return false
	}
}

// getUserMessageForCode returns user-friendly messages for error codes
func getUserMessageForCode(code ErrorCode) string {
	switch code {
	case ErrCodeClientNotFound:
		return "Connection not found. Please refresh and try again."
	case ErrCodeClientInactive:
		return "Your connection is inactive. Please reconnect."
	case ErrCodeUnauthorized:
		return "Authentication required. Please log in."
	case ErrCodePermissionDenied:
		return "You don't have permission to perform this action."
	case ErrCodeUserMuted:
		return "You are currently muted and cannot send messages."
	case ErrCodeRoomNotFound:
		return "The room you're trying to join doesn't exist."
	case ErrCodeRoomFull:
		return "The room is currently full. Please try again later."
	case ErrCodeNotInRoom:
		return "You must join a room before sending messages."
	case ErrCodeContentTooLong:
		return "Your message is too long. Please shorten it and try again."
	case ErrCodeClientRateLimited:
		return "You're sending messages too quickly. Please slow down."
	case ErrCodeServiceUnavailable:
		return "Service is temporarily unavailable. Please try again later."
	case ErrCodeInsufficientFunds:
		return "Insufficient balance to send this gift."
	default:
		return "An error occurred. Please try again."
	}
}

// Common error constructors

// Client errors
func ErrClientNotFound(clientID string) *LiveIMError {
	return NewLiveIMErrorWithDetails(
		ErrCodeClientNotFound,
		"Client not found",
		fmt.Sprintf("Client ID: %s", clientID),
	).WithMetadata("client_id", clientID)
}

func ErrClientInactive(clientID string) *LiveIMError {
	return NewLiveIMErrorWithDetails(
		ErrCodeClientInactive,
		"Client is inactive",
		fmt.Sprintf("Client ID: %s", clientID),
	).WithMetadata("client_id", clientID)
}

func ErrClientRateLimited(clientID string, action string, retryAfter time.Duration) *LiveIMError {
	return NewLiveIMErrorWithDetails(
		ErrCodeClientRateLimited,
		"Rate limit exceeded",
		fmt.Sprintf("Action: %s, retry after: %v", action, retryAfter),
	).WithMetadata("client_id", clientID).
		WithMetadata("action", action).
		WithMetadata("retry_after_seconds", int(retryAfter.Seconds()))
}

// Authentication errors
func ErrUnauthorized(reason string) *LiveIMError {
	return NewLiveIMErrorWithDetails(
		ErrCodeUnauthorized,
		"Authentication required",
		reason,
	)
}

func ErrInvalidToken(token string) *LiveIMError {
	return NewLiveIMErrorWithDetails(
		ErrCodeInvalidToken,
		"Invalid authentication token",
		"Token is malformed or invalid",
	).WithMetadata("token_prefix", token[:min(len(token), 10)]+"...")
}

func ErrPermissionDenied(action string, userID string) *LiveIMError {
	return NewLiveIMErrorWithDetails(
		ErrCodePermissionDenied,
		"Permission denied",
		fmt.Sprintf("User %s cannot perform action: %s", userID, action),
	).WithMetadata("user_id", userID).WithMetadata("action", action)
}

func ErrUserMuted(userID string, mutedUntil *time.Time) *LiveIMError {
	err := NewLiveIMErrorWithDetails(
		ErrCodeUserMuted,
		"User is muted",
		fmt.Sprintf("User ID: %s", userID),
	).WithMetadata("user_id", userID)

	if mutedUntil != nil {
		err.WithMetadata("muted_until", mutedUntil.Format(time.RFC3339))
	}

	return err
}

// Room errors
func ErrRoomNotFound(roomID string) *LiveIMError {
	return NewLiveIMErrorWithDetails(
		ErrCodeRoomNotFound,
		"Room not found",
		fmt.Sprintf("Room ID: %s", roomID),
	).WithMetadata("room_id", roomID)
}

func ErrRoomFull(roomID string, maxCapacity int) *LiveIMError {
	return NewLiveIMErrorWithDetails(
		ErrCodeRoomFull,
		"Room is full",
		fmt.Sprintf("Room ID: %s, max capacity: %d", roomID, maxCapacity),
	).WithMetadata("room_id", roomID).WithMetadata("max_capacity", maxCapacity)
}

func ErrNotInRoom(userID, roomID string) *LiveIMError {
	return NewLiveIMErrorWithDetails(
		ErrCodeNotInRoom,
		"User not in room",
		fmt.Sprintf("User %s is not in room %s", userID, roomID),
	).WithMetadata("user_id", userID).WithMetadata("room_id", roomID)
}

// Message errors
func ErrInvalidMessage(reason string) *LiveIMError {
	return NewLiveIMErrorWithDetails(
		ErrCodeInvalidMessage,
		"Invalid message",
		reason,
	)
}

func ErrContentTooLong(length, maxLength int) *LiveIMError {
	return NewLiveIMErrorWithDetails(
		ErrCodeContentTooLong,
		"Message content too long",
		fmt.Sprintf("Content length: %d, max allowed: %d", length, maxLength),
	).WithMetadata("content_length", length).WithMetadata("max_length", maxLength)
}

func ErrInvalidGift(giftID string, reason string) *LiveIMError {
	return NewLiveIMErrorWithDetails(
		ErrCodeInvalidGift,
		"Invalid gift",
		fmt.Sprintf("Gift ID: %s, reason: %s", giftID, reason),
	).WithMetadata("gift_id", giftID).WithMetadata("reason", reason)
}

// Service errors
func ErrServiceUnavailable(service string, cause error) *LiveIMError {
	return NewLiveIMErrorWithCause(
		ErrCodeServiceUnavailable,
		fmt.Sprintf("%s service unavailable", service),
		cause,
	).WithMetadata("service", service)
}

func ErrRedisError(operation string, cause error) *LiveIMError {
	return NewLiveIMErrorWithCause(
		ErrCodeRedisError,
		fmt.Sprintf("Redis operation failed: %s", operation),
		cause,
	).WithMetadata("operation", operation)
}

func ErrDatabaseError(operation string, cause error) *LiveIMError {
	return NewLiveIMErrorWithCause(
		ErrCodeDatabaseError,
		fmt.Sprintf("Database operation failed: %s", operation),
		cause,
	).WithMetadata("operation", operation)
}

// Validation errors
func ErrValidationFailed(field, reason string) *LiveIMError {
	return NewLiveIMErrorWithDetails(
		ErrCodeValidationFailed,
		"Validation failed",
		reason,
	).WithField(field)
}

func ErrMissingField(field string) *LiveIMError {
	return NewLiveIMErrorWithDetails(
		ErrCodeMissingField,
		"Required field missing",
		fmt.Sprintf("Field '%s' is required", field),
	).WithField(field)
}

// Business logic errors
func ErrInsufficientFunds(userID string, required, available float64) *LiveIMError {
	return NewLiveIMErrorWithDetails(
		ErrCodeInsufficientFunds,
		"Insufficient funds",
		fmt.Sprintf("Required: %.2f, available: %.2f", required, available),
	).WithMetadata("user_id", userID).
		WithMetadata("required_amount", required).
		WithMetadata("available_amount", available)
}

func ErrQuotaExceeded(resource string, limit int, current int) *LiveIMError {
	return NewLiveIMErrorWithDetails(
		ErrCodeQuotaExceeded,
		"Quota exceeded",
		fmt.Sprintf("Resource: %s, limit: %d, current: %d", resource, limit, current),
	).WithMetadata("resource", resource).
		WithMetadata("limit", limit).
		WithMetadata("current", current)
}

// Helper function for min
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
