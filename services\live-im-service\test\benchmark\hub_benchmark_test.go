/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package benchmark

import (
	"context"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/mock"

	"cina.club/services/live-im-service/internal/application/port"
	"cina.club/services/live-im-service/internal/application/service"
	"cina.club/services/live-im-service/internal/domain/model"
)

// MockBroadcaster for benchmarking
type MockBroadcaster struct {
	mock.Mock
}

func (m *MockBroadcaster) Subscribe(ctx context.Context, handler port.BroadcastHandler) error {
	args := m.Called(ctx, handler)
	return args.Error(0)
}

func (m *MockBroadcaster) Unsubscribe(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

func (m *MockBroadcaster) Broadcast(ctx context.Context, roomID string, message model.Message) error {
	args := m.Called(ctx, roomID, message)
	return args.Error(0)
}

func (m *MockBroadcaster) Close() error {
	args := m.Called()
	return args.Error(0)
}

// MockRoomStore for benchmarking
type MockRoomStore struct {
	mock.Mock
}

func (m *MockRoomStore) Subscribe(ctx context.Context, userID uuid.UUID, roomID string) error {
	args := m.Called(ctx, userID, roomID)
	return args.Error(0)
}

func (m *MockRoomStore) Unsubscribe(ctx context.Context, userID uuid.UUID, roomID string) error {
	args := m.Called(ctx, userID, roomID)
	return args.Error(0)
}

func (m *MockRoomStore) GetRoomMembers(ctx context.Context, roomID string) ([]uuid.UUID, error) {
	args := m.Called(ctx, roomID)
	return args.Get(0).([]uuid.UUID), args.Error(1)
}

func (m *MockRoomStore) GetUserRooms(ctx context.Context, userID uuid.UUID) ([]string, error) {
	args := m.Called(ctx, userID)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockRoomStore) IsUserInRoom(ctx context.Context, userID uuid.UUID, roomID string) (bool, error) {
	args := m.Called(ctx, userID, roomID)
	return args.Bool(0), args.Error(1)
}

func (m *MockRoomStore) GetRoomCount(ctx context.Context, roomID string) (int, error) {
	args := m.Called(ctx, roomID)
	return args.Int(0), args.Error(1)
}

// MockRateLimiter for benchmarking
type MockRateLimiter struct {
	mock.Mock
}

func (m *MockRateLimiter) Allow(ctx context.Context, userID uuid.UUID, action string) (bool, error) {
	args := m.Called(ctx, userID, action)
	return args.Bool(0), args.Error(1)
}

func (m *MockRateLimiter) AllowN(ctx context.Context, userID uuid.UUID, action string, n int) (bool, error) {
	args := m.Called(ctx, userID, action, n)
	return args.Bool(0), args.Error(1)
}

func (m *MockRateLimiter) GetRemaining(ctx context.Context, userID uuid.UUID, action string) (int, error) {
	args := m.Called(ctx, userID, action)
	return args.Int(0), args.Error(1)
}

func (m *MockRateLimiter) Reset(ctx context.Context, userID uuid.UUID, action string) error {
	args := m.Called(ctx, userID, action)
	return args.Error(0)
}

// MockAuthService for benchmarking
type MockAuthService struct {
	mock.Mock
}

func (m *MockAuthService) ValidateToken(ctx context.Context, token string) (*port.UserInfo, error) {
	args := m.Called(ctx, token)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.UserInfo), args.Error(1)
}

func (m *MockAuthService) CheckRoomPermission(ctx context.Context, userID uuid.UUID, roomID string) (bool, error) {
	args := m.Called(ctx, userID, roomID)
	return args.Bool(0), args.Error(1)
}

// createBenchmarkHub creates a hub for benchmarking
func createBenchmarkHub(b *testing.B) (*service.Hub, *MockBroadcaster, *MockRoomStore, *MockRateLimiter) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce logging noise

	mockBroadcaster := &MockBroadcaster{}
	mockRoomStore := &MockRoomStore{}
	mockRateLimiter := &MockRateLimiter{}
	mockAuthService := &MockAuthService{}

	// Set up mock expectations for benchmarking
	mockBroadcaster.On("Subscribe", mock.Anything, mock.Anything).Return(nil)
	mockBroadcaster.On("Unsubscribe", mock.Anything).Return(nil)
	mockBroadcaster.On("Broadcast", mock.Anything, mock.Anything).Return(nil)
	mockRoomStore.On("Subscribe", mock.Anything, mock.Anything).Return(nil)
	mockRoomStore.On("Unsubscribe", mock.Anything, mock.Anything).Return(nil)
	mockRateLimiter.On("Allow", mock.Anything, mock.Anything).Return(true, nil)

	config := &service.HubConfig{
		MaxClients:            10000,
		MaxRooms:              1000,
		HeartbeatInterval:     30 * time.Second,
		CleanupInterval:       5 * time.Minute,
		StatsInterval:         10 * time.Second,
		MessageBufferSize:     1000,
		BroadcastBufferSize:   10000,
		LikeAggregateInterval: 5 * time.Second,
		MaxMessageSize:        1024,
		EnableMetrics:         false, // Disable for benchmarking
		EnableRoomAnalytics:   false, // Disable for benchmarking
	}

	mockMessageProcessor := service.NewMockMessageProcessor(logger)

	hub := service.NewHub(
		config,
		mockBroadcaster,
		mockRoomStore,
		mockMessageProcessor,
		mockRateLimiter,
		mockAuthService,
		logger,
	)

	return hub, mockBroadcaster, mockRoomStore, mockRateLimiter
}

// createBenchmarkClient creates a client for benchmarking
func createBenchmarkClient() *model.Client {
	return &model.Client{
		ID:          uuid.New().String(),
		UserID:      uuid.New(),
		ConnectedAt: time.Now(),
	}
}

// BenchmarkHubClientOperations benchmarks client registration/unregistration
func BenchmarkHubClientOperations(b *testing.B) {
	hub, _, _, _ := createBenchmarkHub(b)

	// Start the hub
	go hub.Start()
	defer hub.Stop()
	time.Sleep(10 * time.Millisecond) // Let hub start

	b.Run("ClientRegistration", func(b *testing.B) {
		clients := make([]*model.Client, b.N)
		for i := 0; i < b.N; i++ {
			clients[i] = createBenchmarkClient()
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			hub.RegisterClient(clients[i])
		}

		// Clean up
		for i := 0; i < b.N; i++ {
			hub.UnregisterClient(clients[i])
		}
	})

	b.Run("ClientUnregistration", func(b *testing.B) {
		// Pre-register clients
		clients := make([]*model.Client, b.N)
		for i := 0; i < b.N; i++ {
			clients[i] = createBenchmarkClient()
			hub.RegisterClient(clients[i])
		}

		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			hub.UnregisterClient(clients[i])
		}
	})
}

// BenchmarkHubMessageProcessing benchmarks message processing
func BenchmarkHubMessageProcessing(b *testing.B) {
	hub, _, _, _ := createBenchmarkHub(b)

	// Start the hub
	go hub.Start()
	defer hub.Stop()
	time.Sleep(10 * time.Millisecond)

	// Create and register a client
	client := createBenchmarkClient()
	hub.RegisterClient(client)

	// Join a room
	joinMsg := model.Message{
		Type:      model.MessageTypeJoinRoom,
		MessageID: uuid.New().String(),
		RoomID:    "bench-room",
		Timestamp: time.Now(),
	}
	hub.SendMessage(client, joinMsg)
	time.Sleep(10 * time.Millisecond)

	user := &model.User{
		UserID:   client.UserID,
		Username: "benchuser",
		Role:     model.RoleViewer,
	}

	b.Run("BarrageMessageProcessing", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			msg := model.Message{
				Type:      model.MessageTypeBarrage,
				MessageID: uuid.New().String(),
				Content:   "Benchmark message",
				Timestamp: time.Now(),
				FromUser:  user,
			}
			hub.SendMessage(client, msg)
		}
	})

	b.Run("LikeMessageProcessing", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			msg := model.Message{
				Type:      model.MessageTypeLike,
				MessageID: uuid.New().String(),
				Count:     1,
				Timestamp: time.Now(),
				FromUser:  user,
			}
			hub.SendMessage(client, msg)
		}
	})

	b.Run("PingMessageProcessing", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			msg := model.Message{
				Type:      model.MessageTypePing,
				MessageID: uuid.New().String(),
				Timestamp: time.Now(),
			}
			hub.SendMessage(client, msg)
		}
	})

	// Clean up
	hub.UnregisterClient(client)
}

// BenchmarkHubConcurrentOperations benchmarks concurrent operations
func BenchmarkHubConcurrentOperations(b *testing.B) {
	hub, _, _, _ := createBenchmarkHub(b)

	// Start the hub
	go hub.Start()
	defer hub.Stop()
	time.Sleep(10 * time.Millisecond)

	b.Run("ConcurrentClientRegistration", func(b *testing.B) {
		clients := make([]*model.Client, b.N)
		for i := 0; i < b.N; i++ {
			clients[i] = createBenchmarkClient()
		}

		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			i := 0
			for pb.Next() {
				if i < len(clients) {
					hub.RegisterClient(clients[i])
					i++
				}
			}
		})

		// Clean up
		for i := 0; i < b.N; i++ {
			hub.UnregisterClient(clients[i])
		}
	})

	b.Run("ConcurrentMessageSending", func(b *testing.B) {
		// Pre-register clients
		const numClients = 100
		clients := make([]*model.Client, numClients)
		for i := 0; i < numClients; i++ {
			clients[i] = createBenchmarkClient()
			hub.RegisterClient(clients[i])

			// Join room
			joinMsg := model.Message{
				Type:      model.MessageTypeJoinRoom,
				MessageID: uuid.New().String(),
				RoomID:    "bench-room",
				Timestamp: time.Now(),
			}
			hub.SendMessage(clients[i], joinMsg)
		}
		time.Sleep(50 * time.Millisecond)

		user := &model.User{
			UserID:   uuid.New(),
			Username: "benchuser",
			Role:     model.RoleViewer,
		}

		b.ResetTimer()
		b.RunParallel(func(pb *testing.PB) {
			clientIndex := 0
			for pb.Next() {
				client := clients[clientIndex%numClients]
				msg := model.Message{
					Type:      model.MessageTypeBarrage,
					MessageID: uuid.New().String(),
					Content:   "Concurrent benchmark message",
					Timestamp: time.Now(),
					FromUser:  user,
				}
				hub.SendMessage(client, msg)
				clientIndex++
			}
		})

		// Clean up
		for i := 0; i < numClients; i++ {
			hub.UnregisterClient(clients[i])
		}
	})
}

// BenchmarkHubStatistics benchmarks statistics collection
func BenchmarkHubStatistics(b *testing.B) {
	hub, _, _, _ := createBenchmarkHub(b)

	// Start the hub
	go hub.Start()
	defer hub.Stop()
	time.Sleep(10 * time.Millisecond)

	// Register some clients for realistic stats
	const numClients = 100
	clients := make([]*model.Client, numClients)
	for i := 0; i < numClients; i++ {
		clients[i] = createBenchmarkClient()
		hub.RegisterClient(clients[i])
	}

	b.Run("GetStats", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = hub.GetStats()
		}
	})

	b.Run("GetConnectedClientsCount", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = hub.GetConnectedClientsCount()
		}
	})

	b.Run("GetActiveRoomsCount", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_ = hub.GetActiveRoomsCount()
		}
	})

	// Clean up
	for i := 0; i < numClients; i++ {
		hub.UnregisterClient(clients[i])
	}
}
