/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00

This file demonstrates how to integrate circuit breakers into the Live IM Service.
*/

package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/go-redis/redis/v8"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"

	"cina.club/services/live-im-service/internal/application/port"
	"cina.club/services/live-im-service/internal/domain/model"
	"cina.club/services/live-im-service/pkg/circuitbreaker"
	"cina.club/services/live-im-service/pkg/errors"
)

// Example demonstrates circuit breaker integration
func main() {
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// Setup circuit breaker manager
	cbManager := setupCircuitBreakerManager(logger)
	defer cbManager.Close()

	// Setup HTTP server with circuit breaker endpoints
	router := gin.Default()
	
	// Add circuit breaker middleware
	cbHandler := circuitbreaker.NewHTTPHandler(cbManager, logger)
	router.Use(cbHandler.Middleware())
	
	// Register circuit breaker management endpoints
	api := router.Group("/api/v1")
	cbHandler.RegisterRoutes(api)
	
	// Example service endpoints
	setupExampleEndpoints(router, cbManager, logger)

	// Start server
	log.Println("Starting server on :8080")
	log.Println("Circuit breaker endpoints available at:")
	log.Println("  GET /api/v1/circuit-breakers/")
	log.Println("  GET /api/v1/circuit-breakers/health")
	log.Println("  POST /api/v1/circuit-breakers/{name}/reset")
	
	if err := router.Run(":8080"); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}

// setupCircuitBreakerManager creates and configures the circuit breaker manager
func setupCircuitBreakerManager(logger *logrus.Logger) *circuitbreaker.Manager {
	config := circuitbreaker.ManagerConfig{
		DefaultConfig: circuitbreaker.Config{
			MaxRequests: 10,
			Interval:    60 * time.Second,
			Timeout:     30 * time.Second,
			ReadyToTrip: func(counts circuitbreaker.Counts) bool {
				failureRate := float64(counts.TotalFailures) / float64(counts.Requests)
				return counts.Requests >= 10 && failureRate >= 0.6
			},
		},
		ServiceConfigs: map[string]circuitbreaker.Config{
			"redis": {
				MaxRequests: 5,
				Timeout:     15 * time.Second,
				ReadyToTrip: func(counts circuitbreaker.Counts) bool {
					return counts.ConsecutiveFailures >= 3
				},
			},
			"live_api": {
				MaxRequests: 8,
				Timeout:     45 * time.Second,
				ReadyToTrip: func(counts circuitbreaker.Counts) bool {
					return counts.ConsecutiveFailures >= 5
				},
			},
			"billing": {
				MaxRequests: 3,
				Timeout:     60 * time.Second,
				ReadyToTrip: func(counts circuitbreaker.Counts) bool {
					// More conservative for billing
					return counts.ConsecutiveFailures >= 2
				},
			},
			"cina_coin": {
				MaxRequests: 5,
				Timeout:     30 * time.Second,
				ReadyToTrip: func(counts circuitbreaker.Counts) bool {
					return counts.ConsecutiveFailures >= 3
				},
			},
			"auth": {
				MaxRequests: 10,
				Timeout:     20 * time.Second,
				ReadyToTrip: func(counts circuitbreaker.Counts) bool {
					failureRate := float64(counts.TotalFailures) / float64(counts.Requests)
					return counts.Requests >= 5 && failureRate >= 0.5
				},
			},
		},
		MetricsCollector: circuitbreaker.NewDefaultMetricsCollector(logger),
	}

	return circuitbreaker.NewManager(config, logger)
}

// setupExampleEndpoints demonstrates how to use circuit breakers in HTTP handlers
func setupExampleEndpoints(router *gin.Engine, cbManager *circuitbreaker.Manager, logger *logrus.Logger) {
	// Example Redis operations
	router.GET("/redis/get/:key", func(c *gin.Context) {
		key := c.Param("key")
		
		var result string
		err := cbManager.ExecuteWithContext(c.Request.Context(), "redis", func(ctx context.Context) error {
			// Simulate Redis GET operation
			if key == "fail" {
				return fmt.Errorf("simulated Redis failure")
			}
			result = fmt.Sprintf("value-for-%s", key)
			return nil
		})

		if err != nil {
			if err == circuitbreaker.ErrOpenState {
				c.JSON(http.StatusServiceUnavailable, gin.H{
					"error": "Redis service is currently unavailable",
					"fallback": "Using cached data",
				})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, gin.H{"key": key, "value": result})
	})

	// Example Live API operations
	router.GET("/room/:id/info", func(c *gin.Context) {
		roomID := c.Param("id")
		
		var roomInfo *model.RoomInfo
		err := cbManager.ExecuteWithContext(c.Request.Context(), "live_api", func(ctx context.Context) error {
			// Simulate Live API call
			if roomID == "fail" {
				return fmt.Errorf("simulated Live API failure")
			}
			roomInfo = &model.RoomInfo{
				RoomID:      roomID,
				Title:       "Example Room",
				Description: "This is an example room",
				Status:      "active",
			}
			return nil
		})

		if err != nil {
			if err == circuitbreaker.ErrOpenState {
				c.JSON(http.StatusServiceUnavailable, gin.H{
					"error": "Live API service is currently unavailable",
					"fallback": "Limited room information available",
				})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusOK, roomInfo)
	})

	// Example billing operations
	router.POST("/billing/invoice", func(c *gin.Context) {
		var request struct {
			UserID      string `json:"user_id"`
			Amount      int    `json:"amount"`
			Description string `json:"description"`
		}

		if err := c.ShouldBindJSON(&request); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		var invoice *port.Invoice
		err := cbManager.ExecuteWithContext(c.Request.Context(), "billing", func(ctx context.Context) error {
			// Simulate billing service call
			if request.Amount < 0 {
				return fmt.Errorf("simulated billing failure")
			}
			
			userID, _ := uuid.Parse(request.UserID)
			invoice = &port.Invoice{
				ID:          uuid.New().String(),
				UserID:      userID,
				Amount:      request.Amount,
				Description: request.Description,
				Status:      "pending",
				CreatedAt:   time.Now(),
			}
			return nil
		})

		if err != nil {
			if err == circuitbreaker.ErrOpenState {
				c.JSON(http.StatusServiceUnavailable, gin.H{
					"error": "Billing service is currently unavailable",
					"message": "Please try again later",
				})
				return
			}
			c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
			return
		}

		c.JSON(http.StatusCreated, invoice)
	})

	// Example endpoint that demonstrates graceful degradation
	router.GET("/user/:id/profile", func(c *gin.Context) {
		userID := c.Param("id")
		
		// Try to get user info from auth service
		var userInfo *port.UserInfo
		authErr := cbManager.ExecuteWithContext(c.Request.Context(), "auth", func(ctx context.Context) error {
			if userID == "fail" {
				return fmt.Errorf("simulated auth failure")
			}
			
			uid, _ := uuid.Parse(userID)
			userInfo = &port.UserInfo{
				UserID:   uid,
				Username: "user-" + userID,
				Avatar:   "https://example.com/avatar.jpg",
				Role:     model.RoleViewer,
				Level:    5,
				VIPLevel: 1,
				Badges:   []string{"newcomer"},
			}
			return nil
		})

		// Try to get balance from CinaCoin service
		var balance int
		coinErr := cbManager.ExecuteWithContext(c.Request.Context(), "cina_coin", func(ctx context.Context) error {
			if userID == "fail" {
				return fmt.Errorf("simulated coin service failure")
			}
			balance = 1000 // Simulated balance
			return nil
		})

		// Build response with available data
		response := gin.H{
			"user_id": userID,
			"services": gin.H{
				"auth": gin.H{
					"available": authErr == nil,
				},
				"cina_coin": gin.H{
					"available": coinErr == nil,
				},
			},
		}

		if authErr == nil {
			response["user_info"] = userInfo
		} else if authErr == circuitbreaker.ErrOpenState {
			response["user_info"] = gin.H{
				"error": "Auth service unavailable",
				"fallback": "Limited user information",
			}
		}

		if coinErr == nil {
			response["balance"] = balance
		} else if coinErr == circuitbreaker.ErrOpenState {
			response["balance"] = gin.H{
				"error": "CinaCoin service unavailable",
				"fallback": "Balance information not available",
			}
		}

		// Determine overall status
		status := http.StatusOK
		if authErr == circuitbreaker.ErrOpenState || coinErr == circuitbreaker.ErrOpenState {
			status = http.StatusPartialContent
		}

		c.JSON(status, response)
	})

	// Test endpoints to simulate failures
	router.POST("/test/simulate-failure/:service", func(c *gin.Context) {
		serviceName := c.Param("service")
		
		// Simulate multiple failures to trigger circuit breaker
		for i := 0; i < 10; i++ {
			cbManager.Execute(serviceName, func() error {
				return fmt.Errorf("simulated failure %d", i+1)
			})
		}

		c.JSON(http.StatusOK, gin.H{
			"message": fmt.Sprintf("Simulated failures for service: %s", serviceName),
			"note": "Check circuit breaker status at /api/v1/circuit-breakers/",
		})
	})
}

// MockRedisClient demonstrates how to wrap Redis client with circuit breaker
type MockRedisClient struct {
	client  *redis.Client
	manager *circuitbreaker.Manager
	logger  *logrus.Logger
}

func NewMockRedisClient(client *redis.Client, manager *circuitbreaker.Manager, logger *logrus.Logger) *MockRedisClient {
	return &MockRedisClient{
		client:  client,
		manager: manager,
		logger:  logger,
	}
}

func (m *MockRedisClient) Get(ctx context.Context, key string) (string, error) {
	var result string
	err := m.manager.ExecuteWithContext(ctx, "redis", func(ctx context.Context) error {
		cmd := m.client.Get(ctx, key)
		if cmd.Err() != nil {
			return cmd.Err()
		}
		result = cmd.Val()
		return nil
	})
	return result, err
}

func (m *MockRedisClient) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return m.manager.ExecuteWithContext(ctx, "redis", func(ctx context.Context) error {
		return m.client.Set(ctx, key, value, expiration).Err()
	})
}

// Example of how to integrate with the Hub service
type HubServiceWithCircuitBreaker struct {
	cbManager *circuitbreaker.Manager
	logger    *logrus.Logger
}

func (h *HubServiceWithCircuitBreaker) JoinRoom(ctx context.Context, userID uuid.UUID, roomID string) error {
	// Check room access with circuit breaker protection
	err := h.cbManager.ExecuteWithContext(ctx, "live_api", func(ctx context.Context) error {
		// Simulate room access check
		if roomID == "private" {
			return errors.ErrPermissionDenied("join_room", userID.String())
		}
		return nil
	})

	if err != nil {
		if err == circuitbreaker.ErrOpenState {
			// Fallback: allow join but log for monitoring
			h.logger.Warn("Live API unavailable, allowing room join with degraded validation")
			return h.joinRoomWithDegradedValidation(ctx, userID, roomID)
		}
		return err
	}

	return h.joinRoomNormally(ctx, userID, roomID)
}

func (h *HubServiceWithCircuitBreaker) joinRoomWithDegradedValidation(ctx context.Context, userID uuid.UUID, roomID string) error {
	// Implement fallback logic
	h.logger.WithFields(logrus.Fields{
		"user_id": userID,
		"room_id": roomID,
		"mode":    "degraded",
	}).Info("User joined room with degraded validation")
	return nil
}

func (h *HubServiceWithCircuitBreaker) joinRoomNormally(ctx context.Context, userID uuid.UUID, roomID string) error {
	// Implement normal join logic
	h.logger.WithFields(logrus.Fields{
		"user_id": userID,
		"room_id": roomID,
		"mode":    "normal",
	}).Info("User joined room normally")
	return nil
}
