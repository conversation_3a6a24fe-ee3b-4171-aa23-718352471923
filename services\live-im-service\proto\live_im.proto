/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:12:13
Modified: 2025-07-11 10:12:13
*/

syntax = "proto3";

package live_im;

option go_package = "cina.club/services/live-im-service/proto/live_im";

import "google/protobuf/timestamp.proto";

// MessageType defines the type of message
enum MessageType {
  MESSAGE_TYPE_UNSPECIFIED = 0;
  MESSAGE_TYPE_AUTH = 1;
  MESSAGE_TYPE_AUTH_RESULT = 2;
  MESSAGE_TYPE_BARRAGE = 3;
  MESSAGE_TYPE_LIKE = 4;
  MESSAGE_TYPE_GIFT = 5;
  MESSAGE_TYPE_PING = 6;
  MESSAGE_TYPE_PONG = 7;
  MESSAGE_TYPE_ERROR = 8;
  MESSAGE_TYPE_RATE_LIMITED = 9;
  MESSAGE_TYPE_NEW_BARRAGE = 10;
  MESSAGE_TYPE_LIKE_BURST = 11;
  MESSAGE_TYPE_NEW_GIFT = 12;
  MESSAGE_TYPE_USER_JOIN = 13;
  MESSAGE_TYPE_USER_LEAVE = 14;
  MESSAGE_TYPE_ROOM_UPDATE = 15;
}

// UserRole defines user roles
enum UserRole {
  USER_ROLE_UNSPECIFIED = 0;
  USER_ROLE_VIEWER = 1;
  USER_ROLE_STREAMER = 2;
  USER_ROLE_MODERATOR = 3;
  USER_ROLE_ADMIN = 4;
  USER_ROLE_VIP = 5;
}

// User represents a user
message User {
  string user_id = 1;
  string username = 2;
  string avatar = 3;
  UserRole role = 4;
  int32 level = 5;
  int32 vip_level = 6;
  repeated string badges = 7;
}

// RoomInfo represents room information
message RoomInfo {
  string room_id = 1;
  string title = 2;
  string status = 3;
  int32 online_count = 4;
  google.protobuf.Timestamp start_time = 5;
  string streamer_id = 6;
  string streamer_name = 7;
  string streamer_avatar = 8;
}

// Message represents a live IM message
message Message {
  string message_id = 1;
  MessageType type = 2;
  google.protobuf.Timestamp timestamp = 3;
  
  // Authentication fields
  string token = 4;
  string room_id = 5;
  
  // Content fields
  string content = 6;
  int32 count = 7;
  string gift_id = 8;
  string to_user_id = 9;
  
  // User information
  User from_user = 10;
  
  // Response fields
  bool success = 11;
  string error_message = 12;
  RoomInfo room_info = 13;
  repeated User online_users = 14;
  
  // Additional metadata
  map<string, string> metadata = 15;
}

// AuthRequest represents an authentication request
message AuthRequest {
  string token = 1;
  string room_id = 2;
  string client_version = 3;
  string platform = 4;
}

// AuthResponse represents an authentication response
message AuthResponse {
  bool success = 1;
  string error_message = 2;
  User user_info = 3;
  RoomInfo room_info = 4;
  repeated User online_users = 5;
  string session_id = 6;
}

// BarrageMessage represents a barrage message
message BarrageMessage {
  string message_id = 1;
  string content = 2;
  User from_user = 3;
  string room_id = 4;
  google.protobuf.Timestamp timestamp = 5;
  map<string, string> metadata = 6;
}

// LikeMessage represents a like message
message LikeMessage {
  string message_id = 1;
  int32 count = 2;
  User from_user = 3;
  string room_id = 4;
  google.protobuf.Timestamp timestamp = 5;
}

// LikeBurstMessage represents aggregated likes
message LikeBurstMessage {
  string message_id = 1;
  int32 total_count = 2;
  string room_id = 3;
  google.protobuf.Timestamp timestamp = 4;
}

// GiftMessage represents a gift message
message GiftMessage {
  string message_id = 1;
  string gift_id = 2;
  string gift_name = 3;
  int32 gift_count = 4;
  int32 gift_price = 5;
  User from_user = 6;
  User to_user = 7;
  string room_id = 8;
  google.protobuf.Timestamp timestamp = 9;
  string transaction_id = 10;
  map<string, string> metadata = 11;
}

// UserJoinMessage represents a user joining the room
message UserJoinMessage {
  string message_id = 1;
  User user = 2;
  string room_id = 3;
  google.protobuf.Timestamp timestamp = 4;
  int32 new_online_count = 5;
}

// UserLeaveMessage represents a user leaving the room
message UserLeaveMessage {
  string message_id = 1;
  User user = 2;
  string room_id = 3;
  google.protobuf.Timestamp timestamp = 4;
  int32 new_online_count = 5;
}

// RoomUpdateMessage represents room information updates
message RoomUpdateMessage {
  string message_id = 1;
  RoomInfo room_info = 2;
  google.protobuf.Timestamp timestamp = 3;
}

// ErrorMessage represents an error message
message ErrorMessage {
  string message_id = 1;
  string error_code = 2;
  string error_message = 3;
  google.protobuf.Timestamp timestamp = 4;
}

// PingMessage represents a ping message
message PingMessage {
  string message_id = 1;
  google.protobuf.Timestamp timestamp = 2;
}

// PongMessage represents a pong message
message PongMessage {
  string message_id = 1;
  google.protobuf.Timestamp timestamp = 2;
  google.protobuf.Timestamp ping_timestamp = 3;
}

// RateLimitedMessage represents a rate limited response
message RateLimitedMessage {
  string message_id = 1;
  string action = 2;
  int32 remaining_quota = 3;
  google.protobuf.Timestamp reset_time = 4;
  google.protobuf.Timestamp timestamp = 5;
}

// Batch message for efficient transmission
message MessageBatch {
  repeated Message messages = 1;
  string batch_id = 2;
  google.protobuf.Timestamp timestamp = 3;
  int32 sequence_number = 4;
}
