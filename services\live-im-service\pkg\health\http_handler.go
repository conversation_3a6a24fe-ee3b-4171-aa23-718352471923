/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package health

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// HTTPHandler provides HTTP endpoints for health checks
type HTTPHandler struct {
	service *Service
	logger  *logrus.Logger
}

// NewHTTPHandler creates a new HTTP handler for health checks
func NewHTTPHandler(service *Service, logger *logrus.Logger) *HTTPHandler {
	return &HTTPHandler{
		service: service,
		logger:  logger,
	}
}

// RegisterRoutes registers health check routes with a Gin router
func (h *HTTPHandler) RegisterRoutes(router *gin.RouterGroup) {
	health := router.Group("/health")
	{
		health.GET("", h.Health)
		health.GET("/", h.Health)
		health.GET("/live", h.Liveness)
		health.GET("/ready", h.Readiness)
		health.GET("/checkers", h.<PERSON>Che<PERSON>)
		health.GET("/check/:name", h.<PERSON>)
	}
}

// Health performs comprehensive health checks
// @Summary Comprehensive health check
// @Description Returns the overall health status of the service and all its dependencies
// @Tags health
// @Accept json
// @Produce json
// @Param timeout query int false "Timeout in seconds (default: 30)"
// @Param include query string false "Comma-separated list of checks to include"
// @Param exclude query string false "Comma-separated list of checks to exclude"
// @Success 200 {object} OverallHealth
// @Failure 503 {object} OverallHealth
// @Router /health [get]
func (h *HTTPHandler) Health(c *gin.Context) {
	// Parse timeout
	timeout := 30 * time.Second
	if timeoutStr := c.Query("timeout"); timeoutStr != "" {
		if timeoutSec, err := strconv.Atoi(timeoutStr); err == nil && timeoutSec > 0 {
			timeout = time.Duration(timeoutSec) * time.Second
		}
	}

	// Create context with timeout
	ctx, cancel := c.Request.Context(), func() {}
	if timeout > 0 {
		ctx, cancel = c.Request.Context(), func() {}
		// Note: We'll let the individual checkers handle their own timeouts
	}
	defer cancel()

	// Perform health check
	health := h.service.Check(ctx)

	// Determine HTTP status code
	statusCode := http.StatusOK
	switch health.Status {
	case StatusUnhealthy:
		statusCode = http.StatusServiceUnavailable
	case StatusDegraded:
		statusCode = http.StatusOK // Still return 200 for degraded
	}

	// Log health check result
	h.logger.WithFields(logrus.Fields{
		"status":       health.Status,
		"total_checks": health.Summary.Total,
		"healthy":      health.Summary.Healthy,
		"degraded":     health.Summary.Degraded,
		"unhealthy":    health.Summary.Unhealthy,
		"duration_ms":  time.Since(health.Timestamp).Milliseconds(),
	}).Info("Health check completed")

	c.JSON(statusCode, health)
}

// Liveness provides a simple liveness probe
// @Summary Liveness probe
// @Description Returns whether the service is alive (basic liveness check)
// @Tags health
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /health/live [get]
func (h *HTTPHandler) Liveness(c *gin.Context) {
	liveness := h.service.Liveness()
	c.JSON(http.StatusOK, liveness)
}

// Readiness provides a readiness probe
// @Summary Readiness probe
// @Description Returns whether the service is ready to accept traffic (critical dependencies check)
// @Tags health
// @Accept json
// @Produce json
// @Param timeout query int false "Timeout in seconds (default: 10)"
// @Success 200 {object} map[string]interface{}
// @Failure 503 {object} map[string]interface{}
// @Router /health/ready [get]
func (h *HTTPHandler) Readiness(c *gin.Context) {
	ctx := c.Request.Context()
	readiness := h.service.Readiness(ctx)

	// Determine HTTP status code
	statusCode := http.StatusOK
	if status, ok := readiness["status"].(string); ok && status == "not_ready" {
		statusCode = http.StatusServiceUnavailable
	}

	c.JSON(statusCode, readiness)
}

// GetCheckers returns the list of registered health checkers
// @Summary Get health checkers
// @Description Returns the list of all registered health checkers
// @Tags health
// @Accept json
// @Produce json
// @Success 200 {object} map[string]interface{}
// @Router /health/checkers [get]
func (h *HTTPHandler) GetCheckers(c *gin.Context) {
	checkers := h.service.GetCheckers()

	response := map[string]interface{}{
		"checkers":  checkers,
		"count":     len(checkers),
		"timestamp": time.Now(),
	}

	c.JSON(http.StatusOK, response)
}

// CheckSingle performs a single health check
// @Summary Single health check
// @Description Performs a health check for a specific component
// @Tags health
// @Accept json
// @Produce json
// @Param name path string true "Health checker name"
// @Param timeout query int false "Timeout in seconds (default: checker's default)"
// @Success 200 {object} CheckResult
// @Failure 404 {object} map[string]interface{}
// @Failure 500 {object} map[string]interface{}
// @Router /health/check/{name} [get]
func (h *HTTPHandler) CheckSingle(c *gin.Context) {
	name := c.Param("name")

	ctx := c.Request.Context()
	result, err := h.service.CheckSingle(ctx, name)

	if err != nil {
		h.logger.WithFields(logrus.Fields{
			"checker": name,
			"error":   err.Error(),
		}).Warn("Health checker not found")

		c.JSON(http.StatusNotFound, map[string]interface{}{
			"error":     "Health checker not found",
			"checker":   name,
			"timestamp": time.Now(),
		})
		return
	}

	// Determine HTTP status code based on health status
	statusCode := http.StatusOK
	switch result.Status {
	case StatusUnhealthy:
		statusCode = http.StatusServiceUnavailable
	case StatusDegraded:
		statusCode = http.StatusOK
	}

	c.JSON(statusCode, result)
}

// Middleware provides a Gin middleware that adds health information to request context
func (h *HTTPHandler) Middleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// Add health service to context for use in other handlers
		c.Set("health_service", h.service)
		c.Next()
	}
}

// GetServiceFromContext extracts the health service from Gin context
func GetServiceFromContext(c *gin.Context) (*Service, bool) {
	service, exists := c.Get("health_service")
	if !exists {
		return nil, false
	}

	healthService, ok := service.(*Service)
	return healthService, ok
}

// HealthCheckMiddleware provides middleware that can be used to check health before processing requests
func HealthCheckMiddleware(service *Service, logger *logrus.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		// Quick readiness check for critical services
		ctx := c.Request.Context()
		readiness := service.Readiness(ctx)

		if status, ok := readiness["status"].(string); ok && status == "not_ready" {
			logger.Warn("Service not ready, rejecting request")
			c.JSON(http.StatusServiceUnavailable, map[string]interface{}{
				"error":     "Service not ready",
				"message":   "Service is not ready to accept requests",
				"readiness": readiness,
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// PrometheusMetrics provides Prometheus-compatible metrics for health checks
type PrometheusMetrics struct {
	service *Service
}

// NewPrometheusMetrics creates a new Prometheus metrics provider for health checks
func NewPrometheusMetrics(service *Service) *PrometheusMetrics {
	return &PrometheusMetrics{service: service}
}

// GetMetrics returns Prometheus-formatted metrics for health checks
func (p *PrometheusMetrics) GetMetrics() string {
	// This would typically integrate with the actual Prometheus client
	// For now, we'll return a simple text format

	checkers := p.service.GetCheckers()
	metrics := ""

	// Health check status metric
	metrics += "# HELP health_check_status Health check status (0=unknown, 1=healthy, 2=degraded, 3=unhealthy)\n"
	metrics += "# TYPE health_check_status gauge\n"

	for _, checkerName := range checkers {
		// This would require running the actual health checks
		// In a real implementation, you'd cache recent results
		metrics += fmt.Sprintf("health_check_status{checker=\"%s\"} 1\n", checkerName)
	}

	// Health check duration metric
	metrics += "# HELP health_check_duration_seconds Duration of health checks in seconds\n"
	metrics += "# TYPE health_check_duration_seconds histogram\n"

	// Service uptime metric
	uptime := time.Since(p.service.startTime)
	metrics += "# HELP service_uptime_seconds Service uptime in seconds\n"
	metrics += "# TYPE service_uptime_seconds counter\n"
	metrics += fmt.Sprintf("service_uptime_seconds %f\n", uptime.Seconds())

	return metrics
}

// HealthDashboard provides a simple HTML dashboard for health status
func (h *HTTPHandler) HealthDashboard(c *gin.Context) {
	ctx := c.Request.Context()
	health := h.service.Check(ctx)

	html := `
<!DOCTYPE html>
<html>
<head>
    <title>Live IM Service Health Dashboard</title>
    <meta http-equiv="refresh" content="30">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .healthy { background-color: #d4edda; color: #155724; }
        .degraded { background-color: #fff3cd; color: #856404; }
        .unhealthy { background-color: #f8d7da; color: #721c24; }
        .details { margin-left: 20px; font-size: 0.9em; }
        table { border-collapse: collapse; width: 100%; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
    </style>
</head>
<body>
    <h1>Live IM Service Health Dashboard</h1>
    <div class="status ` + string(health.Status) + `">
        <h2>Overall Status: ` + string(health.Status) + `</h2>
        <p>` + health.Message + `</p>
        <p>Last Updated: ` + health.Timestamp.Format(time.RFC3339) + `</p>
        <p>Uptime: ` + health.Uptime.String() + `</p>
        <p>Version: ` + health.Version + `</p>
    </div>
    
    <h3>Health Check Summary</h3>
    <table>
        <tr><th>Status</th><th>Count</th></tr>
        <tr><td>Healthy</td><td>` + strconv.Itoa(health.Summary.Healthy) + `</td></tr>
        <tr><td>Degraded</td><td>` + strconv.Itoa(health.Summary.Degraded) + `</td></tr>
        <tr><td>Unhealthy</td><td>` + strconv.Itoa(health.Summary.Unhealthy) + `</td></tr>
        <tr><td>Unknown</td><td>` + strconv.Itoa(health.Summary.Unknown) + `</td></tr>
    </table>
    
    <h3>Individual Health Checks</h3>`

	for name, check := range health.Checks {
		html += `
    <div class="status ` + string(check.Status) + `">
        <h4>` + name + ` (` + string(check.Status) + `)</h4>
        <p>` + check.Message + `</p>
        <p>Duration: ` + check.Duration.String() + `</p>
        <div class="details">
            <pre>` + fmt.Sprintf("%+v", check.Details) + `</pre>
        </div>
    </div>`
	}

	html += `
</body>
</html>`

	c.Header("Content-Type", "text/html")
	c.String(http.StatusOK, html)
}
