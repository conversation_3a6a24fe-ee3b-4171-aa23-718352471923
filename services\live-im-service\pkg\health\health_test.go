/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package health

import (
	"context"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
)

// <PERSON><PERSON><PERSON><PERSON><PERSON> implements the Checker interface for testing
type Mock<PERSON><PERSON><PERSON> struct {
	name     string
	timeout  time.Duration
	critical bool
	result   CheckResult
	delay    time.Duration
}

func NewMockChecker(name string, status Status, critical bool) *<PERSON>ck<PERSON>he<PERSON> {
	return &MockChecker{
		name:     name,
		timeout:  5 * time.Second,
		critical: critical,
		result: CheckResult{
			Name:    name,
			Status:  status,
			Message: "Mock checker result",
		},
	}
}

func (m *MockChecker) Name() string           { return m.name }
func (m *<PERSON>ck<PERSON>hecker) Timeout() time.Duration { return m.timeout }
func (m *<PERSON>ckChecker) Critical() bool         { return m.critical }

func (m *<PERSON>ck<PERSON>hecker) Check(ctx context.Context) CheckResult {
	if m.delay > 0 {
		select {
		case <-ctx.Done():
			return CheckResult{
				Name:    m.name,
				Status:  StatusUnknown,
				Message: "Context cancelled",
				Error:   ctx.Err().Error(),
			}
		case <-time.After(m.delay):
		}
	}
	return m.result
}

func (m *MockChecker) SetDelay(delay time.Duration) {
	m.delay = delay
}

func (m *MockChecker) SetResult(result CheckResult) {
	m.result = result
}

func TestHealthService_RegisterChecker(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	config := Config{
		Version:     "1.0.0",
		Environment: "test",
	}

	service := NewService(config, logger)

	t.Run("Register checker", func(t *testing.T) {
		checker := NewMockChecker("test-checker", StatusHealthy, false)
		service.RegisterChecker(checker)

		checkers := service.GetCheckers()
		assert.Contains(t, checkers, "test-checker")
	})

	t.Run("Unregister checker", func(t *testing.T) {
		service.UnregisterChecker("test-checker")

		checkers := service.GetCheckers()
		assert.NotContains(t, checkers, "test-checker")
	})
}

func TestHealthService_Check(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	config := Config{
		Version:     "1.0.0",
		Environment: "test",
	}

	service := NewService(config, logger)

	t.Run("All healthy", func(t *testing.T) {
		service.RegisterChecker(NewMockChecker("checker1", StatusHealthy, false))
		service.RegisterChecker(NewMockChecker("checker2", StatusHealthy, true))

		ctx := context.Background()
		health := service.Check(ctx)

		assert.Equal(t, StatusHealthy, health.Status)
		assert.Equal(t, 2, health.Summary.Total)
		assert.Equal(t, 2, health.Summary.Healthy)
		assert.Equal(t, 0, health.Summary.Degraded)
		assert.Equal(t, 0, health.Summary.Unhealthy)
		assert.Contains(t, health.Checks, "checker1")
		assert.Contains(t, health.Checks, "checker2")
	})

	t.Run("Mixed status", func(t *testing.T) {
		service.UnregisterChecker("checker1")
		service.UnregisterChecker("checker2")

		service.RegisterChecker(NewMockChecker("healthy", StatusHealthy, false))
		service.RegisterChecker(NewMockChecker("degraded", StatusDegraded, false))
		service.RegisterChecker(NewMockChecker("unhealthy", StatusUnhealthy, false))

		ctx := context.Background()
		health := service.Check(ctx)

		assert.Equal(t, StatusUnhealthy, health.Status)
		assert.Equal(t, 3, health.Summary.Total)
		assert.Equal(t, 1, health.Summary.Healthy)
		assert.Equal(t, 1, health.Summary.Degraded)
		assert.Equal(t, 1, health.Summary.Unhealthy)
	})

	t.Run("Critical unhealthy", func(t *testing.T) {
		service.UnregisterChecker("healthy")
		service.UnregisterChecker("degraded")
		service.UnregisterChecker("unhealthy")

		service.RegisterChecker(NewMockChecker("critical", StatusUnhealthy, true))
		service.RegisterChecker(NewMockChecker("non-critical", StatusHealthy, false))

		ctx := context.Background()
		health := service.Check(ctx)

		assert.Equal(t, StatusUnhealthy, health.Status)
		assert.Contains(t, health.Message, "unhealthy")
	})
}

func TestHealthService_CheckSingle(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	config := Config{
		Version:     "1.0.0",
		Environment: "test",
	}

	service := NewService(config, logger)

	t.Run("Existing checker", func(t *testing.T) {
		checker := NewMockChecker("single-test", StatusHealthy, false)
		service.RegisterChecker(checker)

		ctx := context.Background()
		result, err := service.CheckSingle(ctx, "single-test")

		assert.NoError(t, err)
		assert.Equal(t, "single-test", result.Name)
		assert.Equal(t, StatusHealthy, result.Status)
	})

	t.Run("Non-existent checker", func(t *testing.T) {
		ctx := context.Background()
		_, err := service.CheckSingle(ctx, "non-existent")

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "not found")
	})
}

func TestHealthService_Liveness(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	config := Config{
		Version:     "1.0.0",
		Environment: "test",
	}

	service := NewService(config, logger)

	t.Run("Liveness check", func(t *testing.T) {
		liveness := service.Liveness()

		assert.Equal(t, "alive", liveness["status"])
		assert.Contains(t, liveness, "timestamp")
		assert.Contains(t, liveness, "uptime")
	})
}

func TestHealthService_Readiness(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	config := Config{
		Version:     "1.0.0",
		Environment: "test",
	}

	service := NewService(config, logger)

	t.Run("No critical checkers", func(t *testing.T) {
		ctx := context.Background()
		readiness := service.Readiness(ctx)

		assert.Equal(t, "ready", readiness["status"])
		assert.Contains(t, readiness, "timestamp")
	})

	t.Run("Critical checker healthy", func(t *testing.T) {
		service.RegisterChecker(NewMockChecker("critical-healthy", StatusHealthy, true))

		ctx := context.Background()
		readiness := service.Readiness(ctx)

		assert.Equal(t, "ready", readiness["status"])
	})

	t.Run("Critical checker unhealthy", func(t *testing.T) {
		service.UnregisterChecker("critical-healthy")
		service.RegisterChecker(NewMockChecker("critical-unhealthy", StatusUnhealthy, true))

		ctx := context.Background()
		readiness := service.Readiness(ctx)

		assert.Equal(t, "not_ready", readiness["status"])
	})

	t.Run("Critical checker degraded", func(t *testing.T) {
		service.UnregisterChecker("critical-unhealthy")
		service.RegisterChecker(NewMockChecker("critical-degraded", StatusDegraded, true))

		ctx := context.Background()
		readiness := service.Readiness(ctx)

		assert.Equal(t, "ready", readiness["status"]) // Degraded is still ready
	})
}

func TestHealthService_ContextTimeout(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	config := Config{
		Version:     "1.0.0",
		Environment: "test",
		Timeout:     1 * time.Second,
	}

	service := NewService(config, logger)

	t.Run("Context timeout", func(t *testing.T) {
		checker := NewMockChecker("slow-checker", StatusHealthy, false)
		checker.SetDelay(2 * time.Second) // Longer than context timeout
		service.RegisterChecker(checker)

		ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
		defer cancel()

		health := service.Check(ctx)

		// The check should complete even if individual checkers timeout
		assert.Contains(t, health.Checks, "slow-checker")

		// The slow checker should have been cancelled
		result := health.Checks["slow-checker"]
		assert.Equal(t, StatusUnknown, result.Status)
		assert.Contains(t, result.Error, "context")
	})
}

func TestHealthService_ConcurrentChecks(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	config := Config{
		Version:     "1.0.0",
		Environment: "test",
	}

	service := NewService(config, logger)

	t.Run("Concurrent health checks", func(t *testing.T) {
		// Register multiple checkers
		for i := 0; i < 10; i++ {
			name := "checker-" + string(rune('0'+i))
			checker := NewMockChecker(name, StatusHealthy, false)
			checker.SetDelay(100 * time.Millisecond) // Small delay to test concurrency
			service.RegisterChecker(checker)
		}

		ctx := context.Background()
		start := time.Now()
		health := service.Check(ctx)
		duration := time.Since(start)

		// Should complete in roughly the time of the longest check (100ms)
		// plus some overhead, not the sum of all checks (1000ms)
		assert.Less(t, duration, 500*time.Millisecond)
		assert.Equal(t, 10, health.Summary.Total)
		assert.Equal(t, 10, health.Summary.Healthy)
	})
}

func BenchmarkHealthService_Check(b *testing.B) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	config := Config{
		Version:     "1.0.0",
		Environment: "test",
	}

	service := NewService(config, logger)

	// Register some checkers
	for i := 0; i < 5; i++ {
		name := "bench-checker-" + string(rune('0'+i))
		service.RegisterChecker(NewMockChecker(name, StatusHealthy, false))
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		service.Check(ctx)
	}
}

func BenchmarkHealthService_CheckSingle(b *testing.B) {
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel)

	config := Config{
		Version:     "1.0.0",
		Environment: "test",
	}

	service := NewService(config, logger)
	service.RegisterChecker(NewMockChecker("bench-single", StatusHealthy, false))

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		service.CheckSingle(ctx, "bench-single")
	}
}
