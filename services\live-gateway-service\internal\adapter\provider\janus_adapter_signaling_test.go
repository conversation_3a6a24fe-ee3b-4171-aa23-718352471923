package provider

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"cina.club/services/live-gateway-service/internal/application/port"
)

func TestJanusAdapterSignalingFlow(t *testing.T) {
	logger := logrus.New()
	adapter, err := NewJanusAdapter(nil, logger)
	require.NoError(t, err)

	// 测试完整的信令流程
	t.Run("Complete signaling flow", func(t *testing.T) {
		streamKey := "webrtc_stream_123"

		// 1. 处理会话创建事件
		sessionEvent := map[string]interface{}{
			"type":      "session_created",
			"stream":    streamKey,
			"protocol":  "webrtc",
			"session":   "sess_123",
			"timestamp": time.Now().Unix(),
		}
		sessionBody, _ := json.Marshal(sessionEvent)
		sessionReq := &port.WebhookParseRequest{
			Body:      sessionBody,
			ClientIP:  "***********",
			UserAgent: "Janus/1.0.0",
			Method:    http.MethodPost,
			Path:      "/webhook",
			Headers:   map[string]string{},
			Timestamp: time.Now(),
		}
		sessionResult, err := adapter.ParseWebhookRequest(sessionReq)
		require.NoError(t, err)
		assert.Equal(t, streamKey, sessionResult.StreamKey)

		// 2. 处理SDP提议事件
		offerEvent := map[string]interface{}{
			"type":      "sdp_offer",
			"stream":    streamKey,
			"protocol":  "webrtc",
			"session":   "sess_123",
			"sdp":       "v=0\no=- 123 456 IN IP4 ***********\ns=-\nt=0 0\n",
			"timestamp": time.Now().Unix(),
		}
		offerBody, _ := json.Marshal(offerEvent)
		offerReq := &port.WebhookParseRequest{
			Body:      offerBody,
			ClientIP:  "***********",
			UserAgent: "Janus/1.0.0",
			Method:    http.MethodPost,
			Path:      "/webhook",
			Headers:   map[string]string{},
			Timestamp: time.Now(),
		}
		offerResult, err := adapter.ParseWebhookRequest(offerReq)
		require.NoError(t, err)
		assert.Equal(t, streamKey, offerResult.StreamKey)

		// 3. 处理ICE收集完成事件
		iceEvent := map[string]interface{}{
			"type":      "ice_gathering_done",
			"stream":    streamKey,
			"protocol":  "webrtc",
			"session":   "sess_123",
			"timestamp": time.Now().Unix(),
		}
		iceBody, _ := json.Marshal(iceEvent)
		iceReq := &port.WebhookParseRequest{
			Body:      iceBody,
			ClientIP:  "***********",
			UserAgent: "Janus/1.0.0",
			Method:    http.MethodPost,
			Path:      "/webhook",
			Headers:   map[string]string{},
			Timestamp: time.Now(),
		}
		iceResult, err := adapter.ParseWebhookRequest(iceReq)
		require.NoError(t, err)
		assert.Equal(t, streamKey, iceResult.StreamKey)

		// 4. 处理连接建立事件
		connectedEvent := map[string]interface{}{
			"type":      "webrtc_up",
			"stream":    streamKey,
			"protocol":  "webrtc",
			"session":   "sess_123",
			"timestamp": time.Now().Unix(),
		}
		connectedBody, _ := json.Marshal(connectedEvent)
		connectedReq := &port.WebhookParseRequest{
			Body:      connectedBody,
			ClientIP:  "***********",
			UserAgent: "Janus/1.0.0",
			Method:    http.MethodPost,
			Path:      "/webhook",
			Headers:   map[string]string{},
			Timestamp: time.Now(),
		}
		connectedResult, err := adapter.ParseWebhookRequest(connectedReq)
		require.NoError(t, err)
		assert.Equal(t, streamKey, connectedResult.StreamKey)

		// 5. 处理媒体统计事件
		statsEvent := map[string]interface{}{
			"type":     "media_stats",
			"stream":   streamKey,
			"protocol": "webrtc",
			"session":  "sess_123",
			"stats": map[string]interface{}{
				"video_packets_sent":  1000,
				"video_packets_lost":  10,
				"video_nack_received": 5,
				"video_pli_received":  2,
				"video_fir_received":  1,
				"audio_packets_sent":  2000,
				"audio_packets_lost":  5,
				"audio_nack_received": 2,
				"connection_quality":  0.95,
				"estimated_bitrate":   2000000,
			},
			"timestamp": time.Now().Unix(),
		}
		statsBody, _ := json.Marshal(statsEvent)
		statsReq := &port.WebhookParseRequest{
			Body:      statsBody,
			ClientIP:  "***********",
			UserAgent: "Janus/1.0.0",
			Method:    http.MethodPost,
			Path:      "/webhook",
			Headers:   map[string]string{},
			Timestamp: time.Now(),
		}
		statsResult, err := adapter.ParseWebhookRequest(statsReq)
		require.NoError(t, err)
		assert.Equal(t, streamKey, statsResult.StreamKey)

		// 6. 处理连接关闭事件
		hangupEvent := map[string]interface{}{
			"type":      "hangup",
			"stream":    streamKey,
			"protocol":  "webrtc",
			"session":   "sess_123",
			"reason":    "client-closed",
			"timestamp": time.Now().Unix(),
		}
		hangupBody, _ := json.Marshal(hangupEvent)
		hangupReq := &port.WebhookParseRequest{
			Body:      hangupBody,
			ClientIP:  "***********",
			UserAgent: "Janus/1.0.0",
			Method:    http.MethodPost,
			Path:      "/webhook",
			Headers:   map[string]string{},
			Timestamp: time.Now(),
		}
		hangupResult, err := adapter.ParseWebhookRequest(hangupReq)
		require.NoError(t, err)
		assert.Equal(t, streamKey, hangupResult.StreamKey)
	})
}

func TestJanusAdapterSignalingErrors(t *testing.T) {
	logger := logrus.New()
	adapter, err := NewJanusAdapter(nil, logger)
	require.NoError(t, err)

	tests := []struct {
		name        string
		setup       func() *port.WebhookParseRequest
		validate    func(*port.WebhookRequest)
		expectError bool
	}{
		{
			name: "Invalid session ID",
			setup: func() *port.WebhookParseRequest {
				data := map[string]interface{}{
					"type":      "session_created",
					"stream":    "webrtc_stream_123",
					"protocol":  "webrtc",
					"session":   "", // 空会话ID
					"timestamp": time.Now().Unix(),
				}
				body, _ := json.Marshal(data)
				return &port.WebhookParseRequest{
					Body:      body,
					ClientIP:  "***********",
					UserAgent: "Janus/1.0.0",
					Method:    http.MethodPost,
					Path:      "/webhook",
					Headers:   map[string]string{},
					Timestamp: time.Now(),
				}
			},
			validate: func(req *port.WebhookRequest) {
				rawData := req.RawData
				assert.Empty(t, rawData["session"])
			},
			expectError: false, // 允许空会话ID，由上层处理
		},
		{
			name: "Invalid SDP format",
			setup: func() *port.WebhookParseRequest {
				data := map[string]interface{}{
					"type":      "sdp_offer",
					"stream":    "webrtc_stream_123",
					"protocol":  "webrtc",
					"session":   "sess_123",
					"sdp":       "invalid-sdp", // 无效的SDP格式
					"timestamp": time.Now().Unix(),
				}
				body, _ := json.Marshal(data)
				return &port.WebhookParseRequest{
					Body:      body,
					ClientIP:  "***********",
					UserAgent: "Janus/1.0.0",
					Method:    http.MethodPost,
					Path:      "/webhook",
					Headers:   map[string]string{},
					Timestamp: time.Now(),
				}
			},
			validate: func(req *port.WebhookRequest) {
				rawData := req.RawData
				assert.Equal(t, "invalid-sdp", rawData["sdp"])
			},
			expectError: false, // 允许无效的SDP格式，由上层处理
		},
		{
			name: "Invalid media stats",
			setup: func() *port.WebhookParseRequest {
				data := map[string]interface{}{
					"type":     "media_stats",
					"stream":   "webrtc_stream_123",
					"protocol": "webrtc",
					"session":  "sess_123",
					"stats": map[string]interface{}{
						"video_packets_sent": -1000,     // 无效的负�?
						"video_packets_lost": "invalid", // 无效的类�?
					},
					"timestamp": time.Now().Unix(),
				}
				body, _ := json.Marshal(data)
				return &port.WebhookParseRequest{
					Body:      body,
					ClientIP:  "***********",
					UserAgent: "Janus/1.0.0",
					Method:    http.MethodPost,
					Path:      "/webhook",
					Headers:   map[string]string{},
					Timestamp: time.Now(),
				}
			},
			validate: func(req *port.WebhookRequest) {
				rawData := req.RawData
				stats := rawData["stats"].(map[string]interface{})
				assert.Equal(t, float64(-1000), stats["video_packets_sent"])
				assert.Equal(t, "invalid", stats["video_packets_lost"])
			},
			expectError: false, // 允许无效的统计数据，由上层处�?
		},
		{
			name: "Missing timestamp",
			setup: func() *port.WebhookParseRequest {
				data := map[string]interface{}{
					"type":     "hangup",
					"stream":   "webrtc_stream_123",
					"protocol": "webrtc",
					"session":  "sess_123",
					"reason":   "client-closed",
					// 缺少timestamp字段
				}
				body, _ := json.Marshal(data)
				return &port.WebhookParseRequest{
					Body:      body,
					ClientIP:  "***********",
					UserAgent: "Janus/1.0.0",
					Method:    http.MethodPost,
					Path:      "/webhook",
					Headers:   map[string]string{},
					Timestamp: time.Now(),
				}
			},
			validate: func(req *port.WebhookRequest) {
				rawData := req.RawData
				_, exists := rawData["timestamp"]
				assert.False(t, exists)
			},
			expectError: false, // 允许缺少时间戳，使用请求时间
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := tt.setup()
			result, err := adapter.ParseWebhookRequest(req)
			if tt.expectError {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)
			tt.validate(result)
		})
	}
}

func TestJanusAdapterSignalingTimeout(t *testing.T) {
	logger := logrus.New()
	adapter, err := NewJanusAdapter(nil, logger)
	require.NoError(t, err)

	ctx := context.Background()
	streamKey := "webrtc_stream_123"

	// 测试信令超时
	t.Run("Signaling timeout", func(t *testing.T) {
		// 1. 创建会话
		sessionEvent := map[string]interface{}{
			"type":      "session_created",
			"stream":    streamKey,
			"protocol":  "webrtc",
			"session":   "sess_123",
			"timestamp": time.Now().Unix(),
		}
		sessionBody, _ := json.Marshal(sessionEvent)
		sessionReq := &port.WebhookParseRequest{
			Body:      sessionBody,
			ClientIP:  "***********",
			UserAgent: "Janus/1.0.0",
			Method:    http.MethodPost,
			Path:      "/webhook",
			Headers:   map[string]string{},
			Timestamp: time.Now(),
		}
		_, err := adapter.ParseWebhookRequest(sessionReq)
		require.NoError(t, err)

		// 2. 模拟超时
		time.Sleep(time.Second) // 实际应该使用更长的超时时�?

		// 3. 检查会话状�?
		info, err := adapter.GetStreamInfo(ctx, streamKey)
		assert.Error(t, err) // 当前未实�?
		assert.Equal(t, ErrJanusNotImplemented, err)
		assert.Nil(t, info)
	})
}

func TestJanusAdapterSignalingReconnect(t *testing.T) {
	logger := logrus.New()
	adapter, err := NewJanusAdapter(nil, logger)
	require.NoError(t, err)

	// 测试信令重连
	t.Run("Signaling reconnect", func(t *testing.T) {
		ctx := context.Background()
		streamKey := "webrtc_stream_123"

		// 1. 初始连接
		sessionEvent := map[string]interface{}{
			"type":      "session_created",
			"stream":    streamKey,
			"protocol":  "webrtc",
			"session":   "sess_123",
			"timestamp": time.Now().Unix(),
		}
		sessionBody, _ := json.Marshal(sessionEvent)
		sessionReq := &port.WebhookParseRequest{
			Body:      sessionBody,
			ClientIP:  "***********",
			UserAgent: "Janus/1.0.0",
			Method:    http.MethodPost,
			Path:      "/webhook",
			Headers:   map[string]string{},
			Timestamp: time.Now(),
		}
		_, err := adapter.ParseWebhookRequest(sessionReq)
		require.NoError(t, err)

		// 2. 模拟断开连接
		disconnectEvent := map[string]interface{}{
			"type":      "transport_closed",
			"stream":    streamKey,
			"protocol":  "webrtc",
			"session":   "sess_123",
			"timestamp": time.Now().Unix(),
		}
		disconnectBody, _ := json.Marshal(disconnectEvent)
		disconnectReq := &port.WebhookParseRequest{
			Body:      disconnectBody,
			ClientIP:  "***********",
			UserAgent: "Janus/1.0.0",
			Method:    http.MethodPost,
			Path:      "/webhook",
			Headers:   map[string]string{},
			Timestamp: time.Now(),
		}
		_, err = adapter.ParseWebhookRequest(disconnectReq)
		require.NoError(t, err)

		// 3. 模拟重连
		reconnectEvent := map[string]interface{}{
			"type":      "session_created",
			"stream":    streamKey,
			"protocol":  "webrtc",
			"session":   "sess_124", // 新的会话ID
			"timestamp": time.Now().Unix(),
		}
		reconnectBody, _ := json.Marshal(reconnectEvent)
		reconnectReq := &port.WebhookParseRequest{
			Body:      reconnectBody,
			ClientIP:  "***********",
			UserAgent: "Janus/1.0.0",
			Method:    http.MethodPost,
			Path:      "/webhook",
			Headers:   map[string]string{},
			Timestamp: time.Now(),
		}
		_, err = adapter.ParseWebhookRequest(reconnectReq)
		require.NoError(t, err)

		// 4. 检查重连后的状�?
		info, err := adapter.GetStreamInfo(ctx, streamKey)
		assert.Error(t, err) // 当前未实�?
		assert.Equal(t, ErrJanusNotImplemented, err)
		assert.Nil(t, info)
	})
}
