package provider

import (
	"context"
	"encoding/json"
	"net/http"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"cina.club/services/live-gateway-service/internal/application/port"
	"cina.club/services/live-gateway-service/internal/domain/model"
)

func TestJanusAdapterWebRTCPublish(t *testing.T) {
	logger := logrus.New()
	adapter, err := NewJanusAdapter(nil, logger)
	require.NoError(t, err)

	tests := []struct {
		name        string
		setup       func() *port.WebhookParseRequest
		validate    func(*port.WebhookRequest)
		expectError bool
	}{
		{
			name: "WebRTC publish event",
			setup: func() *port.WebhookParseRequest {
				data := map[string]interface{}{
					"type":        "published",
					"stream":      "webrtc_stream_123",
					"protocol":    "webrtc",
					"sdp_type":    "offer",
					"ice_ufrag":   "abc123",
					"ice_pwd":     "xyz789",
					"fingerprint": "sha-256 AA:BB:CC:DD:EE:FF",
					"candidates": []map[string]interface{}{
						{
							"foundation": "1",
							"component":  1,
							"protocol":   "udp",
							"priority":   2113937151,
							"ip":         "***********",
							"port":       50000,
							"type":       "host",
						},
					},
				}
				body, _ := json.Marshal(data)
				return &port.WebhookParseRequest{
					Body:      body,
					ClientIP:  "***********",
					UserAgent: "Janus/1.0.0",
					Method:    http.MethodPost,
					Path:      "/webhook",
					Headers:   map[string]string{},
					Timestamp: time.Now(),
				}
			},
			validate: func(req *port.WebhookRequest) {
				assert.Equal(t, model.WebhookEventTypePublish, req.EventType)
				assert.Equal(t, "webrtc_stream_123", req.StreamKey)

				// 验证WebRTC特定字段
				rawData := req.RawData
				assert.Equal(t, "webrtc", rawData["protocol"])
				assert.Equal(t, "offer", rawData["sdp_type"])
				assert.Equal(t, "abc123", rawData["ice_ufrag"])
				assert.Equal(t, "xyz789", rawData["ice_pwd"])
				assert.Equal(t, "sha-256 AA:BB:CC:DD:EE:FF", rawData["fingerprint"])

				candidates, ok := rawData["candidates"].([]map[string]interface{})
				assert.True(t, ok)
				assert.Len(t, candidates, 1)
				assert.Equal(t, "host", candidates[0]["type"])
			},
			expectError: false,
		},
		{
			name: "Invalid WebRTC SDP",
			setup: func() *port.WebhookParseRequest {
				data := map[string]interface{}{
					"type":     "published",
					"stream":   "webrtc_stream_123",
					"protocol": "webrtc",
					"sdp_type": "invalid",
				}
				body, _ := json.Marshal(data)
				return &port.WebhookParseRequest{
					Body:      body,
					ClientIP:  "***********",
					UserAgent: "Janus/1.0.0",
					Method:    http.MethodPost,
					Path:      "/webhook",
					Headers:   map[string]string{},
					Timestamp: time.Now(),
				}
			},
			validate: func(req *port.WebhookRequest) {
				assert.Equal(t, model.WebhookEventTypePublish, req.EventType)
				assert.Equal(t, "webrtc_stream_123", req.StreamKey)

				// 验证无效的SDP类型
				rawData := req.RawData
				assert.Equal(t, "invalid", rawData["sdp_type"])
			},
			expectError: false, // Janus适配器应该允许无效的SDP，由上层处理
		},
		{
			name: "Missing ICE candidates",
			setup: func() *port.WebhookParseRequest {
				data := map[string]interface{}{
					"type":      "published",
					"stream":    "webrtc_stream_123",
					"protocol":  "webrtc",
					"sdp_type":  "offer",
					"ice_ufrag": "abc123",
					"ice_pwd":   "xyz789",
				}
				body, _ := json.Marshal(data)
				return &port.WebhookParseRequest{
					Body:      body,
					ClientIP:  "***********",
					UserAgent: "Janus/1.0.0",
					Method:    http.MethodPost,
					Path:      "/webhook",
					Headers:   map[string]string{},
					Timestamp: time.Now(),
				}
			},
			validate: func(req *port.WebhookRequest) {
				assert.Equal(t, model.WebhookEventTypePublish, req.EventType)
				assert.Equal(t, "webrtc_stream_123", req.StreamKey)

				// 验证缺少ICE候选者
				rawData := req.RawData
				_, exists := rawData["candidates"]
				assert.False(t, exists)
			},
			expectError: false, // 允许缺少ICE候选者，可能在后续事件中收到
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := tt.setup()
			result, err := adapter.ParseWebhookRequest(req)
			if tt.expectError {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)
			tt.validate(result)
		})
	}
}

func TestJanusAdapterWebRTCPlay(t *testing.T) {
	logger := logrus.New()
	adapter, err := NewJanusAdapter(nil, logger)
	require.NoError(t, err)

	ctx := context.Background()
	streamKey := "webrtc_stream_123"

	// 测试生成WebRTC播放URL
	t.Run("Generate WebRTC play URLs", func(t *testing.T) {
		req := &port.RequestPlayURLsRequest{
			StreamKey: streamKey,
			Protocol:  model.PlayProtocolWebRTC,
			TTL:       time.Hour,
			ClientIP:  "***********",
			UserAgent: "Chrome/90.0.0.0",
			Secure:    true,
		}

		urls, err := adapter.GeneratePlayURLs(ctx, req)
		assert.Error(t, err) // 当前未实�?
		assert.Equal(t, ErrJanusNotImplemented, err)
		assert.Nil(t, urls)
	})

	// 测试获取WebRTC流信�?
	t.Run("Get WebRTC stream info", func(t *testing.T) {
		info, err := adapter.GetStreamInfo(ctx, streamKey)
		assert.Error(t, err) // 当前未实�?
		assert.Equal(t, ErrJanusNotImplemented, err)
		assert.Nil(t, info)
	})
}

func TestJanusAdapterWebRTCSignaling(t *testing.T) {
	logger := logrus.New()
	adapter, err := NewJanusAdapter(nil, logger)
	require.NoError(t, err)

	tests := []struct {
		name        string
		setup       func() *port.WebhookParseRequest
		validate    func(*port.WebhookRequest)
		expectError bool
	}{
		{
			name: "ICE candidate event",
			setup: func() *port.WebhookParseRequest {
				data := map[string]interface{}{
					"type":     "ice_candidate",
					"stream":   "webrtc_stream_123",
					"protocol": "webrtc",
					"candidate": map[string]interface{}{
						"foundation": "2",
						"component":  1,
						"protocol":   "udp",
						"priority":   2113937151,
						"ip":         "***********",
						"port":       50001,
						"type":       "srflx",
						"raddr":      "********",
						"rport":      49999,
					},
				}
				body, _ := json.Marshal(data)
				return &port.WebhookParseRequest{
					Body:      body,
					ClientIP:  "***********",
					UserAgent: "Janus/1.0.0",
					Method:    http.MethodPost,
					Path:      "/webhook",
					Headers:   map[string]string{},
					Timestamp: time.Now(),
				}
			},
			validate: func(req *port.WebhookRequest) {
				rawData := req.RawData
				candidate, ok := rawData["candidate"].(map[string]interface{})
				assert.True(t, ok)
				assert.Equal(t, "srflx", candidate["type"])
				assert.Equal(t, "********", candidate["raddr"])
			},
			expectError: false,
		},
		{
			name: "ICE state change event",
			setup: func() *port.WebhookParseRequest {
				data := map[string]interface{}{
					"type":      "ice_state",
					"stream":    "webrtc_stream_123",
					"protocol":  "webrtc",
					"state":     "connected",
					"timestamp": time.Now().Unix(),
				}
				body, _ := json.Marshal(data)
				return &port.WebhookParseRequest{
					Body:      body,
					ClientIP:  "***********",
					UserAgent: "Janus/1.0.0",
					Method:    http.MethodPost,
					Path:      "/webhook",
					Headers:   map[string]string{},
					Timestamp: time.Now(),
				}
			},
			validate: func(req *port.WebhookRequest) {
				rawData := req.RawData
				assert.Equal(t, "connected", rawData["state"])
			},
			expectError: false,
		},
		{
			name: "DTLS state change event",
			setup: func() *port.WebhookParseRequest {
				data := map[string]interface{}{
					"type":      "dtls_state",
					"stream":    "webrtc_stream_123",
					"protocol":  "webrtc",
					"state":     "connected",
					"timestamp": time.Now().Unix(),
				}
				body, _ := json.Marshal(data)
				return &port.WebhookParseRequest{
					Body:      body,
					ClientIP:  "***********",
					UserAgent: "Janus/1.0.0",
					Method:    http.MethodPost,
					Path:      "/webhook",
					Headers:   map[string]string{},
					Timestamp: time.Now(),
				}
			},
			validate: func(req *port.WebhookRequest) {
				rawData := req.RawData
				assert.Equal(t, "connected", rawData["state"])
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			req := tt.setup()
			result, err := adapter.ParseWebhookRequest(req)
			if tt.expectError {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)
			tt.validate(result)
		})
	}
}
