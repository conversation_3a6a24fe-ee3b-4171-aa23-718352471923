package performance

import (
	"context"
	"fmt"
	"math/rand"
	"sort"
	"sync"
	"sync/atomic"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"

	"cina.club/services/live-gateway-service/internal/application/port"
	"cina.club/services/live-gateway-service/internal/application/service"
	"cina.club/services/live-gateway-service/internal/domain/model"
)

type TestResult struct {
	RPS           float64
	ErrorRate     float64
	P50Latency    time.Duration
	P90Latency    time.Duration
	P99Latency    time.Duration
	MaxLatency    time.Duration
	MinLatency    time.Duration
	AvgLatency    time.Duration
	Concurrency   int
	TotalRequests int64
	TotalErrors   int64
}

func TestServiceScalability(t *testing.T) {
	ctx := context.Background()

	// 测试不同的并发度和 RPS
	for concurrency := minConcurrency; concurrency <= maxConcurrency; concurrency += concurrencyStep {
		for rps := baseRPS; rps <= maxRPS; rps += rpsIncrement {
			t.Run(fmt.Sprintf("Concurrency=%d_RPS=%d", concurrency, rps), func(t *testing.T) {
				// 使用不同的配置创建服务
				config := &service.GatewayConfig{
					DefaultTTL:           time.Hour,
					MaxConcurrentStreams: concurrency * 100,
					LoadBalanceStrategy:  "round_robin",
					EnableMetrics:        true,
					WebhookTimeout:       time.Second * 5,
					AuthTimeout:          time.Second * 3,
				}

				svc := setupTestServiceWithConfig(t, config)

				// 预创建一些流
				streamKeys := make([]string, concurrency*streamMultiplier)
				for i := range streamKeys {
					streamKeys[i] = setupTestStream(t, svc)
				}

				// 运行扩展性测试
				result := runScalabilityTest(t, ctx, svc, streamKeys, concurrency, rps)

				// 记录测试结果
				t.Logf("Test Results for Concurrency=%d, Target RPS=%d:", concurrency, rps)
				t.Logf("Actual RPS: %.2f", result.RPS)
				t.Logf("Error Rate: %.2f%%", result.ErrorRate)
				t.Logf("Latency (P50/P90/P99): %v/%v/%v", result.P50Latency, result.P90Latency, result.P99Latency)
				t.Logf("Latency (Min/Avg/Max): %v/%v/%v", result.MinLatency, result.AvgLatency, result.MaxLatency)
				t.Logf("Total Requests: %d", result.TotalRequests)
				t.Logf("Total Errors: %d", result.TotalErrors)

				// 验证性能指标
				require.True(t, result.RPS >= float64(rps)*0.8, "actual RPS %.2f is less than 80%% of target RPS %d", result.RPS, rps)
				require.True(t, result.ErrorRate < 5.0, "error rate %.2f%% exceeds 5%%", result.ErrorRate)
				require.True(t, result.P99Latency < time.Second, "P99 latency %v exceeds 1s", result.P99Latency)
			})
		}
	}
}

func runScalabilityTest(t *testing.T, ctx context.Context, svc port.GatewayService, streamKeys []string, concurrency int, targetRPS int) *TestResult {
	var (
		wg           sync.WaitGroup
		requestCount int64
		errorCount   int64
		latencies    []time.Duration
		latencyMu    sync.Mutex
	)

	// 预热
	time.Sleep(warmupDuration)

	// 记录开始时间
	start := time.Now()

	// 启动工作 goroutine
	for i := 0; i < concurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			for time.Since(start) < testDuration {
				// 随机选择一个操作
				op := rand.Intn(5)
				var err error
				var latency time.Duration

				startOp := time.Now()

				switch op {
				case 0:
					// RequestPushURL
					req := &port.CreateStreamRequest{
						RoomID:    uuid.New(),
						UserID:    uuid.New(),
						Protocol:  model.StreamProtocolRTMP,
						Quality:   model.StreamQualityHigh,
						ClientIP:  "127.0.0.1",
						UserAgent: "test-agent",
					}
					_, err = svc.RequestPushURL(ctx, req)

				case 1:
					// RequestPlayURLs
					req := &port.RequestPlayURLsRequest{
						StreamKey: streamKeys[rand.Intn(len(streamKeys))],
						Protocols: []model.PlayProtocol{
							model.PlayProtocolHLS,
							model.PlayProtocolFLV,
						},
						Quality:   model.StreamQualityHigh,
						EnableCDN: true,
						Region:    "test-region",
						ClientIP:  "127.0.0.1",
						UserAgent: "test-agent",
					}
					_, err = svc.RequestPlayURLs(ctx, req)

				case 2:
					// HandleWebhook
					req := &port.WebhookRequest{
						EventType:  model.WebhookEventTypePublish,
						StreamKey:  streamKeys[rand.Intn(len(streamKeys))],
						AuthToken:  "test-token",
						ClientIP:   "127.0.0.1",
						UserAgent:  "test-agent",
						ServerNode: "test-node",
						Protocol:   model.StreamProtocolRTMP,
						Timestamp:  time.Now(),
					}
					_, err = svc.HandleWebhook(ctx, req)

				case 3:
					// GetStreamInfo
					_, err = svc.GetStreamInfo(ctx, streamKeys[rand.Intn(len(streamKeys))])

				case 4:
					// GetServerStats
					_, err = svc.GetServerStats(ctx)
				}

				latency = time.Since(startOp)

				// 记录延迟
				latencyMu.Lock()
				latencies = append(latencies, latency)
				latencyMu.Unlock()

				if err != nil {
					atomic.AddInt64(&errorCount, 1)
				}
				atomic.AddInt64(&requestCount, 1)

				// 控制发送速率
				time.Sleep(time.Second / time.Duration(targetRPS/concurrency))
			}
		}()
	}

	// 等待所有请求完成
	wg.Wait()

	// 冷却
	time.Sleep(cooldownDuration)

	// 计算统计信息
	duration := time.Since(start)
	actualRPS := float64(requestCount) / duration.Seconds()
	errorRate := float64(errorCount) / float64(requestCount) * 100

	// 计算延迟统计
	sort.Slice(latencies, func(i, j int) bool {
		return latencies[i] < latencies[j]
	})

	result := &TestResult{
		RPS:           actualRPS,
		ErrorRate:     errorRate,
		P50Latency:    latencies[len(latencies)*50/100],
		P90Latency:    latencies[len(latencies)*90/100],
		P99Latency:    latencies[len(latencies)*99/100],
		MaxLatency:    latencies[len(latencies)-1],
		MinLatency:    latencies[0],
		AvgLatency:    calculateAvgLatency(latencies),
		Concurrency:   concurrency,
		TotalRequests: requestCount,
		TotalErrors:   errorCount,
	}

	return result
}

func calculateAvgLatency(latencies []time.Duration) time.Duration {
	var sum time.Duration
	for _, latency := range latencies {
		sum += latency
	}
	return sum / time.Duration(len(latencies))
}
