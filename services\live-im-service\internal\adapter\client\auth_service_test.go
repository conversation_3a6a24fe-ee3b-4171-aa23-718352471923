/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:12:13
Modified: 2025-07-11 10:12:13
*/

package client

import (
	"context"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"cina.club/services/live-im-service/internal/domain/model"
)

func TestNewAuthService(t *testing.T) {
	userCoreClient := &struct{}{} // Mock client
	jwtSecret := "test-secret"
	logger := logrus.New()

	authService := NewAuthService(userCoreClient, jwtSecret, logger)

	assert.NotNil(t, authService)
}

func TestAuthService_ValidateToken(t *testing.T) {
	userCoreClient := &struct{}{} // Mock client
	jwtSecret := "test-secret-key-for-testing-purposes-only"
	logger := logrus.New()
	authService := NewAuthService(userCoreClient, jwtSecret, logger).(*AuthService)

	ctx := context.Background()

	t.Run("Valid token", func(t *testing.T) {
		// Create a valid JWT token
		userID := uuid.New()
		claims := &Claims{
			UserID:   userID.String(),
			Username: "testuser",
			Avatar:   "avatar.jpg",
			Role:     "viewer",
			Level:    5,
			VIPLevel: 2,
			RegisteredClaims: jwt.RegisteredClaims{
				ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
				IssuedAt:  jwt.NewNumericDate(time.Now()),
				NotBefore: jwt.NewNumericDate(time.Now()),
			},
		}

		token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
		tokenString, err := token.SignedString([]byte(jwtSecret))
		require.NoError(t, err)

		// Validate the token
		userInfo, err := authService.ValidateToken(ctx, tokenString)

		assert.NoError(t, err)
		assert.NotNil(t, userInfo)
		assert.Equal(t, userID, userInfo.UserID)
		assert.Equal(t, "testuser", userInfo.Username)
		assert.Equal(t, "avatar.jpg", userInfo.Avatar)
		assert.Equal(t, model.RoleViewer, userInfo.Role)
		assert.Equal(t, 5, userInfo.Level)
		assert.Equal(t, 2, userInfo.VIPLevel)
	})

	t.Run("Invalid token format", func(t *testing.T) {
		invalidToken := "invalid.token.format"

		userInfo, err := authService.ValidateToken(ctx, invalidToken)

		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Contains(t, err.Error(), "failed to parse token")
	})

	t.Run("Token with wrong signature", func(t *testing.T) {
		// Create a token with wrong secret
		wrongSecret := "wrong-secret"
		userID := uuid.New()
		claims := &Claims{
			UserID:   userID.String(),
			Username: "testuser",
			Avatar:   "avatar.jpg",
			Role:     "viewer",
			Level:    5,
			VIPLevel: 2,
			RegisteredClaims: jwt.RegisteredClaims{
				ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
				IssuedAt:  jwt.NewNumericDate(time.Now()),
				NotBefore: jwt.NewNumericDate(time.Now()),
			},
		}

		token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
		tokenString, err := token.SignedString([]byte(wrongSecret))
		require.NoError(t, err)

		// Try to validate with correct secret
		userInfo, err := authService.ValidateToken(ctx, tokenString)

		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Contains(t, err.Error(), "signature is invalid")
	})

	t.Run("Expired token", func(t *testing.T) {
		// Create an expired token
		userID := uuid.New()
		claims := &Claims{
			UserID:   userID.String(),
			Username: "testuser",
			Avatar:   "avatar.jpg",
			Role:     "viewer",
			Level:    5,
			VIPLevel: 2,
			RegisteredClaims: jwt.RegisteredClaims{
				ExpiresAt: jwt.NewNumericDate(time.Now().Add(-time.Hour)), // Expired 1 hour ago
				IssuedAt:  jwt.NewNumericDate(time.Now().Add(-2 * time.Hour)),
				NotBefore: jwt.NewNumericDate(time.Now().Add(-2 * time.Hour)),
			},
		}

		token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
		tokenString, err := token.SignedString([]byte(jwtSecret))
		require.NoError(t, err)

		// Validate the expired token
		userInfo, err := authService.ValidateToken(ctx, tokenString)

		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Contains(t, err.Error(), "token is expired")
	})

	t.Run("Token with invalid user ID", func(t *testing.T) {
		// Create a token with invalid UUID
		claims := &Claims{
			UserID:   "invalid-uuid",
			Username: "testuser",
			Avatar:   "avatar.jpg",
			Role:     "viewer",
			Level:    5,
			VIPLevel: 2,
			RegisteredClaims: jwt.RegisteredClaims{
				ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
				IssuedAt:  jwt.NewNumericDate(time.Now()),
				NotBefore: jwt.NewNumericDate(time.Now()),
			},
		}

		token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
		tokenString, err := token.SignedString([]byte(jwtSecret))
		require.NoError(t, err)

		// Validate the token
		userInfo, err := authService.ValidateToken(ctx, tokenString)

		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Contains(t, err.Error(), "invalid user ID")
	})

	t.Run("Token with different roles", func(t *testing.T) {
		testCases := []struct {
			roleString   string
			expectedRole model.UserRole
		}{
			{"viewer", model.RoleViewer},
			{"streamer", model.RoleStreamer},
			{"moderator", model.RoleModerator},
			{"admin", model.RoleAdmin},
			{"vip", model.RoleVIP},
			{"unknown", model.RoleViewer}, // Default to viewer for unknown roles
		}

		for _, tc := range testCases {
			t.Run("Role: "+tc.roleString, func(t *testing.T) {
				userID := uuid.New()
				claims := &Claims{
					UserID:   userID.String(),
					Username: "testuser",
					Avatar:   "avatar.jpg",
					Role:     tc.roleString,
					Level:    5,
					VIPLevel: 2,
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
						IssuedAt:  jwt.NewNumericDate(time.Now()),
						NotBefore: jwt.NewNumericDate(time.Now()),
					},
				}

				token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				tokenString, err := token.SignedString([]byte(jwtSecret))
				require.NoError(t, err)

				userInfo, err := authService.ValidateToken(ctx, tokenString)

				assert.NoError(t, err)
				assert.NotNil(t, userInfo)
				assert.Equal(t, tc.expectedRole, userInfo.Role)
			})
		}
	})
}

func TestAuthService_CheckRoomPermission(t *testing.T) {
	userCoreClient := &struct{}{} // Mock client
	jwtSecret := "test-secret"
	logger := logrus.New()
	authService := NewAuthService(userCoreClient, jwtSecret, logger).(*AuthService)

	ctx := context.Background()
	userID := uuid.New()
	roomID := "test-room"

	t.Run("Permission check - always returns true for now", func(t *testing.T) {
		// Current implementation always returns true
		hasPermission, err := authService.CheckRoomPermission(ctx, userID, roomID)

		assert.NoError(t, err)
		assert.True(t, hasPermission)
	})
}

func TestAuthService_RoleMapping(t *testing.T) {
	// Test that different role strings are handled correctly in token validation
	userCoreClient := &struct{}{} // Mock client
	jwtSecret := "test-secret-key-for-testing-purposes-only"
	logger := logrus.New()
	authService := NewAuthService(userCoreClient, jwtSecret, logger)

	ctx := context.Background()

	// Test that the auth service can handle role mapping correctly
	// This is tested indirectly through the ValidateToken method
	userID := uuid.New()
	claims := &Claims{
		UserID:   userID.String(),
		Username: "testuser",
		Avatar:   "avatar.jpg",
		Role:     "admin",
		Level:    5,
		VIPLevel: 2,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(jwtSecret))
	require.NoError(t, err)

	userInfo, err := authService.ValidateToken(ctx, tokenString)

	assert.NoError(t, err)
	assert.NotNil(t, userInfo)
	assert.Equal(t, model.RoleAdmin, userInfo.Role)
}
