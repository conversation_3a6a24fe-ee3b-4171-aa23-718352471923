/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:12:13
Modified: 2025-07-11 10:12:13
*/

package client

import (
	"context"
	"fmt"
	"testing"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"cina.club/services/live-im-service/internal/domain/model"
)

func TestNewAuthService(t *testing.T) {
	userCoreClient := &struct{}{} // Mock client
	jwtSecret := "test-secret"
	logger := logrus.New()

	authService := NewAuthService(userCoreClient, jwtSecret, logger)

	assert.NotNil(t, authService)
}

func TestAuthService_ValidateToken(t *testing.T) {
	userCoreClient := &struct{}{} // Mock client
	jwtSecret := "test-secret-key-for-testing-purposes-only"
	logger := logrus.New()
	authService := NewAuthService(userCoreClient, jwtSecret, logger).(*AuthService)

	ctx := context.Background()

	t.Run("Valid token", func(t *testing.T) {
		// Create a valid JWT token
		userID := uuid.New()
		claims := &Claims{
			UserID:   userID.String(),
			Username: "testuser",
			Avatar:   "avatar.jpg",
			Role:     "viewer",
			Level:    5,
			VIPLevel: 2,
			RegisteredClaims: jwt.RegisteredClaims{
				ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
				IssuedAt:  jwt.NewNumericDate(time.Now()),
				NotBefore: jwt.NewNumericDate(time.Now()),
			},
		}

		token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
		tokenString, err := token.SignedString([]byte(jwtSecret))
		require.NoError(t, err)

		// Validate the token
		userInfo, err := authService.ValidateToken(ctx, tokenString)

		assert.NoError(t, err)
		assert.NotNil(t, userInfo)
		assert.Equal(t, userID, userInfo.UserID)
		assert.Equal(t, "testuser", userInfo.Username)
		assert.Equal(t, "avatar.jpg", userInfo.Avatar)
		assert.Equal(t, model.RoleViewer, userInfo.Role)
		assert.Equal(t, 5, userInfo.Level)
		assert.Equal(t, 2, userInfo.VIPLevel)
	})

	t.Run("Invalid token format", func(t *testing.T) {
		invalidToken := "invalid.token.format"

		userInfo, err := authService.ValidateToken(ctx, invalidToken)

		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Contains(t, err.Error(), "failed to parse token")
	})

	t.Run("Token with wrong signature", func(t *testing.T) {
		// Create a token with wrong secret
		wrongSecret := "wrong-secret"
		userID := uuid.New()
		claims := &Claims{
			UserID:   userID.String(),
			Username: "testuser",
			Avatar:   "avatar.jpg",
			Role:     "viewer",
			Level:    5,
			VIPLevel: 2,
			RegisteredClaims: jwt.RegisteredClaims{
				ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
				IssuedAt:  jwt.NewNumericDate(time.Now()),
				NotBefore: jwt.NewNumericDate(time.Now()),
			},
		}

		token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
		tokenString, err := token.SignedString([]byte(wrongSecret))
		require.NoError(t, err)

		// Try to validate with correct secret
		userInfo, err := authService.ValidateToken(ctx, tokenString)

		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Contains(t, err.Error(), "signature is invalid")
	})

	t.Run("Expired token", func(t *testing.T) {
		// Create an expired token
		userID := uuid.New()
		claims := &Claims{
			UserID:   userID.String(),
			Username: "testuser",
			Avatar:   "avatar.jpg",
			Role:     "viewer",
			Level:    5,
			VIPLevel: 2,
			RegisteredClaims: jwt.RegisteredClaims{
				ExpiresAt: jwt.NewNumericDate(time.Now().Add(-time.Hour)), // Expired 1 hour ago
				IssuedAt:  jwt.NewNumericDate(time.Now().Add(-2 * time.Hour)),
				NotBefore: jwt.NewNumericDate(time.Now().Add(-2 * time.Hour)),
			},
		}

		token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
		tokenString, err := token.SignedString([]byte(jwtSecret))
		require.NoError(t, err)

		// Validate the expired token
		userInfo, err := authService.ValidateToken(ctx, tokenString)

		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Contains(t, err.Error(), "token is expired")
	})

	t.Run("Token with invalid user ID", func(t *testing.T) {
		// Create a token with invalid UUID
		claims := &Claims{
			UserID:   "invalid-uuid",
			Username: "testuser",
			Avatar:   "avatar.jpg",
			Role:     "viewer",
			Level:    5,
			VIPLevel: 2,
			RegisteredClaims: jwt.RegisteredClaims{
				ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
				IssuedAt:  jwt.NewNumericDate(time.Now()),
				NotBefore: jwt.NewNumericDate(time.Now()),
			},
		}

		token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
		tokenString, err := token.SignedString([]byte(jwtSecret))
		require.NoError(t, err)

		// Validate the token
		userInfo, err := authService.ValidateToken(ctx, tokenString)

		assert.Error(t, err)
		assert.Nil(t, userInfo)
		assert.Contains(t, err.Error(), "invalid user ID")
	})

	t.Run("Token with different roles", func(t *testing.T) {
		testCases := []struct {
			roleString   string
			expectedRole model.UserRole
		}{
			{"viewer", model.RoleViewer},
			{"streamer", model.RoleStreamer},
			{"moderator", model.RoleModerator},
			{"admin", model.RoleAdmin},
			{"vip", model.RoleVIP},
			{"unknown", model.RoleViewer}, // Default to viewer for unknown roles
		}

		for _, tc := range testCases {
			t.Run("Role: "+tc.roleString, func(t *testing.T) {
				userID := uuid.New()
				claims := &Claims{
					UserID:   userID.String(),
					Username: "testuser",
					Avatar:   "avatar.jpg",
					Role:     tc.roleString,
					Level:    5,
					VIPLevel: 2,
					RegisteredClaims: jwt.RegisteredClaims{
						ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
						IssuedAt:  jwt.NewNumericDate(time.Now()),
						NotBefore: jwt.NewNumericDate(time.Now()),
					},
				}

				token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
				tokenString, err := token.SignedString([]byte(jwtSecret))
				require.NoError(t, err)

				userInfo, err := authService.ValidateToken(ctx, tokenString)

				assert.NoError(t, err)
				assert.NotNil(t, userInfo)
				assert.Equal(t, tc.expectedRole, userInfo.Role)
			})
		}
	})
}

func TestAuthService_CheckRoomPermission(t *testing.T) {
	userCoreClient := &struct{}{} // Mock client
	jwtSecret := "test-secret"
	logger := logrus.New()
	authService := NewAuthService(userCoreClient, jwtSecret, logger).(*AuthService)

	ctx := context.Background()
	userID := uuid.New()
	roomID := "test-room"

	t.Run("Permission check - always returns true for now", func(t *testing.T) {
		// Current implementation always returns true
		hasPermission, err := authService.CheckRoomPermission(ctx, userID, roomID)

		assert.NoError(t, err)
		assert.True(t, hasPermission)
	})
}

func TestAuthService_RoleMapping(t *testing.T) {
	// Test that different role strings are handled correctly in token validation
	userCoreClient := &struct{}{} // Mock client
	jwtSecret := "test-secret-key-for-testing-purposes-only"
	logger := logrus.New()
	authService := NewAuthService(userCoreClient, jwtSecret, logger)

	ctx := context.Background()

	// Test that the auth service can handle role mapping correctly
	// This is tested indirectly through the ValidateToken method
	userID := uuid.New()
	claims := &Claims{
		UserID:   userID.String(),
		Username: "testuser",
		Avatar:   "avatar.jpg",
		Role:     "admin",
		Level:    5,
		VIPLevel: 2,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(jwtSecret))
	require.NoError(t, err)

	userInfo, err := authService.ValidateToken(ctx, tokenString)

	assert.NoError(t, err)
	assert.NotNil(t, userInfo)
	assert.Equal(t, model.RoleAdmin, userInfo.Role)
}

func TestAuthService_MalformedTokens(t *testing.T) {
	userCoreClient := &struct{}{} // Mock client
	jwtSecret := "test-secret-key-for-testing-purposes-only"
	logger := logrus.New()
	authService := NewAuthService(userCoreClient, jwtSecret, logger)

	ctx := context.Background()

	testCases := []struct {
		name  string
		token string
	}{
		{"Empty token", ""},
		{"Invalid format", "invalid.token.format"},
		{"Missing parts", "invalid"},
		{"Too many parts", "too.many.parts.in.token.here"},
		{"Invalid base64", "invalid.base64!@#.token"},
		{"Non-JWT format", "Bearer token123"},
		{"Malformed JSON", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.invalid-json.signature"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			userInfo, err := authService.ValidateToken(ctx, tc.token)
			assert.Error(t, err)
			assert.Nil(t, userInfo)
		})
	}
}

func TestAuthService_TokenExpirationEdgeCases(t *testing.T) {
	userCoreClient := &struct{}{} // Mock client
	jwtSecret := "test-secret-key-for-testing-purposes-only"
	logger := logrus.New()
	authService := NewAuthService(userCoreClient, jwtSecret, logger)

	ctx := context.Background()
	userID := uuid.New()

	// Test token that expires in 1 second
	claims := &Claims{
		UserID:   userID.String(),
		Username: "testuser",
		Avatar:   "avatar.jpg",
		Role:     "viewer",
		Level:    1,
		VIPLevel: 0,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(1 * time.Second)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(jwtSecret))
	require.NoError(t, err)

	// Token should be valid initially
	userInfo, err := authService.ValidateToken(ctx, tokenString)
	assert.NoError(t, err)
	assert.NotNil(t, userInfo)

	// Wait for token to expire
	time.Sleep(2 * time.Second)

	// Token should now be expired
	userInfo, err = authService.ValidateToken(ctx, tokenString)
	assert.Error(t, err)
	assert.Nil(t, userInfo)
	assert.Contains(t, err.Error(), "token is expired")
}

func TestAuthService_RolePermissionMatrix(t *testing.T) {
	userCoreClient := &struct{}{} // Mock client
	jwtSecret := "test-secret-key-for-testing-purposes-only"
	logger := logrus.New()
	authService := NewAuthService(userCoreClient, jwtSecret, logger)

	ctx := context.Background()

	roleTestCases := []struct {
		roleString   string
		expectedRole model.UserRole
		level        int
		vipLevel     int
	}{
		{"viewer", model.RoleViewer, 1, 0},
		{"streamer", model.RoleStreamer, 5, 0},
		{"moderator", model.RoleModerator, 10, 0},
		{"admin", model.RoleAdmin, 20, 0},
		{"vip", model.RoleVIP, 1, 5},
		{"VIEWER", model.RoleViewer, 1, 0},  // Case insensitive
		{"Admin", model.RoleViewer, 20, 0},  // Mixed case - should default to viewer
		{"unknown", model.RoleViewer, 1, 0}, // Default fallback
		{"", model.RoleViewer, 1, 0},        // Empty role
		{"invalid", model.RoleViewer, 1, 0}, // Invalid role
	}

	for _, tc := range roleTestCases {
		t.Run(fmt.Sprintf("Role_%s", tc.roleString), func(t *testing.T) {
			userID := uuid.New()
			claims := &Claims{
				UserID:   userID.String(),
				Username: "testuser",
				Avatar:   "avatar.jpg",
				Role:     tc.roleString,
				Level:    tc.level,
				VIPLevel: tc.vipLevel,
				RegisteredClaims: jwt.RegisteredClaims{
					ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
					IssuedAt:  jwt.NewNumericDate(time.Now()),
					NotBefore: jwt.NewNumericDate(time.Now()),
				},
			}

			token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
			tokenString, err := token.SignedString([]byte(jwtSecret))
			require.NoError(t, err)

			userInfo, err := authService.ValidateToken(ctx, tokenString)
			assert.NoError(t, err)
			assert.NotNil(t, userInfo)
			assert.Equal(t, tc.expectedRole, userInfo.Role)
			assert.Equal(t, tc.level, userInfo.Level)
			assert.Equal(t, tc.vipLevel, userInfo.VIPLevel)
		})
	}
}

func TestAuthService_ConcurrentTokenValidation(t *testing.T) {
	userCoreClient := &struct{}{} // Mock client
	jwtSecret := "test-secret-key-for-testing-purposes-only"
	logger := logrus.New()
	authService := NewAuthService(userCoreClient, jwtSecret, logger)

	ctx := context.Background()
	userID := uuid.New()

	// Create a valid token
	claims := &Claims{
		UserID:   userID.String(),
		Username: "testuser",
		Avatar:   "avatar.jpg",
		Role:     "viewer",
		Level:    1,
		VIPLevel: 0,
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(time.Hour)),
			IssuedAt:  jwt.NewNumericDate(time.Now()),
			NotBefore: jwt.NewNumericDate(time.Now()),
		},
	}

	token := jwt.NewWithClaims(jwt.SigningMethodHS256, claims)
	tokenString, err := token.SignedString([]byte(jwtSecret))
	require.NoError(t, err)

	// Test concurrent token validation
	const numGoroutines = 50
	done := make(chan bool, numGoroutines)
	errors := make(chan error, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func() {
			defer func() { done <- true }()
			userInfo, err := authService.ValidateToken(ctx, tokenString)
			if err != nil {
				errors <- err
				return
			}
			if userInfo == nil {
				errors <- fmt.Errorf("userInfo is nil")
				return
			}
			if userInfo.UserID != userID {
				errors <- fmt.Errorf("userID mismatch")
				return
			}
		}()
	}

	// Wait for all goroutines to complete
	for i := 0; i < numGoroutines; i++ {
		<-done
	}

	// Check for any errors
	close(errors)
	for err := range errors {
		t.Errorf("Concurrent validation error: %v", err)
	}
}
