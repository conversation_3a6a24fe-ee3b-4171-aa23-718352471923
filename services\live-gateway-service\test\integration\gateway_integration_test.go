//go:build integration
// +build integration

/*
Copyright (c) 2025 Cina.Club
All rights reserved.
*/

package integration

import (
	"context"
	"testing"
	"time"

	"cina.club/services/live-gateway-service/internal/application/port"
	"cina.club/services/live-gateway-service/internal/application/service"
	"cina.club/services/live-gateway-service/internal/domain/model"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/mock"
)

// --- Mocks ---

type MockCacheRepository struct {
	mock.Mock
}

type MockLiveAPIClient struct {
	mock.Mock
}

type MockMediaServerAdapter struct {
	mock.Mock
}

type MockLoadBalancer struct {
	mock.Mock
}

type MockEventPublisher struct {
	mock.Mock
}

type MockEventStore struct {
	mock.Mock
}

// Add missing types from port package
type Pipeline interface {
	Set(key string, value interface{}, ttl time.Duration) error
	Get(key string, dest interface{}) error
	Delete(key string) error
}

type Transaction interface {
	Set(key string, value interface{}, ttl time.Duration) error
	Get(key string, dest interface{}) error
	Delete(key string) error
	Commit() error
	Rollback() error
}

type EventQuery struct {
	Type      string
	StreamKey string
	StartTime time.Time
	EndTime   time.Time
	Limit     int
	Offset    int
}

// Restore original MockCacheRepository methods
func (m *MockCacheRepository) StoreStreamMapping(ctx context.Context, mapping *model.StreamMapping) error {
	args := m.Called(ctx, mapping)
	return args.Error(0)
}

func (m *MockCacheRepository) GetStreamMapping(ctx context.Context, streamKey string) (*model.StreamMapping, error) {
	args := m.Called(ctx, streamKey)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.StreamMapping), args.Error(1)
}

// Add other required methods for the interface
func (m *MockCacheRepository) Pipeline(ctx context.Context, fn func(pipeline port.Pipeline) error) error {
	args := m.Called(ctx, fn)
	return args.Error(0)
}

func (m *MockCacheRepository) Transaction(ctx context.Context, keys []string, fn func(tx port.Transaction) error) error {
	args := m.Called(ctx, keys, fn)
	return args.Error(0)
}

// Add missing methods to MockCacheRepository
func (m *MockCacheRepository) DeleteStreamMapping(ctx context.Context, streamKey string) error {
	args := m.Called(ctx, streamKey)
	return args.Error(0)
}

// Add missing methods to MockCacheRepository
func (m *MockCacheRepository) Close() error {
	args := m.Called()
	return args.Error(0)
}

// Add missing methods to MockCacheRepository
func (m *MockCacheRepository) DeleteNodeLoad(ctx context.Context, nodeID string) error {
	args := m.Called(ctx, nodeID)
	return args.Error(0)
}

// Add missing methods to MockCacheRepository
func (m *MockCacheRepository) DeleteStreamStats(ctx context.Context, streamKey string) error {
	args := m.Called(ctx, streamKey)
	return args.Error(0)
}

// Add missing methods to MockCacheRepository
func (m *MockCacheRepository) GetNodeLoad(ctx context.Context, nodeID string) (*model.NodeLoad, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.NodeLoad), args.Error(1)
}

// Add missing methods to MockCacheRepository
func (m *MockCacheRepository) GetStreamStats(ctx context.Context, streamKey string) (*model.StreamStats, error) {
	args := m.Called(ctx, streamKey)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.StreamStats), args.Error(1)
}

// Add missing methods to MockCacheRepository
func (m *MockCacheRepository) ListNodeLoads(ctx context.Context) (map[string]*model.NodeLoad, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(map[string]*model.NodeLoad), args.Error(1)
}

// Add missing DeleteNode method to MockCacheRepository
func (m *MockCacheRepository) DeleteNode(ctx context.Context, nodeID string) error {
	args := m.Called(ctx, nodeID)
	return args.Error(0)
}

// Add missing StoreNode method to MockCacheRepository
func (m *MockCacheRepository) StoreNode(ctx context.Context, node *model.MediaNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

// Add missing GetNode method to MockCacheRepository
func (m *MockCacheRepository) GetNode(ctx context.Context, nodeID string) (*model.MediaNode, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

// Add missing GetAllNodes method to MockCacheRepository
func (m *MockCacheRepository) GetAllNodes(ctx context.Context) ([]*model.MediaNode, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.MediaNode), args.Error(1)
}

// Add missing UpdateNodeStatus method to MockCacheRepository
func (m *MockCacheRepository) UpdateNodeStatus(ctx context.Context, nodeID string, status model.NodeStatus) error {
	args := m.Called(ctx, nodeID, status)
	return args.Error(0)
}

// Add missing UpdateNodeStats method to MockCacheRepository
func (m *MockCacheRepository) UpdateNodeStats(ctx context.Context, nodeID string, stats *model.NodeStats) error {
	args := m.Called(ctx, nodeID, stats)
	return args.Error(0)
}

// Ping method already exists, removing duplicate

// Restore original MockMediaServerAdapter methods
func (m *MockMediaServerAdapter) GeneratePushURL(ctx context.Context, req *port.PushURLRequest) (*model.PushURL, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.PushURL), args.Error(1)
}

func (m *MockMediaServerAdapter) GeneratePlayURLs(ctx context.Context, req *port.RequestPlayURLsRequest) ([]*model.PlayURL, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.PlayURL), args.Error(1)
}

func (m *MockMediaServerAdapter) AddNode(ctx context.Context, node *model.MediaServerNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

func (m *MockMediaServerAdapter) GetType() model.MediaServerType {
	args := m.Called()
	return args.Get(0).(model.MediaServerType)
}

func (m *MockMediaServerAdapter) GetServerStats(ctx context.Context, nodeID string) (*model.NodeLoad, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.NodeLoad), args.Error(1)
}

func (m *MockMediaServerAdapter) GetStreamInfo(ctx context.Context, streamKey string) (*port.StreamInfo, error) {
	args := m.Called(ctx, streamKey)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.StreamInfo), args.Error(1)
}

// Add missing methods to MockMediaServerAdapter
func (m *MockMediaServerAdapter) HealthCheck(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

// Add missing methods to MockMediaServerAdapter
func (m *MockMediaServerAdapter) KickStream(ctx context.Context, streamKey string) error {
	args := m.Called(ctx, streamKey)
	return args.Error(0)
}

// Add missing methods to MockMediaServerAdapter
func (m *MockMediaServerAdapter) ParseWebhookRequest(req *port.WebhookParseRequest) (*port.WebhookRequest, error) {
	args := m.Called(req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.WebhookRequest), args.Error(1)
}

// Add missing methods to MockMediaServerAdapter
func (m *MockMediaServerAdapter) RespondWebhookFailure(req *port.WebhookErrorRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

// Add missing methods to MockMediaServerAdapter
func (m *MockMediaServerAdapter) RespondWebhookSuccess(req *port.WebhookResponseRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

// Restore original MockLoadBalancer methods
func (m *MockLoadBalancer) GetAllNodes(ctx context.Context) ([]*model.MediaNode, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.MediaNode), args.Error(1)
}

func (m *MockLoadBalancer) AddNode(ctx context.Context, node *model.MediaServerNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

func (m *MockLoadBalancer) GetAvailableNodes(ctx context.Context) ([]*model.MediaNode, error) {
	args := m.Called(ctx)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.MediaNode), args.Error(1)
}

// Add missing methods to MockLoadBalancer
func (m *MockLoadBalancer) GetNodeByID(ctx context.Context, nodeID string) (*model.MediaNode, error) {
	args := m.Called(ctx, nodeID)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

// Add missing methods to MockLoadBalancer
func (m *MockLoadBalancer) RegisterNode(ctx context.Context, node *model.MediaNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

// Add missing methods to MockLoadBalancer
func (m *MockLoadBalancer) RemoveNode(ctx context.Context, nodeID string) error {
	args := m.Called(ctx, nodeID)
	return args.Error(0)
}

// Add missing methods to MockLoadBalancer
func (m *MockLoadBalancer) SelectNode(ctx context.Context, nodes []*model.MediaNode) (*model.MediaNode, error) {
	args := m.Called(ctx, nodes)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

// Restore original MockLiveAPIClient methods
func (m *MockLiveAPIClient) CheckPushAuth(ctx context.Context, req *port.AuthRequest) (*port.AuthResponse, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*port.AuthResponse), args.Error(1)
}

// Add missing methods to MockLiveAPIClient
func (m *MockLiveAPIClient) NotifyRecordingCompleted(ctx context.Context, event *port.RecordingCompletedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

// Add missing methods to MockLiveAPIClient
func (m *MockLiveAPIClient) NotifyStreamKicked(ctx context.Context, event *port.StreamKickedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

// Add missing methods to MockLiveAPIClient
func (m *MockLiveAPIClient) NotifyStreamPublished(ctx context.Context, event *port.StreamPublishedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

// Add missing methods to MockLiveAPIClient
func (m *MockLiveAPIClient) NotifyStreamUnpublished(ctx context.Context, event *port.StreamUnpublishedEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

// Add missing methods to MockLiveAPIClient
func (m *MockLiveAPIClient) SubmitForModeration(ctx context.Context, event *port.ModerationSubmissionEvent) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

// Add missing methods to MockEventPublisher
func (m *MockEventPublisher) PublishAlertEvent(ctx context.Context, event interface{}) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

// Add missing methods to MockEventPublisher
func (m *MockEventPublisher) PublishEvent(ctx context.Context, event interface{}) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

// Update MockEventStore to match the interface
func (m *MockEventStore) QueryEvents(ctx context.Context, startTime, endTime time.Time) ([]*model.Event, error) {
	args := m.Called(ctx, startTime, endTime)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).([]*model.Event), args.Error(1)
}

// Add missing methods to MockEventStore
func (m *MockEventStore) StoreBatch(ctx context.Context, events []*model.Event) error {
	args := m.Called(ctx, events)
	return args.Error(0)
}

// Add missing methods to MockEventStore
func (m *MockEventStore) StoreEvent(ctx context.Context, event *model.Event) error {
	args := m.Called(ctx, event)
	return args.Error(0)
}

// Add EventType constants
const (
	EventTypePublish = model.EventType("publish")
)

// Add missing methods to MockCacheRepository
func (m *MockCacheRepository) Ping(ctx context.Context) error {
	args := m.Called(ctx)
	return args.Error(0)
}

// Add missing methods to MockMediaServerAdapter
func (m *MockMediaServerAdapter) ValidateWebhookRequest(req *port.WebhookValidateRequest) error {
	args := m.Called(req)
	return args.Error(0)
}

// Add missing methods to MockLoadBalancer
func (m *MockLoadBalancer) SelectNodeByRequest(ctx context.Context, req *port.NodeSelectionRequest) (*model.MediaNode, error) {
	args := m.Called(ctx, req)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*model.MediaNode), args.Error(1)
}

// Add missing methods to MockCacheRepository
func (m *MockCacheRepository) StoreNodeLoad(ctx context.Context, nodeID string, load *model.NodeLoad) error {
	args := m.Called(ctx, nodeID, load)
	return args.Error(0)
}

func (m *MockCacheRepository) StoreStreamStats(ctx context.Context, streamKey string, stats *model.StreamStats) error {
	args := m.Called(ctx, streamKey, stats)
	return args.Error(0)
}

// Add missing methods to MockLoadBalancer
func (m *MockLoadBalancer) UpdateNode(ctx context.Context, node *model.MediaNode) error {
	args := m.Called(ctx, node)
	return args.Error(0)
}

func (m *MockLoadBalancer) UpdateNodeLoad(ctx context.Context, nodeID string, load *model.NodeLoad) error {
	args := m.Called(ctx, nodeID, load)
	return args.Error(0)
}

// Update test cases to use correct AuthResponse fields
func TestGatewayService_HandlePushAuth(t *testing.T) {
	_, _, mockAPIClient := setupTestService()

	mockAPIClient.On("CheckPushAuth", mock.Anything, mock.Anything).Return(&port.AuthResponse{
		Allowed: true,
		Reason:  "",
	}, nil)

	// Add test implementation here
	// ... rest of the test ...
}

// Update setupTestService to use the correct types
func setupTestService() (port.GatewayService, *MockCacheRepository, *MockLiveAPIClient) {
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	mockCache := new(MockCacheRepository)
	mockAPIClient := new(MockLiveAPIClient)
	mockMediaAdapter := &MockMediaServerAdapter{}
	mockLoadBalancer := &MockLoadBalancer{}
	mockEventPublisher := &MockEventPublisher{}
	mockEventStore := &MockEventStore{}

	gatewayService := service.NewGatewayService(
		mockCache,
		mockMediaAdapter,
		mockLoadBalancer,
		mockAPIClient,
		mockEventPublisher,
		mockEventStore,
		&service.GatewayConfig{AuthTimeout: 5 * time.Second},
		logger,
	)

	return gatewayService, mockCache, mockAPIClient
}
