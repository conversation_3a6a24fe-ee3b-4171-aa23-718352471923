/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package metrics

import (
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/sirupsen/logrus"
)

// Collector provides Prometheus metrics collection for the Live IM Service
type Collector struct {
	// HTTP metrics
	httpRequestsTotal    *prometheus.CounterVec
	httpRequestDuration  *prometheus.HistogramVec
	httpRequestSize      *prometheus.HistogramVec
	httpResponseSize     *prometheus.HistogramVec
	httpRequestsInFlight *prometheus.GaugeVec

	// WebSocket metrics
	wsConnectionsTotal   *prometheus.CounterVec
	wsConnectionsActive  *prometheus.GaugeVec
	wsMessagesTotal      *prometheus.CounterVec
	wsMessageSize        *prometheus.HistogramVec
	wsConnectionDuration *prometheus.HistogramVec

	// Hub metrics
	hubClientsConnected    prometheus.Gauge
	hubActiveRooms         prometheus.Gauge
	hubMessagesTotal       *prometheus.CounterVec
	hubBroadcastsTotal     *prometheus.CounterVec
	hubMessagesPerSecond   prometheus.Gauge
	hubBroadcastsPerSecond prometheus.Gauge

	// Room metrics
	roomMembersTotal  *prometheus.GaugeVec
	roomMessagesTotal *prometheus.CounterVec
	roomJoinsTotal    *prometheus.CounterVec
	roomLeavesTotal   *prometheus.CounterVec

	// Message processing metrics
	messageProcessingDuration *prometheus.HistogramVec
	messageValidationTotal    *prometheus.CounterVec
	messageDroppedTotal       *prometheus.CounterVec

	// Rate limiting metrics
	rateLimitHitsTotal    *prometheus.CounterVec
	rateLimitAllowedTotal *prometheus.CounterVec

	// External service metrics
	redisOperationsTotal    *prometheus.CounterVec
	redisOperationDuration  *prometheus.HistogramVec
	externalServiceCalls    *prometheus.CounterVec
	externalServiceDuration *prometheus.HistogramVec

	// System metrics
	systemUptime     prometheus.Gauge
	memoryUsage      prometheus.Gauge
	goroutinesActive prometheus.Gauge

	logger *logrus.Logger
}

// NewCollector creates a new Prometheus metrics collector
func NewCollector(logger *logrus.Logger) *Collector {
	collector := &Collector{
		// HTTP metrics
		httpRequestsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "http_requests_total",
				Help: "Total number of HTTP requests",
			},
			[]string{"method", "endpoint", "status_code"},
		),
		httpRequestDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "http_request_duration_seconds",
				Help:    "Duration of HTTP requests in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"method", "endpoint"},
		),
		httpRequestSize: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "http_request_size_bytes",
				Help:    "Size of HTTP requests in bytes",
				Buckets: prometheus.ExponentialBuckets(100, 10, 8),
			},
			[]string{"method", "endpoint"},
		),
		httpResponseSize: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "http_response_size_bytes",
				Help:    "Size of HTTP responses in bytes",
				Buckets: prometheus.ExponentialBuckets(100, 10, 8),
			},
			[]string{"method", "endpoint"},
		),
		httpRequestsInFlight: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "http_requests_in_flight",
				Help: "Number of HTTP requests currently being processed",
			},
			[]string{"method", "endpoint"},
		),

		// WebSocket metrics
		wsConnectionsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "websocket_connections_total",
				Help: "Total number of WebSocket connections",
			},
			[]string{"status"}, // connected, disconnected, failed
		),
		wsConnectionsActive: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "websocket_connections_active",
				Help: "Number of active WebSocket connections",
			},
			[]string{"room_id"},
		),
		wsMessagesTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "websocket_messages_total",
				Help: "Total number of WebSocket messages",
			},
			[]string{"type", "direction"}, // direction: inbound, outbound
		),
		wsMessageSize: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "websocket_message_size_bytes",
				Help:    "Size of WebSocket messages in bytes",
				Buckets: prometheus.ExponentialBuckets(10, 10, 6),
			},
			[]string{"type", "direction"},
		),
		wsConnectionDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "websocket_connection_duration_seconds",
				Help:    "Duration of WebSocket connections in seconds",
				Buckets: prometheus.ExponentialBuckets(1, 10, 6),
			},
			[]string{"room_id"},
		),

		// Hub metrics
		hubClientsConnected: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "hub_clients_connected",
				Help: "Number of clients connected to the hub",
			},
		),
		hubActiveRooms: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "hub_active_rooms",
				Help: "Number of active rooms in the hub",
			},
		),
		hubMessagesTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "hub_messages_total",
				Help: "Total number of messages processed by the hub",
			},
			[]string{"type"},
		),
		hubBroadcastsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "hub_broadcasts_total",
				Help: "Total number of broadcasts sent by the hub",
			},
			[]string{"room_id"},
		),
		hubMessagesPerSecond: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "hub_messages_per_second",
				Help: "Current messages per second rate",
			},
		),
		hubBroadcastsPerSecond: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "hub_broadcasts_per_second",
				Help: "Current broadcasts per second rate",
			},
		),

		// Room metrics
		roomMembersTotal: prometheus.NewGaugeVec(
			prometheus.GaugeOpts{
				Name: "room_members_total",
				Help: "Number of members in each room",
			},
			[]string{"room_id"},
		),
		roomMessagesTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "room_messages_total",
				Help: "Total number of messages in each room",
			},
			[]string{"room_id", "type"},
		),
		roomJoinsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "room_joins_total",
				Help: "Total number of room joins",
			},
			[]string{"room_id"},
		),
		roomLeavesTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "room_leaves_total",
				Help: "Total number of room leaves",
			},
			[]string{"room_id"},
		),

		// Message processing metrics
		messageProcessingDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "message_processing_duration_seconds",
				Help:    "Duration of message processing in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"type", "status"},
		),
		messageValidationTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "message_validation_total",
				Help: "Total number of message validations",
			},
			[]string{"type", "result"}, // result: valid, invalid
		),
		messageDroppedTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "message_dropped_total",
				Help: "Total number of dropped messages",
			},
			[]string{"reason"},
		),

		// Rate limiting metrics
		rateLimitHitsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "rate_limit_hits_total",
				Help: "Total number of rate limit hits",
			},
			[]string{"user_id", "action"},
		),
		rateLimitAllowedTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "rate_limit_allowed_total",
				Help: "Total number of rate limit allowed requests",
			},
			[]string{"action"},
		),

		// External service metrics
		redisOperationsTotal: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "redis_operations_total",
				Help: "Total number of Redis operations",
			},
			[]string{"operation", "status"},
		),
		redisOperationDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "redis_operation_duration_seconds",
				Help:    "Duration of Redis operations in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"operation"},
		),
		externalServiceCalls: prometheus.NewCounterVec(
			prometheus.CounterOpts{
				Name: "external_service_calls_total",
				Help: "Total number of external service calls",
			},
			[]string{"service", "method", "status"},
		),
		externalServiceDuration: prometheus.NewHistogramVec(
			prometheus.HistogramOpts{
				Name:    "external_service_duration_seconds",
				Help:    "Duration of external service calls in seconds",
				Buckets: prometheus.DefBuckets,
			},
			[]string{"service", "method"},
		),

		// System metrics
		systemUptime: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "system_uptime_seconds",
				Help: "System uptime in seconds",
			},
		),
		memoryUsage: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "memory_usage_bytes",
				Help: "Memory usage in bytes",
			},
		),
		goroutinesActive: prometheus.NewGauge(
			prometheus.GaugeOpts{
				Name: "goroutines_active",
				Help: "Number of active goroutines",
			},
		),

		logger: logger,
	}

	collector.registerMetrics()
	return collector
}

// registerMetrics registers all metrics with Prometheus
func (c *Collector) registerMetrics() {
	prometheus.MustRegister(
		// HTTP metrics
		c.httpRequestsTotal,
		c.httpRequestDuration,
		c.httpRequestSize,
		c.httpResponseSize,
		c.httpRequestsInFlight,

		// WebSocket metrics
		c.wsConnectionsTotal,
		c.wsConnectionsActive,
		c.wsMessagesTotal,
		c.wsMessageSize,
		c.wsConnectionDuration,

		// Hub metrics
		c.hubClientsConnected,
		c.hubActiveRooms,
		c.hubMessagesTotal,
		c.hubBroadcastsTotal,
		c.hubMessagesPerSecond,
		c.hubBroadcastsPerSecond,

		// Room metrics
		c.roomMembersTotal,
		c.roomMessagesTotal,
		c.roomJoinsTotal,
		c.roomLeavesTotal,

		// Message processing metrics
		c.messageProcessingDuration,
		c.messageValidationTotal,
		c.messageDroppedTotal,

		// Rate limiting metrics
		c.rateLimitHitsTotal,
		c.rateLimitAllowedTotal,

		// External service metrics
		c.redisOperationsTotal,
		c.redisOperationDuration,
		c.externalServiceCalls,
		c.externalServiceDuration,

		// System metrics
		c.systemUptime,
		c.memoryUsage,
		c.goroutinesActive,
	)
}

// HTTP Metrics Methods

// RecordHTTPRequest records an HTTP request
func (c *Collector) RecordHTTPRequest(method, endpoint, statusCode string, duration time.Duration, requestSize, responseSize float64) {
	c.httpRequestsTotal.WithLabelValues(method, endpoint, statusCode).Inc()
	c.httpRequestDuration.WithLabelValues(method, endpoint).Observe(duration.Seconds())
	c.httpRequestSize.WithLabelValues(method, endpoint).Observe(requestSize)
	c.httpResponseSize.WithLabelValues(method, endpoint).Observe(responseSize)
}

// RecordHTTPRequestInFlight records in-flight HTTP requests
func (c *Collector) RecordHTTPRequestInFlight(method, endpoint string, delta float64) {
	c.httpRequestsInFlight.WithLabelValues(method, endpoint).Add(delta)
}

// WebSocket Metrics Methods

// RecordWebSocketConnection records a WebSocket connection event
func (c *Collector) RecordWebSocketConnection(status string) {
	c.wsConnectionsTotal.WithLabelValues(status).Inc()
}

// UpdateWebSocketConnectionsActive updates active WebSocket connections
func (c *Collector) UpdateWebSocketConnectionsActive(roomID string, count float64) {
	c.wsConnectionsActive.WithLabelValues(roomID).Set(count)
}

// RecordWebSocketMessage records a WebSocket message
func (c *Collector) RecordWebSocketMessage(messageType, direction string, size float64) {
	c.wsMessagesTotal.WithLabelValues(messageType, direction).Inc()
	c.wsMessageSize.WithLabelValues(messageType, direction).Observe(size)
}

// RecordWebSocketConnectionDuration records WebSocket connection duration
func (c *Collector) RecordWebSocketConnectionDuration(roomID string, duration time.Duration) {
	c.wsConnectionDuration.WithLabelValues(roomID).Observe(duration.Seconds())
}

// Hub Metrics Methods

// UpdateHubStats updates hub statistics
func (c *Collector) UpdateHubStats(clientsConnected, activeRooms int, messagesPerSecond, broadcastsPerSecond float64) {
	c.hubClientsConnected.Set(float64(clientsConnected))
	c.hubActiveRooms.Set(float64(activeRooms))
	c.hubMessagesPerSecond.Set(messagesPerSecond)
	c.hubBroadcastsPerSecond.Set(broadcastsPerSecond)
}

// RecordHubMessage records a hub message
func (c *Collector) RecordHubMessage(messageType string) {
	c.hubMessagesTotal.WithLabelValues(messageType).Inc()
}

// RecordHubBroadcast records a hub broadcast
func (c *Collector) RecordHubBroadcast(roomID string) {
	c.hubBroadcastsTotal.WithLabelValues(roomID).Inc()
}

// Room Metrics Methods

// UpdateRoomMembers updates room member count
func (c *Collector) UpdateRoomMembers(roomID string, count float64) {
	c.roomMembersTotal.WithLabelValues(roomID).Set(count)
}

// RecordRoomMessage records a room message
func (c *Collector) RecordRoomMessage(roomID, messageType string) {
	c.roomMessagesTotal.WithLabelValues(roomID, messageType).Inc()
}

// RecordRoomJoin records a room join
func (c *Collector) RecordRoomJoin(roomID string) {
	c.roomJoinsTotal.WithLabelValues(roomID).Inc()
}

// RecordRoomLeave records a room leave
func (c *Collector) RecordRoomLeave(roomID string) {
	c.roomLeavesTotal.WithLabelValues(roomID).Inc()
}

// Message Processing Metrics Methods

// RecordMessageProcessing records message processing duration
func (c *Collector) RecordMessageProcessing(messageType, status string, duration time.Duration) {
	c.messageProcessingDuration.WithLabelValues(messageType, status).Observe(duration.Seconds())
}

// RecordMessageValidation records message validation result
func (c *Collector) RecordMessageValidation(messageType, result string) {
	c.messageValidationTotal.WithLabelValues(messageType, result).Inc()
}

// RecordMessageDropped records a dropped message
func (c *Collector) RecordMessageDropped(reason string) {
	c.messageDroppedTotal.WithLabelValues(reason).Inc()
}

// Rate Limiting Metrics Methods

// RecordRateLimitHit records a rate limit hit
func (c *Collector) RecordRateLimitHit(userID, action string) {
	c.rateLimitHitsTotal.WithLabelValues(userID, action).Inc()
}

// RecordRateLimitAllowed records an allowed rate limit request
func (c *Collector) RecordRateLimitAllowed(action string) {
	c.rateLimitAllowedTotal.WithLabelValues(action).Inc()
}

// External Service Metrics Methods

// RecordRedisOperation records a Redis operation
func (c *Collector) RecordRedisOperation(operation, status string, duration time.Duration) {
	c.redisOperationsTotal.WithLabelValues(operation, status).Inc()
	c.redisOperationDuration.WithLabelValues(operation).Observe(duration.Seconds())
}

// RecordExternalServiceCall records an external service call
func (c *Collector) RecordExternalServiceCall(service, method, status string, duration time.Duration) {
	c.externalServiceCalls.WithLabelValues(service, method, status).Inc()
	c.externalServiceDuration.WithLabelValues(service, method).Observe(duration.Seconds())
}

// System Metrics Methods

// UpdateSystemUptime updates system uptime
func (c *Collector) UpdateSystemUptime(uptime time.Duration) {
	c.systemUptime.Set(uptime.Seconds())
}

// UpdateMemoryUsage updates memory usage
func (c *Collector) UpdateMemoryUsage(bytes float64) {
	c.memoryUsage.Set(bytes)
}

// UpdateGoroutinesActive updates active goroutines count
func (c *Collector) UpdateGoroutinesActive(count float64) {
	c.goroutinesActive.Set(count)
}
