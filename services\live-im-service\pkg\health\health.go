/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:00:00
Modified: 2025-07-11 10:00:00
*/

package health

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// Status represents the health status of a component
type Status string

const (
	// StatusHealthy indicates the component is healthy
	StatusHealthy Status = "healthy"
	// StatusDegraded indicates the component is degraded but functional
	StatusDegraded Status = "degraded"
	// StatusUnhealthy indicates the component is unhealthy
	StatusUnhealthy Status = "unhealthy"
	// StatusUnknown indicates the component status is unknown
	StatusUnknown Status = "unknown"
)

// CheckResult represents the result of a health check
type CheckResult struct {
	Name        string                 `json:"name"`
	Status      Status                 `json:"status"`
	Message     string                 `json:"message"`
	Details     map[string]interface{} `json:"details,omitempty"`
	Duration    time.Duration          `json:"duration"`
	Timestamp   time.Time              `json:"timestamp"`
	Error       string                 `json:"error,omitempty"`
}

// OverallHealth represents the overall health status
type OverallHealth struct {
	Status      Status                  `json:"status"`
	Message     string                  `json:"message"`
	Timestamp   time.Time               `json:"timestamp"`
	Uptime      time.Duration           `json:"uptime"`
	Version     string                  `json:"version"`
	Environment string                  `json:"environment"`
	Checks      map[string]CheckResult  `json:"checks"`
	Summary     HealthSummary           `json:"summary"`
}

// HealthSummary provides a summary of health check results
type HealthSummary struct {
	Total     int `json:"total"`
	Healthy   int `json:"healthy"`
	Degraded  int `json:"degraded"`
	Unhealthy int `json:"unhealthy"`
	Unknown   int `json:"unknown"`
}

// Checker defines the interface for health checkers
type Checker interface {
	// Name returns the name of the health check
	Name() string
	
	// Check performs the health check
	Check(ctx context.Context) CheckResult
	
	// Timeout returns the timeout for this health check
	Timeout() time.Duration
	
	// Critical indicates if this check is critical for overall health
	Critical() bool
}

// Service provides health checking functionality
type Service struct {
	checkers    map[string]Checker
	startTime   time.Time
	version     string
	environment string
	logger      *logrus.Logger
	mutex       sync.RWMutex
}

// Config defines the configuration for the health service
type Config struct {
	Version     string
	Environment string
	Timeout     time.Duration
}

// NewService creates a new health service
func NewService(config Config, logger *logrus.Logger) *Service {
	if config.Timeout == 0 {
		config.Timeout = 30 * time.Second
	}

	return &Service{
		checkers:    make(map[string]Checker),
		startTime:   time.Now(),
		version:     config.Version,
		environment: config.Environment,
		logger:      logger,
	}
}

// RegisterChecker registers a health checker
func (s *Service) RegisterChecker(checker Checker) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	s.checkers[checker.Name()] = checker
	s.logger.WithField("checker", checker.Name()).Info("Health checker registered")
}

// UnregisterChecker unregisters a health checker
func (s *Service) UnregisterChecker(name string) {
	s.mutex.Lock()
	defer s.mutex.Unlock()
	
	delete(s.checkers, name)
	s.logger.WithField("checker", name).Info("Health checker unregistered")
}

// Check performs all health checks
func (s *Service) Check(ctx context.Context) OverallHealth {
	s.mutex.RLock()
	checkers := make(map[string]Checker)
	for name, checker := range s.checkers {
		checkers[name] = checker
	}
	s.mutex.RUnlock()

	results := make(map[string]CheckResult)
	var wg sync.WaitGroup
	resultsChan := make(chan CheckResult, len(checkers))

	// Run all checks concurrently
	for _, checker := range checkers {
		wg.Add(1)
		go func(c Checker) {
			defer wg.Done()
			
			checkCtx, cancel := context.WithTimeout(ctx, c.Timeout())
			defer cancel()
			
			start := time.Now()
			result := c.Check(checkCtx)
			result.Duration = time.Since(start)
			result.Timestamp = time.Now()
			
			resultsChan <- result
		}(checker)
	}

	// Wait for all checks to complete
	go func() {
		wg.Wait()
		close(resultsChan)
	}()

	// Collect results
	for result := range resultsChan {
		results[result.Name] = result
	}

	// Calculate overall health
	overall := s.calculateOverallHealth(results)
	
	s.logger.WithFields(logrus.Fields{
		"status":       overall.Status,
		"total_checks": len(results),
		"healthy":      overall.Summary.Healthy,
		"degraded":     overall.Summary.Degraded,
		"unhealthy":    overall.Summary.Unhealthy,
	}).Info("Health check completed")

	return overall
}

// CheckSingle performs a single health check
func (s *Service) CheckSingle(ctx context.Context, name string) (CheckResult, error) {
	s.mutex.RLock()
	checker, exists := s.checkers[name]
	s.mutex.RUnlock()

	if !exists {
		return CheckResult{}, fmt.Errorf("health checker %s not found", name)
	}

	checkCtx, cancel := context.WithTimeout(ctx, checker.Timeout())
	defer cancel()

	start := time.Now()
	result := checker.Check(checkCtx)
	result.Duration = time.Since(start)
	result.Timestamp = time.Now()

	return result, nil
}

// GetCheckers returns the list of registered checkers
func (s *Service) GetCheckers() []string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	names := make([]string, 0, len(s.checkers))
	for name := range s.checkers {
		names = append(names, name)
	}
	return names
}

// Liveness returns a simple liveness check
func (s *Service) Liveness() map[string]interface{} {
	return map[string]interface{}{
		"status":    "alive",
		"timestamp": time.Now(),
		"uptime":    time.Since(s.startTime),
	}
}

// Readiness returns readiness status based on critical health checks
func (s *Service) Readiness(ctx context.Context) map[string]interface{} {
	s.mutex.RLock()
	checkers := make(map[string]Checker)
	for name, checker := range s.checkers {
		if checker.Critical() {
			checkers[name] = checker
		}
	}
	s.mutex.RUnlock()

	if len(checkers) == 0 {
		return map[string]interface{}{
			"status":    "ready",
			"timestamp": time.Now(),
			"message":   "No critical health checks configured",
		}
	}

	results := make(map[string]CheckResult)
	var wg sync.WaitGroup
	resultsChan := make(chan CheckResult, len(checkers))

	// Run critical checks
	for _, checker := range checkers {
		wg.Add(1)
		go func(c Checker) {
			defer wg.Done()
			
			checkCtx, cancel := context.WithTimeout(ctx, c.Timeout())
			defer cancel()
			
			result := c.Check(checkCtx)
			result.Timestamp = time.Now()
			
			resultsChan <- result
		}(checker)
	}

	go func() {
		wg.Wait()
		close(resultsChan)
	}()

	// Collect results
	for result := range resultsChan {
		results[result.Name] = result
	}

	// Determine readiness
	ready := true
	for _, result := range results {
		if result.Status == StatusUnhealthy {
			ready = false
			break
		}
	}

	status := "ready"
	if !ready {
		status = "not_ready"
	}

	return map[string]interface{}{
		"status":    status,
		"timestamp": time.Now(),
		"checks":    results,
	}
}

// calculateOverallHealth calculates the overall health status
func (s *Service) calculateOverallHealth(results map[string]CheckResult) OverallHealth {
	summary := HealthSummary{
		Total: len(results),
	}

	overallStatus := StatusHealthy
	criticalUnhealthy := false

	for _, result := range results {
		switch result.Status {
		case StatusHealthy:
			summary.Healthy++
		case StatusDegraded:
			summary.Degraded++
			if overallStatus == StatusHealthy {
				overallStatus = StatusDegraded
			}
		case StatusUnhealthy:
			summary.Unhealthy++
			// Check if this is a critical checker
			s.mutex.RLock()
			checker, exists := s.checkers[result.Name]
			s.mutex.RUnlock()
			
			if exists && checker.Critical() {
				criticalUnhealthy = true
			}
			overallStatus = StatusUnhealthy
		case StatusUnknown:
			summary.Unknown++
			if overallStatus == StatusHealthy {
				overallStatus = StatusDegraded
			}
		}
	}

	// If any critical checker is unhealthy, overall status is unhealthy
	if criticalUnhealthy {
		overallStatus = StatusUnhealthy
	}

	message := s.getOverallMessage(overallStatus, summary)

	return OverallHealth{
		Status:      overallStatus,
		Message:     message,
		Timestamp:   time.Now(),
		Uptime:      time.Since(s.startTime),
		Version:     s.version,
		Environment: s.environment,
		Checks:      results,
		Summary:     summary,
	}
}

// getOverallMessage returns an appropriate message for the overall health status
func (s *Service) getOverallMessage(status Status, summary HealthSummary) string {
	switch status {
	case StatusHealthy:
		return fmt.Sprintf("All %d health checks are healthy", summary.Total)
	case StatusDegraded:
		return fmt.Sprintf("Service is degraded: %d healthy, %d degraded, %d unhealthy", 
			summary.Healthy, summary.Degraded, summary.Unhealthy)
	case StatusUnhealthy:
		return fmt.Sprintf("Service is unhealthy: %d healthy, %d degraded, %d unhealthy", 
			summary.Healthy, summary.Degraded, summary.Unhealthy)
	default:
		return "Health status unknown"
	}
}
