#!/bin/bash

# Copyright (c) 2025 Cina.Club
# All rights reserved.
# Created: 2025-07-11 10:12:13
# Modified: 2025-07-11 10:12:13

set -e

# Script to generate protobuf Go files

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SERVICE_DIR="$(dirname "$SCRIPT_DIR")"
PROTO_DIR="$SERVICE_DIR/proto"
OUTPUT_DIR="$SERVICE_DIR/proto/live_im"

echo "Generating protobuf files..."
echo "Proto directory: $PROTO_DIR"
echo "Output directory: $OUTPUT_DIR"

# Create output directory if it doesn't exist
mkdir -p "$OUTPUT_DIR"

# Generate Go protobuf files
protoc \
    --proto_path="$PROTO_DIR" \
    --go_out="$OUTPUT_DIR" \
    --go_opt=paths=source_relative \
    "$PROTO_DIR/live_im.proto"

echo "Protobuf generation completed successfully!"
echo "Generated files:"
find "$OUTPUT_DIR" -name "*.pb.go" -type f
