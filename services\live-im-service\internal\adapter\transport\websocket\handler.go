/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:12:13
Modified: 2025-07-11 10:12:13
*/

package websocket

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"

	"cina.club/services/live-im-service/internal/application/port"
	"cina.club/services/live-im-service/internal/application/service"
	"cina.club/services/live-im-service/internal/domain/model"
)

// WebSocketConfig defines WebSocket configuration.
type WebSocketConfig struct {
	WriteWait         time.Duration `mapstructure:"write_wait"`
	PongWait          time.Duration `mapstructure:"pong_wait"`
	PingPeriod        time.Duration `mapstructure:"ping_period"`
	MaxMessageSize    int64         `mapstructure:"max_message_size"`
	ReadBufferSize    int           `mapstructure:"read_buffer_size"`
	WriteBufferSize   int           `mapstructure:"write_buffer_size"`
	CheckOrigin       bool          `mapstructure:"check_origin"`
	EnableCompression bool          `mapstructure:"enable_compression"`
}

// SecurityConfig defines security configuration.
type SecurityConfig struct {
	JWTSecret           string   `mapstructure:"jwt_secret"`
	AllowedOrigins      []string `mapstructure:"allowed_origins"`
	EnableIPWhitelist   bool     `mapstructure:"enable_ip_whitelist"`
	IPWhitelist         []string `mapstructure:"ip_whitelist"`
	MaxConnectionsPerIP int      `mapstructure:"max_connections_per_ip"`
	EnableCORS          bool     `mapstructure:"enable_cors"`
}

// Handler handles WebSocket connections.
type Handler struct {
	hub         *service.Hub
	authService port.AuthService
	config      WebSocketConfig
	security    SecurityConfig
	logger      *logrus.Logger
	upgrader    websocket.Upgrader
}

// NewHandler creates a new WebSocket handler.
func NewHandler(
	hub *service.Hub,
	config WebSocketConfig,
	security SecurityConfig,
	logger *logrus.Logger,
) *Handler {
	upgrader := websocket.Upgrader{
		ReadBufferSize:    config.ReadBufferSize,
		WriteBufferSize:   config.WriteBufferSize,
		EnableCompression: config.EnableCompression,
		CheckOrigin: func(r *http.Request) bool {
			if !config.CheckOrigin {
				return true
			}
			// TODO: Implement proper origin checking
			return true
		},
	}

	return &Handler{
		hub:      hub,
		config:   config,
		security: security,
		logger:   logger.WithField("component", "websocket_handler"),
		upgrader: upgrader,
	}
}

// HandleWebSocket handles WebSocket connections.
func (h *Handler) HandleWebSocket(c *gin.Context) {
	// Upgrade HTTP connection to WebSocket
	conn, err := h.upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		h.logger.WithError(err).Error("Failed to upgrade connection")
		return
	}
	defer conn.Close()

	// Set connection limits
	conn.SetReadLimit(h.config.MaxMessageSize)

	// Wait for authentication message
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	userInfo, roomID, err := h.authenticateConnection(ctx, conn)
	if err != nil {
		h.logger.WithError(err).Error("Authentication failed")
		h.sendAuthError(conn, err.Error())
		return
	}

	// Create client
	client := model.NewClient(
		userInfo.UserID,
		userInfo.Username,
		userInfo.Avatar,
		conn,
		c.Request,
		h.hub,
		h.logger.Logger,
	)

	// Set client role
	client.SetRole(userInfo.Role)

	// Join room
	if err := client.JoinRoom(roomID); err != nil {
		h.logger.WithError(err).Error("Failed to join room")
		h.sendAuthError(conn, "Failed to join room")
		return
	}

	// Send authentication success
	if err := h.sendAuthSuccess(conn, roomID, userInfo); err != nil {
		h.logger.WithError(err).Error("Failed to send auth success")
		return
	}

	// Register client with hub
	h.hub.RegisterClient(client)

	// Start client pumps
	client.Start()

	h.logger.WithFields(logrus.Fields{
		"user_id":   userInfo.UserID.String(),
		"username":  userInfo.Username,
		"room_id":   roomID,
		"client_id": client.ID,
	}).Info("WebSocket client connected")
}

// authenticateConnection authenticates a WebSocket connection.
func (h *Handler) authenticateConnection(ctx context.Context, conn *websocket.Conn) (*port.UserInfo, string, error) {
	// Set read deadline for authentication
	if err := conn.SetReadDeadline(time.Now().Add(30 * time.Second)); err != nil {
		return nil, "", fmt.Errorf("failed to set read deadline: %w", err)
	}

	// Read authentication message
	messageType, data, err := conn.ReadMessage()
	if err != nil {
		return nil, "", fmt.Errorf("failed to read auth message: %w", err)
	}

	if messageType != websocket.TextMessage {
		return nil, "", fmt.Errorf("expected text message for authentication")
	}

	// Parse authentication message
	var authMsg model.Message
	if err := json.Unmarshal(data, &authMsg); err != nil {
		return nil, "", fmt.Errorf("failed to parse auth message: %w", err)
	}

	if authMsg.Type != model.MessageTypeAuth {
		return nil, "", fmt.Errorf("expected auth message, got %s", authMsg.Type)
	}

	if authMsg.Token == "" {
		return nil, "", fmt.Errorf("token is required")
	}

	if authMsg.RoomID == "" {
		return nil, "", fmt.Errorf("room_id is required")
	}

	// Validate token
	userInfo, err := h.authService.ValidateToken(ctx, authMsg.Token)
	if err != nil {
		return nil, "", fmt.Errorf("token validation failed: %w", err)
	}

	// Check room permission
	hasPermission, err := h.authService.CheckRoomPermission(ctx, userInfo.UserID, authMsg.RoomID)
	if err != nil {
		return nil, "", fmt.Errorf("permission check failed: %w", err)
	}

	if !hasPermission {
		return nil, "", fmt.Errorf("no permission to join room %s", authMsg.RoomID)
	}

	return userInfo, authMsg.RoomID, nil
}

// sendAuthSuccess sends authentication success message.
func (h *Handler) sendAuthSuccess(conn *websocket.Conn, roomID string, userInfo *port.UserInfo) error {
	// TODO: Get actual room info and online users
	roomInfo := &model.RoomInfo{
		RoomID:      roomID,
		Title:       "Live Room",
		Status:      "live",
		OnlineCount: 0,
		StartTime:   time.Now(),
	}

	onlineUsers := []*model.User{} // TODO: Get actual online users

	authResult := model.NewAuthResultMessage(true, roomInfo, onlineUsers, "")

	return h.sendMessage(conn, authResult)
}

// sendAuthError sends authentication error message.
func (h *Handler) sendAuthError(conn *websocket.Conn, errorMsg string) {
	authResult := model.NewAuthResultMessage(false, nil, nil, errorMsg)
	h.sendMessage(conn, authResult)
}

// sendMessage sends a message through WebSocket.
func (h *Handler) sendMessage(conn *websocket.Conn, message model.Message) error {
	data, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal message: %w", err)
	}

	if err := conn.SetWriteDeadline(time.Now().Add(h.config.WriteWait)); err != nil {
		return fmt.Errorf("failed to set write deadline: %w", err)
	}

	return conn.WriteMessage(websocket.TextMessage, data)
}

// SetAuthService sets the auth service.
func (h *Handler) SetAuthService(authService port.AuthService) {
	h.authService = authService
}

// GetStats returns handler statistics.
func (h *Handler) GetStats() map[string]interface{} {
	return map[string]interface{}{
		"component": "websocket_handler",
		"config": map[string]interface{}{
			"max_message_size":   h.config.MaxMessageSize,
			"read_buffer_size":   h.config.ReadBufferSize,
			"write_buffer_size":  h.config.WriteBufferSize,
			"enable_compression": h.config.EnableCompression,
		},
	}
}
