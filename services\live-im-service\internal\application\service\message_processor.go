/*
Copyright (c) 2025 Cina.Club
All rights reserved.
Created: 2025-07-11 10:12:13
Modified: 2025-07-11 10:12:13
*/

package service

import (
	"context"
	"fmt"
	"sync"
	"sync/atomic"
	"time"

	"github.com/sirupsen/logrus"

	"cina.club/services/live-im-service/internal/application/port"
	"cina.club/services/live-im-service/internal/domain/model"
)

// MessageProcessor implements the port.MessageProcessor interface.
type MessageProcessor struct {
	liveAPIClient  port.LiveAPIClient
	billingClient  port.BillingClient
	cinaCoinClient port.CinaCoinClient
	broadcaster    port.Broadcaster
	logger         *logrus.Entry

	// Like aggregation
	likeAggregator *LikeAggregator
}

// NewMessageProcessor creates a new message processor.
func NewMessageProcessor(
	liveAPIClient interface{}, // TODO: Replace with actual client
	billingClient interface{}, // TODO: Replace with actual client
	cinaCoinClient interface{}, // TODO: Replace with actual client
	logger *logrus.Logger,
) port.MessageProcessor {
	return &MessageProcessor{
		// TODO: Assign actual clients when implemented
		logger:         logger.WithField("component", "message_processor"),
		likeAggregator: NewLikeAggregator(logger),
	}
}

// ProcessMessage processes a message from a client.
func (m *MessageProcessor) ProcessMessage(ctx context.Context, client *model.Client, message model.Message) error {
	// Set message metadata
	message.FromClient = client
	message.Timestamp = time.Now()

	// Process based on message type
	switch message.Type {
	case model.MessageTypeBarrage:
		return m.ProcessBarrage(ctx, client, message)
	case model.MessageTypeLike:
		return m.ProcessLike(ctx, client, message)
	case model.MessageTypeGift:
		return m.ProcessGift(ctx, client, message)
	case model.MessageTypeAuth:
		return m.processAuth(ctx, client, message)
	case model.MessageTypePing:
		return m.processPing(ctx, client, message)
	default:
		return fmt.Errorf("unsupported message type: %s", message.Type)
	}
}

// ProcessBarrage processes a barrage message.
func (m *MessageProcessor) ProcessBarrage(ctx context.Context, client *model.Client, message model.Message) error {
	// Create user info from client
	user := &model.User{
		UserID:   client.UserID,
		Username: client.Username,
		Avatar:   client.Avatar,
		Role:     client.Role,
	}

	// Create broadcast message
	broadcastMsg := model.NewNewBarrageMessage(message.Content, client.RoomID, user)

	// Broadcast to room via Hub
	if client.Hub != nil {
		client.Hub.BroadcastToRoom(client.RoomID, broadcastMsg, client)
	}

	m.logger.WithFields(logrus.Fields{
		"user_id":  client.UserID.String(),
		"room_id":  client.RoomID,
		"content":  message.Content,
		"msg_id":   broadcastMsg.MessageID,
	}).Debug("Barrage message processed")

	return nil
}

// ProcessLike processes a like message.
func (m *MessageProcessor) ProcessLike(ctx context.Context, client *model.Client, message model.Message) error {
	// Add to like aggregator instead of broadcasting immediately
	m.likeAggregator.AddLike(client.RoomID, message.Count)

	m.logger.WithFields(logrus.Fields{
		"user_id": client.UserID.String(),
		"room_id": client.RoomID,
		"count":   message.Count,
	}).Debug("Like message processed")

	return nil
}

// ProcessGift processes a gift message with payment.
func (m *MessageProcessor) ProcessGift(ctx context.Context, client *model.Client, message model.Message) error {
	// TODO: Implement actual gift processing with payment
	// This is a critical business logic that requires:
	// 1. Validate gift exists and get price
	// 2. Call CinaCoin service to debit user account
	// 3. If successful, broadcast gift message
	// 4. If failed, send error to client

	// For now, just log and return error
	m.logger.WithFields(logrus.Fields{
		"user_id":    client.UserID.String(),
		"room_id":    client.RoomID,
		"gift_id":    message.GiftID,
		"count":      message.Count,
		"to_user_id": message.ToUserID,
	}).Warn("Gift processing not yet implemented")

	// Send error to client
	errorMsg := model.NewErrorMessage("Gift processing is not yet implemented")
	return client.SendMessage(errorMsg)
}

// processAuth processes an authentication message.
func (m *MessageProcessor) processAuth(ctx context.Context, client *model.Client, message model.Message) error {
	// Authentication should be handled at the WebSocket handler level
	// This should not normally be called
	m.logger.WithField("user_id", client.UserID.String()).Warn("Auth message received after connection established")
	return nil
}

// processPing processes a ping message.
func (m *MessageProcessor) processPing(ctx context.Context, client *model.Client, message model.Message) error {
	// Send pong response
	pongMsg := model.NewPongMessage()
	return client.SendMessage(pongMsg)
}

// LikeAggregator aggregates like messages to reduce broadcast frequency.
type LikeAggregator struct {
	logger *logrus.Logger
	
	// Room like counters
	roomLikes map[string]*int64 // map[roomID] -> atomic counter
	mu        sync.RWMutex
	
	// Control
	ticker *time.Ticker
	done   chan struct{}
}

// NewLikeAggregator creates a new like aggregator.
func NewLikeAggregator(logger *logrus.Logger) *LikeAggregator {
	aggregator := &LikeAggregator{
		logger:    logger.WithField("component", "like_aggregator"),
		roomLikes: make(map[string]*int64),
		ticker:    time.NewTicker(1 * time.Second), // Aggregate every second
		done:      make(chan struct{}),
	}

	// Start aggregation goroutine
	go aggregator.run()

	return aggregator
}

// AddLike adds a like for a room.
func (l *LikeAggregator) AddLike(roomID string, count int) {
	l.mu.Lock()
	counter, exists := l.roomLikes[roomID]
	if !exists {
		counter = new(int64)
		l.roomLikes[roomID] = counter
	}
	l.mu.Unlock()

	atomic.AddInt64(counter, int64(count))
}

// run runs the aggregation loop.
func (l *LikeAggregator) run() {
	for {
		select {
		case <-l.ticker.C:
			l.flushLikes()
		case <-l.done:
			l.ticker.Stop()
			return
		}
	}
}

// flushLikes flushes aggregated likes and broadcasts them.
func (l *LikeAggregator) flushLikes() {
	l.mu.Lock()
	defer l.mu.Unlock()

	for roomID, counter := range l.roomLikes {
		count := atomic.SwapInt64(counter, 0)
		if count > 0 {
			// Create like burst message
			likeBurstMsg := model.NewLikeBurstMessage(int(count), roomID)
			
			// TODO: Broadcast via Hub
			// This requires access to the Hub, which we don't have here
			// We need to refactor this to work with the Hub
			
			l.logger.WithFields(logrus.Fields{
				"room_id": roomID,
				"count":   count,
				"msg_id":  likeBurstMsg.MessageID,
			}).Debug("Like burst aggregated")
		}
	}
}

// Stop stops the like aggregator.
func (l *LikeAggregator) Stop() {
	close(l.done)
}

// SetBroadcaster sets the broadcaster for the message processor.
func (m *MessageProcessor) SetBroadcaster(broadcaster port.Broadcaster) {
	m.broadcaster = broadcaster
}

// MockMessageProcessor provides a mock implementation for testing.
type MockMessageProcessor struct {
	logger *logrus.Logger
}

// NewMockMessageProcessor creates a new mock message processor.
func NewMockMessageProcessor(logger *logrus.Logger) port.MessageProcessor {
	return &MockMessageProcessor{
		logger: logger.WithField("component", "mock_message_processor"),
	}
}

// ProcessMessage processes a message (mock).
func (m *MockMessageProcessor) ProcessMessage(ctx context.Context, client *model.Client, message model.Message) error {
	m.logger.WithFields(logrus.Fields{
		"user_id":      client.UserID.String(),
		"room_id":      client.RoomID,
		"message_type": message.Type,
		"message_id":   message.MessageID,
	}).Debug("Mock message processed")
	return nil
}

// ProcessBarrage processes a barrage message (mock).
func (m *MockMessageProcessor) ProcessBarrage(ctx context.Context, client *model.Client, message model.Message) error {
	return m.ProcessMessage(ctx, client, message)
}

// ProcessLike processes a like message (mock).
func (m *MockMessageProcessor) ProcessLike(ctx context.Context, client *model.Client, message model.Message) error {
	return m.ProcessMessage(ctx, client, message)
}

// ProcessGift processes a gift message (mock).
func (m *MockMessageProcessor) ProcessGift(ctx context.Context, client *model.Client, message model.Message) error {
	return m.ProcessMessage(ctx, client, message)
}
